﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal struct RedBall
    {
        private readonly byte number;

        internal RedBall(int number)
        {
            if (number < 1 || number > 26) throw new GameEngineException($"Red ball must be from 1 to 26. {number} is not valid.");
            this.number = (byte)number;
        }

        internal RedBall(string number)
        {
            if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
            this.number = 0;
            switch (number.Length)
            {
                case 1:
                    if (number[0] >= '1' && number[0] <= '9')
                    {
                        this.number = (byte)(number[0] - '0');
                    }
                    break;
                case 2:
                    if (number[0] == '0' && number[1] >= '1' && number[1] <= '9')
                    {
                        this.number = (byte)(number[1] - '0');
                    }
                    else if (number[0] >= '0' && number[0] <= '2' && number[1] >= '0' && number[1] <= '9')
                    {
                        this.number = (byte)((number[0] - '0') * 10 + (number[1] - '0'));
                    }
                    break;
            }
            if (this.number == 0 || this.number > 26) throw new GameEngineException($"Red ball must be from 1 to 26. {number} is not valid.");
        }

        public static bool operator ==(RedBall obj1, RedBall obj2)
        {
            return obj1.number == obj2.number;
        }

        public static bool operator !=(RedBall obj1, RedBall obj2)
        {
            return obj1.number != obj2.number;
        }

        public override string ToString()
        {
            return number < 10 ? $"0{number}" : "" + number;
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        internal static RedBall GenerateOneRandom()
        {
            var random = new Random();
            int number = random.Next(1,27);
            return new RedBall(number);
        }

        internal int ToInt32()
        {
            return this.number;
        }
    }
}
