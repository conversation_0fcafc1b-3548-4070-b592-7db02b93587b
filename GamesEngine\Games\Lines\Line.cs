﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Logs;
using GamesEngine.Middleware;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Resources;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
	class Tier : Objeto
	{
		internal static Tier TIER_ONE = new Tier("T1");
		internal static Tier TIER_TWO = new Tier("T2");
		internal static Tier PROP = new Tier("PROP");

		public string Name { get;}

		private Tier(string name)
		{
			Name = name;
		}

	}

	public enum LineType 
	{ 
		MONEY_LINE = 1, 
		SPREAD_LINE = 2, 
		YES_NO_LINE = 3, 
		OVER_UNDER_LINE = 4, 
		FIXED_LINE = 5, 
		TOTAL_POINTS_LINE = 6, 
		MONEYDRAW_LINE = 7
	}

	[Puppet]
	abstract class Line : Objeto, ILog
	{
		private readonly int lineId;
		private readonly Shelve shelve;
		private readonly int index;
		private readonly int version;
		protected readonly Game game;
		private readonly Line previousVersion;
		private bool isLastVersion = false;
		private readonly Tier tier;
		private string text;
		private Log log;
		private GradingStatus grading;
		private GradingStatus gradingConfirmed;
		private bool gradingWasConfirmed;
		private readonly CheckPeriods checkedPeriods;
		private Visibility visibility;
		private Timestamp publishOn;
		private Period gradedOn;
		private readonly DateTime creationDate;
		private readonly DomainsActivation activations = new DomainsActivation();
		private AvailabilityTime dueDate;
		private AvailabilityTime publishDate;
		private bool isInMemory;
		private Question question;

		internal Line(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, string who, DateTime now)
		{
			if (lineId <= 0) throw new GameEngineException("Invalid line id, must be greater than zero");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (shelve == null) throw new ArgumentNullException(nameof(shelve));
			if (index < 0) throw new GameEngineException($"Id {nameof(index)} must be greater or equal than 0");
			if (game.IsGameOver()) throw new GameEngineException($"Game {game.ToString()} already ended, it does not allow changes");
			var checkedPeriods = new CheckPeriods(game.Sport);

			this.lineId = lineId;
			this.tier = tier;
			this.game = game;
			this.shelve = shelve;
			this.index = index;
			this.version = 0;
			this.isLastVersion = true;
			this.previousVersion = null;
			this.visibility = Visibility.DRAFT;
			this.grading.Init();
			this.gradingConfirmed.Init();
			this.gradingWasConfirmed = true;
			this.checkedPeriods = checkedPeriods;
			this.creationDate = now;
			this.dueDate = AvailabilityTime.UNLIMITED;
			this.publishDate = AvailabilityTime.UNLIMITED;
			this.isInMemory = true;
			this.question = question;

			LogLineCreation(who, now);
			shelve.Showcase.RiskAssestment.Initialize(this);
		}

		protected Line(Line previousVersion, string who, DateTime now)
		{
			if (previousVersion == null) throw new ArgumentNullException(nameof(previousVersion));
			if (!previousVersion.isLastVersion) throw new GameEngineException("There is an inconsistency because new line version must be based on the current active line version");
			if (previousVersion.game.IsGameOver()) throw new GameEngineException($"Game {game.ToString()} already ended, it does not allow changes");
			if (previousVersion.OriginalVersion.IsCanceled) throw new GameEngineException("Line was canceled, it can not have new versions");
			
			this.lineId = previousVersion.lineId;
			this.tier = previousVersion.tier;
			this.game = previousVersion.game;
			this.shelve = previousVersion.shelve;
			this.index = previousVersion.index;
			this.version = previousVersion.version + 1;
			this.isLastVersion = true;
			previousVersion.isLastVersion = false;
			this.previousVersion = previousVersion;
			this.text = previousVersion.text;
			this.visibility = previousVersion.visibility;
			this.dueDate = previousVersion.dueDate;
			previousVersion.dueDate = AvailabilityTime.EXPIRED;
			this.publishDate = previousVersion.publishDate;
			previousVersion.publishDate = AvailabilityTime.EXPIRED;
			if (previousVersion.HasBackground()) this.background = previousVersion.Background;

			this.grading.Init();
			this.gradingConfirmed.Init();
			this.OriginalVersion.gradingWasConfirmed = true;
			this.checkedPeriods = previousVersion.checkedPeriods;
			this.publishOn = previousVersion.publishOn;
			this.isInMemory = true;
			this.question = previousVersion.BasedOn;
			this.shelve.UpdateLine(this);

			CopyActivationsFrom(previousVersion.ActivationsInDomains);
			LogNewVersion(who, now);
		}

		protected string ReplacePlaceholders(string text, Game game)
		{
			int initial = text.IndexOf('{');
			if (initial == -1) return text;
			int final = text.IndexOf('}', initial);
			if(final == -1) return text;
			string placeholder = text.Substring(initial, final - initial + 1);
			string replaceWith = "";
			switch (placeholder.ToUpper())
			{
				case "{TEAMA}":
					replaceWith = game.TeamA.ShortName;
					break;
				case "{TEAMB}":
					replaceWith = game.TeamB.ShortName;
					break;
				case "{TEAM A}":
					replaceWith = game.TeamA.ShortName;
					break;
				case "{TEAM B}":
					replaceWith = game.TeamB.ShortName;
					break;
				case "{SPREAD}":
					replaceWith =  this.IsSpreadLine() ? (this as SpreadLine).Spread.ToString() : placeholder;
					break;
				case "{TOTALPOINTS}":
					replaceWith = this.IsTotalPointsLine() ? (this as TotalPointsLine).Score.ToString() : placeholder;
					break;
				case "{SCORE}":
					replaceWith = this.IsOverUnderLine() ? (this as OverUnderLine).Score.ToString() : placeholder;
					break;
				case "{PERIOD}":
					replaceWith = IsPublished ? this.publishOn.Period.Name : placeholder;
					break;
				default:
					replaceWith = placeholder;
					break;
			}

			StringBuilder result = new StringBuilder();
			if (initial > 0) result.Append(text.Substring(0, initial));
			result.Append(replaceWith);
			if (final < text.Length - 1) result.Append(text.Substring(final + 1));

			return ReplacePlaceholders(result.ToString(), game);
		}

		internal decimal ToWinIfPlayerWould(decimal risk, WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));
			ValidateWagerAnswer(answer);

			var coefficientReward = this.CoefficientReward(answer);
			decimal result = Wager.ToWin(risk, coefficientReward);
			return result;
		}

		private void LogLineCreation(string who, DateTime now)
        {
			log = new Log(lineId.ToString());
			log.AddEntry(now, who, $"{LineTypeAsString} was created by {who} at {now.ToString("MM/dd/yyyy hh:mm tt")}. Tier {tier.Name}.");
		}

		private void LogNewVersion(string who, DateTime now)
		{
			var log = OriginalVersion.Log;
			log.AddEntry(now, who, $"Rewards were changed by {who} at {now.ToString("MM/dd/yyyy hh:mm tt")}. Version {version.ToString()}.");
		}

		public void AddAnnotation(string message, string who, DateTime now)
		{
			if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
			if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));

			var log = OriginalVersion.log;
			log.AddEntry(now, who, $"{message} by {who} at {now.ToString("MM/dd/yyyy hh:mm tt")}.");
		}

		internal DateTime CreationDate
		{
			get
			{
				return OriginalVersion.creationDate;
			}
		}

		internal bool IsInMemory
		{
			get
			{
				return this.isInMemory;
			}
		}

		internal Question BasedOn
		{
			get
			{
				return this.question;
			}
		}

		protected abstract LineEvent GetChangedLineEvent(DateTime timestamp);

		protected abstract void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp);

		private void ValidateWagerAnswer(WagerAnswer answer)
		{
			switch (this)
			{
				case ABLine abL:
					if (this is MoneyDrawLine)
					{
						if (!(answer is ABAnswer) && !(answer is DrawAnswer)) throw new GameEngineException($"Answer {answer.GetType().Name} does not correspond to {this.GetType().Name}");
					}
					else 
					{ 
						if (!(answer is ABAnswer)) throw new GameEngineException($"Answer {answer.GetType().Name} does not correspond to {this.GetType().Name}");
					}
					break;
				case YesNoLine yNL:
					if (!(answer is YesNoAnswer)) throw new GameEngineException($"Answer {answer.GetType().Name} does not correspond to {this.GetType().Name}");
					break;
				case OverUnderLine oUL:
					if (!(answer is OverUnderAnswer)) throw new GameEngineException($"Answer {answer.GetType().Name} does not correspond to {this.GetType().Name}");
					break;
				case FixedLine fL:
					if (!(answer is FixedAnswer)) throw new GameEngineException($"Answer {answer.GetType().Name} does not correspond to {this.GetType().Name}");
					break;
				default:
					throw new GameEngineException("Line type is not handled");
			}
		}

		internal Wager PlaceBet(int authorizationId, Player player, DateTime nowChangeMeToBeOnWager, WagerAnswer answer, decimal amount)
		{
			if (authorizationId <= 0) throw new GameEngineException($"Invalid authorization id {authorizationId}");
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (answer == null) throw new ArgumentNullException(nameof(answer));
			if (amount <= 0) throw new GameEngineException("Amount must be greater than zero");
			if (!this.isLastVersion) throw new GameEngineException("This line is not the current active line");
			if (this.IsSuspended) throw new GameEngineException($"Line is suspended, therefore bet with authorization id {authorizationId} can not be placed");
			if (this.IsGraded() || this.IsNoAction() || this.IsRegraded()) throw new GameEngineException($"Line was already graded, therefore bet with authorization id {authorizationId} can not be placed");
			if(this.IsCanceled) throw new GameEngineException($"Line was canceled, it can not accept more bets");
			if (this.tier == Tier.TIER_ONE && !this.game.IsPregame()) throw new GameEngineException("You can not place a bet when line is Tier one and period is not pregame");
			ValidateWagerAnswer(answer);

			var result = new Wager(this, authorizationId, player, answer, amount, nowChangeMeToBeOnWager);
			this.shelve.AddWager(result);
			result.CalculateRisk(shelve.Showcase.RiskAssestment);
			if (Company.Book.RiskTags.Has(player))
			{
				result.nowChangeMeToBeOnWager = nowChangeMeToBeOnWager;
				result.NotifyRiskTag(nowChangeMeToBeOnWager);
			}
			return result;
		}

		internal void CalculateRisk(Wager wager, RiskAssestment toWin)
		{
			wager.CoefficientReward = this.CoefficientReward(wager.ChosenAnswer);
			toWin.Increment(this, wager);
		}

		internal abstract string LineTypeAsString { get; }

		protected abstract double CoefficientReward(WagerAnswer answer);

		internal abstract string RewardAsString(WagerAnswer answer);

		internal abstract void Serialize(KafkaMessage message);

		internal GradingStatus Grading
		{
			get
			{
				return this.OriginalVersion.grading.GradingStatus();
			}
		}

		internal bool IsGraded()
		{
			var result = this.OriginalVersion.grading.IsGraded();
			return result;
		}

		internal bool IsRegraded()
		{
			var result = this.OriginalVersion.grading.IsRegraded();
			return result;
		}

		internal bool IsNoAction()
		{
			var result = this.OriginalVersion.grading.IsNoAction();
			return result;
		}

		internal bool IsPending()
		{
			var result = this.OriginalVersion.grading.IsPending();
			return result;
		}

		internal bool IsOriginalVersion()
		{
			var result = this.version == 0;
			return result;
		}

		internal bool IsLastVersion()
		{
			return this.isLastVersion;
		}

		internal bool IsSpreadLine()
		{
			return this is SpreadLine;
		}

		internal bool IsMoneyLine()
		{
			return this is MoneyLine;
		}
		internal bool IsMoneyDrawLine()
		{
			return this is MoneyDrawLine;
		}


		internal bool IsTotalPointsLine()
		{
			return this is TotalPointsLine;
		}

		internal bool IsYesNoLine()
		{
			return this is YesNoLine;
		}

		internal bool IsOverUnderLine()
		{
			return this is OverUnderLine;
		}

		internal bool IsFixedLine()
		{
			return this is FixedLine;
		}

		internal void ChangeToGraded()
		{
			if (this.grading.IsGraded()) throw new GameEngineException($"Game can not be {nameof(ChangeToGraded)} because it is on {this.grading} status");
			if (!this.IsOriginalVersion()) throw new GameEngineException("Line could not be graded because it is not an original version");

			this.grading.ChangeToGraded();
			this.gradedOn = this.Game.CurrentPeriod;
			this.OriginalVersion.gradingWasConfirmed = false;
		}

		internal void ChangeToNoAction()
		{
			if (!this.IsOriginalVersion()) throw new GameEngineException("Line could not be graded because it is not an original version");

			this.grading.ChangeToNoAction();
			this.gradedOn = this.Game.CurrentPeriod;
			this.OriginalVersion.gradingWasConfirmed = false;
		}

		internal void ChangeToRegraded()
		{
			if (!this.grading.IsGraded() && !this.grading.IsNoAction()) throw new GameEngineException($"Game can not be {nameof(ChangeToRegraded)} because it is on {this.grading} status");
			if (!this.IsOriginalVersion()) throw new GameEngineException("Line could not be regraded because it is not an original version");

			this.grading.ChangeToRegraded();
			this.gradedOn = this.Game.CurrentPeriod;
			this.OriginalVersion.gradingWasConfirmed = false;
		}

		internal bool GradingWasConfirmed
		{
			get
			{
				return this.OriginalVersion.gradingWasConfirmed;
			}
			set
			{
				if (this.grading.IsPending()) throw new GameEngineException("Line could not be marked as confirmed because it is still pending");
				if (value == false) throw new GameEngineException("Confirmation can only be set through grading methods");
				if (gradedOn == null) throw new GameEngineException($"Line was not graded yet.");

				this.OriginalVersion.gradingWasConfirmed = true;
			}
		}

		internal abstract decimal Grade(Wager wager);

		internal static void ValidateSpread(double spread, Sport sport)
		{
			if (spread == 0) throw new GameEngineException("Spread Line can not be zero");
			if (spread < 0)  throw new GameEngineException("Spread values can not be negative");

			var residue = spread - Math.Truncate(spread);

			if (residue != 0 && residue != 0.5)
			{
				throw new GameEngineException($"Spread for tie scores can not be different than 1/2");
			}
		}

		internal static void ValidateReward(int teamAReward, int teamBReward)
		{
			ValidateReward(teamAReward);
			ValidateReward(teamBReward);
		}

		internal static void ValidateReward(int teamReward)
		{
			if (Math.Abs(teamReward) <= 100) throw new GameEngineException($"Team A reward {teamReward} must be greater or equal than 100");
		}

		internal void Suspend(bool itIsThePresent, DateTime now)
		{
			if (this.IsGraded() || this.IsNoAction() || this.IsRegraded()) throw new GameEngineException("Line is already graded, it can not be suspended");
			if (this.IsCanceled) throw new GameEngineException("Line is canceled, it can not be suspended");
			if (this.IsSuspended) throw new GameEngineException("This line is already suspended, it can not be suspended again");
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");
			//TODO que pasa si el BO es real time para las lineas

			this.visibility.Suspend();
			if (itIsThePresent)
			{
				var suspendedLineEvent = new SuspendedLineEvent(now, this);
				PlatformMonitor.GetInstance().WhenNewEvent(suspendedLineEvent);
			}

		}
		internal void Cancel(bool itIsThePresent, DateTime now)
		{
			if ( ! this.IsPending()) throw new GameEngineException("Line is already graded, regraded or noAction, it can not be canceled");
			if (this.IsCanceled) throw new GameEngineException("This line is already canceled, it can not be canceled again");
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");

			if (this.IsDraft)
			{
				this.shelve.RemoveLine(this);
			}
			else if (this.IsPublished || this.IsSuspended) 
			{
				if (!this.Showcase.Wagers.HasPurchases(this))
				{
					this.shelve.RemoveLine(this);
				}
				else
				{
					this.visibility.Cancel();
				}

				if (itIsThePresent)
				{
					var removedLineEvent = new CanceledLineEvent(now, this);
					PlatformMonitor.GetInstance().WhenNewEvent(removedLineEvent);
				}
			}
			else
			{
				throw new GameEngineException($"Unhandled visibility line");
			}
		}


		internal void Resume(bool itIsThePresent, DateTime now)
		{
			if (this.IsGraded() || this.IsNoAction() || this.IsRegraded()) throw new GameEngineException("Line is already graded, it can not be resumed");
			if (! this.IsSuspended) throw new GameEngineException("This line is not suspended, it can not be resumed");
			if (this.IsCanceled) throw new GameEngineException("This line is canceled, it can not be resumed");
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");
			if (!this.checkedPeriods.IsChecked(this.game.CurrentPeriod)) throw new GameEngineException("This line can not be resumed because current period was not marked as checked");
			//TODO Que pasa si regradea la linea

			this.visibility.Resume();
			if (itIsThePresent)
			{
				switch (this)
				{
					case SpreadLine sPL:
						var resumeSpreadLineEvent = new ResumeSpreadLineEvent(now, sPL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeSpreadLineEvent);
						break;
					case MoneyLine mL:
						var resumeMoneyLineEvent = new ResumeMoneyLineEvent(now, mL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeMoneyLineEvent);
						break;
					case MoneyDrawLine mDL:
						var resumeMoneyDrawLineEvent = new ResumeMoneyDrawLineEvent(now, mDL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeMoneyDrawLineEvent);
						break;
					case TotalPointsLine tPL:
						var resumeTotalPointsLineEvent = new ResumeTotalPointsLineEvent(now, tPL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeTotalPointsLineEvent);
						break;
					case YesNoLine yNL:
						var resumeYesNoLineEvent = new ResumeYesNoLineEvent(now, yNL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeYesNoLineEvent);
						break;
					case OverUnderLine oUL:
						var resumeOverUnderLineEvent = new ResumeOverUnderLineEvent(now, oUL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeOverUnderLineEvent);
						break;
					case FixedLine fL:
						var resumeFixedLineEvent = new ResumeFixedLineEvent(now, fL);
						PlatformMonitor.GetInstance().WhenNewEvent(resumeFixedLineEvent);
						break;
					default:
						throw new GameEngineException("Line type is not handled");
				}
			}
			
		}

		internal void Publish(bool itIsThePresent, DateTime now)
        {
			if (this.IsGraded() || this.IsNoAction()) throw new GameEngineException("Line is already graded, it can not be published");
			if (this.IsCanceled) throw new GameEngineException("Line is canceled, it can not be published");
			if (this.visibility.IsSuspended()) throw new GameEngineException("This line is suspended therefore it can not be suspended.");
			if (this.visibility.IsPublished()) throw new GameEngineException("This line is already published therefore it can not be published again.");
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");
			if (!this.checkedPeriods.IsChecked(this.game.CurrentPeriod)) throw new GameEngineException("This line can not be published because current period was not marked as checked");

			var thisIsTheFirstAddedLine = this.Showcase.IsFirstLineToBePublished();
			this.visibility.Publish();
			this.publishOn = new Timestamp(this.game.CurrentPeriod, now);
			this.text = ReplacePlaceholders(text, game);
			if (itIsThePresent && thisIsTheFirstAddedLine && this.IsPublished)
			{
				var firstAvailableLineEvent = new FirstAvailableLineEvent(this.CreationDate, this);
				PlatformMonitor.GetInstance().WhenNewEvent(firstAvailableLineEvent);
			}

			ForcePublishedLineEvent(itIsThePresent, now);
		}

		internal Period PublishedOn
		{
			get
			{
				if (this.visibility.IsDraft()) throw new GameEngineException($"Line was not published yet.");
				if (this.publishOn == null) throw new GameEngineException($"Line is already published but it does not have published on period.");
				
				return this.publishOn.Period;
			}
		}

		internal DateTime PublishedOnDateTime
		{
			get
			{
				return this.publishOn.Moment;
			}
		}

		internal Period GradedOn
		{
			get
			{
				if (this.IsPending()) throw new GameEngineException($"Line was not graded yet.");
				if (this.OriginalVersion.gradedOn == null) throw new GameEngineException($"Line was not graded yet.");

				return this.OriginalVersion.gradedOn;
			}
		}

		internal bool IsDraft
		{
			get
			{
				return this.visibility.IsDraft();
			}

		}

		internal bool IsSuspended
        {
            get
            {
				return this.visibility.IsSuspended();
			}
			
        }

		internal bool IsCanceled
		{
			get
			{
				return this.visibility.IsCanceled();
			}

		}

		internal bool IsPublished
		{
            get
            {
				return this.visibility.IsPublished();
			}
		}

		internal void AllowToPublishOn(Period period)
		{
			if (period == null) throw new ArgumentException(nameof(period));
			if (tier == Tier.TIER_ONE && period != game.Sport.OfficialPeriod(Timeline.PRE_GAME)) throw new GameEngineException("Tier One Line can only be published on pregame period.");
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");

			this.checkedPeriods.Check(period);
		}

		internal bool IsAllowedToPublishOn(Period period)
		{
			if (period == null) throw new ArgumentNullException(nameof(period));
			
			var result = this.checkedPeriods.IsChecked(period);
			return result;
		}

		internal void DenyPublished(Period period)
		{
			if (tier == Tier.TIER_ONE) throw new GameEngineException("Tier One Line can only be published on pregame period.");
			if (period == null) throw new ArgumentException(nameof(period));
			if (!this.isLastVersion) throw new GameEngineException($"You can not setup a line version that has a new version.");

			this.checkedPeriods.Uncheck(period);
		}

		internal IEnumerable<Period> ValidPeriodsToBePublished()
		{
			return this.checkedPeriods;
		}

		internal int Index
		{
			get
			{
				return this.index;
			}
		}

		internal Log Log
		{
			get
			{
				return OriginalVersion.log;
			}
		}

		internal Tier Tier
		{
			get
			{
				return this.tier;
			}
		}

		internal Game Game
		{
			get
			{
				return this.game;
			}
		}

		internal Showcase Showcase
		{
			get
			{
				return this.shelve.Showcase;
			}
		}

		internal Company Company
		{
			get
			{
				return this.shelve.Showcase.Betboard.Company;
			}
		}

		internal int Version
		{
			get
			{
				return this.version;
			}
		}

		internal int LineId
		{
			get
			{
				return this.lineId;
			}
		}

		internal Line OriginalVersion
		{
			get
			{
				if (this.previousVersion == null)
				{
					return this;
				}
				return this.previousVersion.OriginalVersion;
			}
		}

		internal Line PreviousVersion
		{
			get
			{
				if (this.previousVersion == null) throw new GameEngineException("This is an original line");
				
				return this.previousVersion;
			}
		}

		internal string Text
		{
			get
			{
				return this.text;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

				this.text = value;
			}
		}

		internal AvailabilityTime DueDate
		{
			get
			{
				return this.dueDate;
			}
			set
			{
				this.dueDate = value;
			}
		}

		internal AvailabilityTime PublishDate
		{
			get
			{
				if (!this.IsDraft) return AvailabilityTime.EXPIRED;

				return this.publishDate;
			}
			set
			{
				if (!this.IsDraft) throw new GameEngineException("Published date can not be modified on an already published line");
				this.publishDate = value;
			}
		}

		internal void UnlimitedDueDate()
		{
			this.dueDate = AvailabilityTime.UNLIMITED;
		}

		internal void UndefinedPublishDate()
		{
			this.publishDate = AvailabilityTime.UNLIMITED;
		}

		internal int RemainingAvailabilityTime(DateTime now)
		{
			var result = this.dueDate.RemainingTimeInSeconds(now);
			return result;
		}

		internal int RemainingTimeToPublish(DateTime now)
		{
			var result = this.publishDate.RemainingTimeInSeconds(now);
			return result;
		}

		internal int ShelveIndex => shelve.MyIndex();

		internal Visibility Visibility
		{
			get
			{
				return this.visibility;
			}
		}

		internal void MoveUp(DateTime now)
        {
			Showcase.MoveUp(shelve);
			if (IsPublished)
			{
				var movedUpLineEvent = new MoveUpLineEvent(now, this);
				PlatformMonitor.GetInstance().WhenNewEvent(movedUpLineEvent);
			}
		}

		internal void MoveDown(DateTime now)
		{
			Showcase.MoveDown(shelve);
			if (IsPublished)
			{
				var movedDownLineEvent = new MoveDownLineEvent(now, this);
				PlatformMonitor.GetInstance().WhenNewEvent(movedDownLineEvent);
			}
		}

		internal void IncludeDomainForActivations(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.IncludeDomain(domain, false);
		}

		internal bool IsEnabled(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var result = activations.IsEnabled(domain);
			return result;
		}

		internal DomainsActivation EnableDomain(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.EnableDomain(domain);
			return activations;
		}

		internal DomainsActivation DisableDomain(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.DisableDomain(domain);
			return activations;
		}

		internal void CopyActivationsFrom(DomainsActivation domainsActivation)
		{
			if (domainsActivation == null) throw new ArgumentNullException(nameof(domainsActivation));

			activations.CopyActivationsFrom(domainsActivation);
		}

		internal DomainsActivation ActivationsInDomains => activations;

		internal bool HasBackground()
		{
			return background != null;
		}

		ImageResource background;
		internal ImageResource Background
		{
			get
			{
				if (background == null) throw new ArgumentNullException(nameof(background));

				return background;
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));

				background = value;
			}
		}

		internal static int ConvertDecimalOddsToAmerican(decimal decimalOdd)
        {
			if (decimalOdd <= 1) throw new GameEngineException($"{nameof(decimalOdd)} '{decimalOdd}' is not valid.");

			var result = Convert.ToInt32(decimalOdd >= 2 ? (decimalOdd - 1) * 100 : -100 / (decimalOdd - 1));
			return result;
		}

		Aliases aliases;
		internal Aliases Aliases
		{
			get
			{
				if (aliases == null) aliases = new Aliases();
				return aliases;
			}
		}

		//TODO xxxx
		public override string ToString()
		{
			string result = "";
			if (game.Favorite.IsTeamA()) result = $"-{game.TeamA.Name} / +{game.TeamB.Name}";
			if (game.Favorite.IsTeamB()) result = $"+{game.TeamA.Name} / -{game.TeamB.Name}";
			if (game.Favorite.AreBoth()) result = $"-{game.TeamA.Name} / -{game.TeamB.Name}";
			return result;
		}
	}

	enum Visibility
	{
		DRAFT,
		SUSPENDED,
		PUBLISHED,
		CANCELED
	}

	static class VisibilityExtention
	{

		internal static void Cancel(this ref Visibility currentVisibility)
		{
			if (currentVisibility.IsDraft()) throw new GameEngineException($"A draft line can not be suspended because draft lines are not shown to players.");

			currentVisibility = Visibility.CANCELED;
		}
		internal static void Suspend(this ref Visibility currentVisibility)
		{
			if (currentVisibility.IsSuspended()) throw new GameEngineException($"Line is already suspended. It can not be suspended again.");
			if (currentVisibility.IsDraft()) throw new GameEngineException($"A draft line can not be suspended because draft lines are not shown to players.");

			currentVisibility = Visibility.SUSPENDED;
		}

		internal static void Resume(this ref Visibility currentVisibility)
		{
			if (currentVisibility.IsPublished()) throw new GameEngineException($"Line is already published. It can not be resume it again.");
			if (currentVisibility.IsDraft()) throw new GameEngineException($"A draft line can not be resume because it was not suspended.");

			currentVisibility = Visibility.PUBLISHED;
		}

		internal static void Publish(this ref Visibility currentVisibility)
		{
			if (currentVisibility.IsPublished()) throw new GameEngineException($"Line is already published. It can not be publish again.");
			if ( ! currentVisibility.IsDraft()) throw new GameEngineException($"Only draft line can be published.");

			currentVisibility = Visibility.PUBLISHED;
		}

		internal static bool IsSuspended(this Visibility currentVisibility)
		{
			var result = currentVisibility == Visibility.SUSPENDED;
			return result;
		}

		internal static bool IsCanceled(this Visibility currentVisibility)
		{
			var result = currentVisibility == Visibility.CANCELED;
			return result;
		}

		internal static bool IsDraft(this Visibility currentVisibility)
		{
			var result = currentVisibility == Visibility.DRAFT;
			return result;
		}

		internal static bool IsPublished(this Visibility currentVisibility)
		{
			var result = currentVisibility == Visibility.PUBLISHED;
			return result;
		}

	}
}

