﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero.processors.AuthTransaction;
using town.connectors.drivers.hades;
using static ExchangeAPI.Controllers.APIController;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;
namespace ExchangeAPI.Controllers
{
    public class WalletController: AuthorizeController
    {
		[HttpPost("api/wallet/drafts/deposit")]
		[Authorize(Roles = "c5,player")]
		public async Task<IActionResult> AddWalletDepositDraftAsync([FromBody] WalletDepositCreationBody body, [FromHeader(Name = "domain-url")] string domain)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
			string depositor = (body.Depositor == null) ? "" : body.Depositor;
			string voucher = (body.Voucher == null) ? "" : body.Voucher;
            string voucherUrl = (body.VoucherUrl == null) ? "" : body.VoucherUrl;
            int agentId = (int)Security.UserAgent(HttpContext);


            var accountCommand = string.IsNullOrWhiteSpace(body.AccountNumber) ?
					$"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
					$"account = customer.FindAccount('{body.AccountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.ToIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Deposit}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain).Deposit(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), '{employeeName}', '{depositor}', '{voucher}', '{voucherUrl}', '{body.Description}', processor, processorAccountId);
				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
				print company.Sales.CurrentStore.Id storeId;
				print domain.Id domainId;
			}}
			");

			if (!(result is OkObjectResult)) return result;

            var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var transactionResponse = JsonConvert.DeserializeObject<TransactionResponse>(json);



            bool itIsThePresent = true;
            DespositBody despositBody = new DespositBody(
                body.AccountNumber,
                body.ToIdentifier,
                body.Amount,
                $"{nameof(TransactionType.Deposit)}",
                DateTime.Now,
                string.Empty,
                employeeName,
                new GamesEngine.Domains.Domain(itIsThePresent, transactionResponse.DomainId, domain, Agents.TEST_BOOK),
                path,
                body.PaymentMethod.Value,
                body.EntityId,
                transactionResponse.StoreId,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                0,
                transactionResponse.AuthorizationId.ToString()
            );
			despositBody.SourceNumber = 17;//Rubicon: DEFAULT SOURCENUMBER
			despositBody.WithLock = true;
            DespositResponse despositResponse = await PaymentChannels.DepositAsync(itIsThePresent, agentId, domain, body.FromCurrencyCode, despositBody);
            if (despositResponse.Status != TransactionStatus.APPROVED)
            {
                return BadRequest($"Deposit draft with id {transactionResponse.AuthorizationId} could not be created. Status: {despositResponse.Status}");
            }

            if (despositResponse.AuthorizationId <= 0)
            {
                return BadRequest($"Deposit draft with id {transactionResponse.AuthorizationId} could not be created. AuthorizationId: {despositResponse.AuthorizationId}");
            }



            var resultAuth = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
            $@"
			{{
				transaction = marketplace.FindDraftTransaction({transactionResponse.AuthorizationId}, '{path}', '{employeeName}');
				transaction.AuthorizationId = {despositResponse.AuthorizationId};
			}}
			");

            if (!(resultAuth is OkObjectResult)) return resultAuth;

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchFor(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, body.ToCurrencyCode, PaymentMethod.Secrets, entityId: body.EntityId);
            ResponseInvoicePayment response = null;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
				recordSet.SetParameter("currency", body.FromCurrencyCode);
                recordSet.SetParameter("amount", body.Amount);
                recordSet.SetParameter("atAddress", body.ToIdentifier);
                recordSet.SetParameter("accountNumber", body.AccountNumber);
				recordSet.SetParameter("employeeName", employeeName);
				//recordSet.SetParameter("path", path);
                recordSet.SetParameter("description", body.Description);
				recordSet.SetParameter("entityId", body.EntityId);
				recordSet.SetParameter("domain", domain);
				recordSet.SetParameter("externalReference", transactionResponse.AuthorizationId);

                var executeResponse = await paymentProcessor.ExecuteAsync<ResponseInvoicePayment>(DateTime.Now, recordSet);

                if (executeResponse.Status == WholePaymentProcessor.PaymentProcessor.Status.Ok && executeResponse.Data != null)
                {
                    response = executeResponse.Data;
                }
                else
                {
                    return BadRequest($"Failed to execute deposit transaction: {executeResponse.ErrorMessage}");
                }
            }
            if (response == null || response.AuthorizationId <= 0) return BadRequest("Failed to create deposit transaction. Please check the input data and try again.");
			var walletResponse = new WalletTransactionResponse();
			walletResponse.AuthorizationId = transactionResponse.AuthorizationId;
            walletResponse.DestinationAddress = response.QrCodeResponse.Destination;
			walletResponse.PaymentLink = response.QrCodeResponse.PaymentLink;
			walletResponse.Amount = response.QrCodeResponse.Amount;
			walletResponse.Rate = response.QrCodeResponse.Rate;

            return Ok(walletResponse);
		}

		[HttpPost("api/wallet/drafts/withdrawal")]
		[Authorize(Roles = "c9,player")]
		public async Task<IActionResult> AddWalletWithdrawalDraftAsync([FromBody] WalletWithdrawalCreationBody body, [FromHeader(Name = "domain-url")] string domain)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.MinerFee < 0) return BadRequest($"{nameof(body.MinerFee)} must be greater or equal than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";

			string accountCommand = string.IsNullOrWhiteSpace(body.accountNumber) ?
				$"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
				$"account = customer.FindAccount('{body.accountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
                Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Withdrawal});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Withdrawal} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.FromIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});
				
				processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
				print transaction.Id transactionId;
				print account.Identificator account;
				print customer.AccountNumber customerNumber;
				print processorAccount.Id processorAccountId;
				print company.Sales.CurrentStore.Id storeId;
				print domain.AgentId agentId;
				print domain.Url domainUrl;
				print domain.Id domainId;
			}}
			");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);


            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
				tempTransactionResponse.CustomerNumber,
				body.Amount,
				body.FromCurrencyCode,
				body.Description,
				tempTransactionResponse.TransactionId.ToString(),
				tempTransactionResponse.Account,
				employeeName,
				tempTransactionResponse.StoreId,
                useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                body.PaymentMethod.Value,
                body.EntityId
            );

			if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
			{
				await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
				}}
				");
			}

			var feeScript = body.MinerFee == 0 ? $"NoFee('{body.FromCurrencyCode}')" : $"MinerFee(Currency('{body.FromCurrencyCode}',{body.MinerFee}))";
			string realAccount = body.RealAccount;
			var errorMessage = "Sorry, your withdrawal cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
			result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Withdraw(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, '{employeeName}', '{realAccount}', '{body.Description}', {feeScript}, processor, processorAccountId);
					
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			o = (OkObjectResult)result;
			json = o.Value.ToString();
			var idResponse = JsonConvert.DeserializeObject<WalletTransactionResponse>(json);

			return Ok(idResponse);
		}

        [HttpPost("api/wallet/drafts/transfer")]
        [Authorize(Roles = "c13,player")]
        public async Task<IActionResult> AddTransferDraftAsync([FromBody] WalletTransferCreationBody body, [FromHeader(Name = "domain-url")] string domain)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
            //if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.RealAccount)) body.RealAccount = "";
            if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string batchPath = path + '/' + employeeName;

            string accountCommand = string.IsNullOrWhiteSpace(body.FromAccountNumber) ?
                $"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
                $"account = customer.FindAccount('{body.FromAccountNumber}');";

            var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					existPaymentProcessor = company.System.DriverManagers.ExistPaymentProcessor({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.EntityId}, {body.PaymentMethod.Value});
					Check(existPaymentProcessor) Error 'Payment processor does not exist';

					existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
					Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{body.FromIdentifier}');
						Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
					}}

					domain = company.Sales.DomainFrom('{domain}');
					isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Transfer});
					Check(isTransactionAllowed) Error 'Transaction {TransactionType.Transfer} is not enabled';
					existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';

					existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existsBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(agentBatch.BatchTransactions.HasAvailable(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent batch {batchPath} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}

					existsAgent = marketplace.ExistsAgent('{path}');
					Check(existsAgent) Error 'Agent {path} does not exist.';
					if (existsAgent)
					{{
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{employeeName}');
						Check(user.IsInRange(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent {path} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					{accountCommand}
					Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
					print transaction.Id transactionId;
					print account.Identificator account;
					processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
					print processorAccount.Id processorAccountId;
					print customer.AccountNumber customerNumber;
					print company.Sales.CurrentStore.Id storeId;
					print domain.AgentId agentId;
					print domain.Url domainUrl;
					print domain.Id domainId;
					print transaction.Processor.PaymentMethodAsString paymentMethodType;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            TempTransactionResponse tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
                tempTransactionResponse.CustomerNumber,
                body.Amount,
                body.FromCurrencyCode,
                body.Description,
                tempTransactionResponse.TransactionId.ToString(),
                tempTransactionResponse.Account,
                employeeName,
                tempTransactionResponse.StoreId,
				useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                tempTransactionResponse.PaymentMethodType.Value,
                body.EntityId
            );

            if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
            {
                await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
					}}
					");
            }

            accountCommand = string.IsNullOrWhiteSpace(body.ToAccountNumber) ?
                $"toAccount = targetCustomer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
                $"toAccount = targetCustomer.FindAccount('{body.ToAccountNumber}');";

            var errorMessage = "Sorry, your transfer cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
            result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{	
					targetCustomer = company.CustomerByIdentifier('{body.ToIdentifier}');
					{accountCommand}
					
					domain = company.Sales.DomainFrom('{domain}');

					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').TransferTo(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, toAccount, '{employeeName}', '{body.RealAccount}', '{body.Description}', processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            o = (OkObjectResult)result;
            json = o.Value.ToString();
            DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

            return Ok(idResponse);
        }

        [DataContract(Name = "WalletDepositCreationBody")]
		public class WalletDepositCreationBody
		{
			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "toIdentifier")]
			public string ToIdentifier { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "voucher")]
			public string Voucher { get; set; }
			[DataMember(Name = "voucherurl")]
			public string VoucherUrl { get; set; }
			[DataMember(Name = "depositor")]
			public string Depositor { get; set; }

			[DataMember(Name = "sendersName")]
			public string SendersName { get; set; }
			[DataMember(Name = "country")]
			public string Country { get; set; }
			[DataMember(Name = "state")]
			public string State { get; set; }
			[DataMember(Name = "city")]
			public string City { get; set; }
			[DataMember(Name = "controlNum")]
			public string ControlNum { get; set; }
			[DataMember(Name = "providerId")]
			public int ProviderId { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "IdWalletDepositResponse")]
		public class WalletTransactionResponse
		{
			[DataMember(Name = "authorizationId")]
			public long AuthorizationId { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "now")]
			public string Now { get; set; }

            [DataMember(Name = "destinationAddress")]
            public string DestinationAddress { get; set; }


            [DataMember(Name = "paymentLink")]
            public string PaymentLink { get; set; }
            [DataMember(Name = "amount")]
            public string Amount { get; internal set; }
            [DataMember(Name = "rate")]
            public string Rate { get; internal set; }
        }


        [DataContract(Name = "TransactionResponse")]
        public class TransactionResponse
        {
            [DataMember(Name = "authorizationId")]
            public int AuthorizationId { get; set; }
            [DataMember(Name = "batchNumber")]
            public int BatchNumber { get; set; }
            [DataMember(Name = "now")]
            public string Now { get; set; }

            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }

            [DataMember(Name = "storeId")]
            public int StoreId { get; set; }

        }

        [DataContract(Name = "WalletWithdrawalCreationBody")]
		public class WalletWithdrawalCreationBody
		{
			[DataMember(Name = "accountNumber")]
			public string accountNumber { get; set; }

			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "fromIdentifier")]
			public string FromIdentifier { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "realAccount")]
			public string RealAccount { get; set; }
			[DataMember(Name = "minerFee")]
			public decimal MinerFee { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

        [DataContract(Name = "WalletTransferCreationBody")]
        public class WalletTransferCreationBody
        {

            [DataMember(Name = "toIdentifier")]
            public string ToIdentifier { get; set; }

            [DataMember(Name = "fromIdentifier")]
            public string FromIdentifier { get; set; }

            [DataMember(Name = "fromCurrencyCode")]
            public string FromCurrencyCode { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "realAccount")]
            public string RealAccount { get; set; }

            [DataMember(Name = "fromAccountNumber")]
            public string FromAccountNumber { get; set; }

            [DataMember(Name = "toAccountNumber")]
            public string ToAccountNumber { get; set; }
            [DataMember(Name = "paymentMethod")]
            public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
        }
    }
}
