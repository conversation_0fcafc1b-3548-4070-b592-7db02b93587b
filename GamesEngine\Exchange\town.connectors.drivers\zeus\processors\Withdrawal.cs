﻿using Connectors.town.connectors.driver.transactions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.zeus;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.zeus
{
    public class Withdrawal : DGSProcessorDriver
    {
        private RestClient _postWithdrawClient;

        public Withdrawal()
            : base(TransactionType.Deposit)
        {
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (_postWithdrawClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postWithdrawClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        _ = Task.Run(async () =>
                        {
                            await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                        });
                    }

                    _postWithdrawClient = new RestClient(ServicesUrl);
                }
            }

            var authorizationNumber = DeductAmount(now, recordSet);

            var result = new WithdrawalTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }
        private int DeductAmount(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (string.IsNullOrWhiteSpace(AppToken)) throw new Exception($"{nameof(AppToken)} is empty");

            string customerId = recordSet.Mappings["customerId"].AsString;
            decimal amount = recordSet.Mappings["amount"].AsDecimal;
            string description = recordSet.Mappings["description"].AsString;
            string referenceID = recordSet.Mappings["referenceID"].AsString;
            var httpContext = recordSet.Mappings["httpContext"].As<HttpContext>();

            if (string.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (string.IsNullOrWhiteSpace(referenceID)) throw new ArgumentNullException(nameof(referenceID));
            if (referenceID.Length >= 31) throw new Exception($"Reference {referenceID} its longer than 31 characters.");
            if (amount <= 0) throw new Exception($"{nameof(amount)} {amount} is not valid to send request");

            StringValues tokenValue;
            httpContext.Request.Headers.TryGetValue(PLAYER_TOKEN_NAME, out tokenValue);
            var tokenToUse = StringValues.IsNullOrEmpty(tokenValue) ? SUBSTITUTE_PLAYER_TOKEN : tokenValue.ToString();

            var values = new CreditAndDebitTransactionBody()
            {
                idplayer = customerId,
                amount = amount,
                description = description,
                token = tokenToUse
            };
            const string url = "/InsertDebitTransaction";
            var jsonString = Commons.ToJson(values);

            string responseString = "";
            string valuesWithHiddenFields = $@"idPlayer:{customerId}, description:{description}, reference:{referenceID}, amount:{amount}";
            var authorizationNumber = FAKE_DOCUMENT_NUMBER;
            try
            {
                Loggers.GetIntance().AccountingServicesWithdraw.Debug($@"url:{url} data:{valuesWithHiddenFields}");

                var request = new RestRequest(url, Method.Post);
                request.AddHeader("Authorization", $"Bearer {AppToken}");
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var response = _postWithdrawClient.Execute(request);
                responseString = response.Content;
                Loggers.GetIntance().AccountingServicesWithdraw.Debug($@"response:{responseString}");
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingServicesWithdraw.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
                InternalOnError(nameof(DeductAmount), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);

                return FAKE_DOCUMENT_NUMBER;
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(DeductAmount), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
            }
            else
            {
                var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
                if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                {
                    Loggers.GetIntance().AccountingServicesWithdraw.Debug($"{nameof(DeductAmount)}\nUrl:{url}\nResponse: {responseString}");
                }
                else
                {
                    try
                    {
                        var responseObj = Commons.FromJson<CreditAndDebitTransactionResponse>(responseString);
                        authorizationNumber = responseObj.transactionId;

                        if (authorizationNumber <= 0)
                        {
                            NotifyWarn(nameof(DeductAmount), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(DeductAmount)} has an invalid {nameof(authorizationNumber)} '{authorizationNumber}' for {customerId} ref {referenceID}");
                        }
                    }
                    catch (Exception e)
                    {
                        NotifyWarn(nameof(DeductAmount), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                    }
                }
            }
            return authorizationNumber;
        }
        public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("referenceID");
            CustomSettings.AddVariableParameter("httpContext");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
