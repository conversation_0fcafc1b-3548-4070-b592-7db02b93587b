﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
	class HomeTeam
	{

		private readonly Game game;
		private TeamSelection selection;
		internal enum TeamSelection { Home = 1, Visitor = 2, None = 3};
		internal readonly static HomeTeam NONE = new HomeTeam();

		private HomeTeam(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			this.game = game;
		}

		private HomeTeam()
		{
			this.selection = TeamSelection.None;
		}

		internal HomeTeam Team(Game game, Team team)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (team == null) throw new ArgumentNullException(nameof(team));
			if (game.TeamA != team && game.TeamB != team) throw new GameEngineException($"Home team does not belong to match {game.ToString()}");

			var result = new HomeTeam(game);
			result.selection = game.TeamA == team ? TeamSelection.Home : TeamSelection.Visitor;

			return result;
		}

		internal Team Home
		{
			get
			{
				if (this.IsTeamA())
				{
					return this.game.TeamA;
				}
				else
				{
					return this.game.TeamB;
				}
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));
				var team = value;
				if (this.game.TeamA != team && this.game.TeamB != team) throw new GameEngineException($"Home team does not belong to match {game.ToString()}");

				var result = new HomeTeam(this.game);
				result.selection = TeamSelection.Home;
			}
		}

		internal Team Visitor
		{
			get
			{
				if (this.IsTeamB())
				{
					return this.game.TeamB;
				}
				else
				{
					return this.game.TeamA;
				}
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));
				var team = value;
				if (game.TeamA != team && game.TeamB != team) throw new GameEngineException($"Home team does not belong to match {game.ToString()}");

				var result = new HomeTeam(game);
				result.selection = TeamSelection.Visitor;
			}
		}

		internal bool IsTeamA()
		{
			return this.selection == TeamSelection.Home;
		}

		internal bool IsTeamB()
		{
			return this.selection == TeamSelection.Visitor;
		}

	}
}
