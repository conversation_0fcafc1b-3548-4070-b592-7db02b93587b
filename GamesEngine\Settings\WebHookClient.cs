﻿using GamesEngine.Business;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using town.connectors;

namespace GamesEngine.Settings
{
    public class WebHookClient
    {
        private static WebHookClient instance;
        private HttpClient clientWebHook;
        internal static string BASE_ADDRESS { get; private set; }
        internal static bool USE_WEBHOOK { get; private set; }

        private WebHookClient()
        {
            if (USE_WEBHOOK == true)
            {
                clientWebHook = new HttpClient();
                clientWebHook.BaseAddress = new Uri(BASE_ADDRESS);
            }
        }

        public static void Configure(IConfiguration Configuration)
        {
            var webHookConfiguration = Configuration.GetSection("WebHookConfiguration");
            if (string.IsNullOrEmpty(webHookConfiguration.GetSection("useWebHook").Value)) throw new Exception("Add to useWebHook to the app settings.");
            if (bool.Parse(webHookConfiguration.GetSection("useWebHook").Value))
            {
                if (string.IsNullOrEmpty(webHookConfiguration.GetSection("baseAddress").Value)) throw new Exception("Add to url to the app settings.");
                BASE_ADDRESS = webHookConfiguration.GetSection("baseAddress").Value;
            }

            USE_WEBHOOK = bool.Parse(webHookConfiguration.GetSection("useWebHook").Value);
            
            if (instance == null) instance = new WebHookClient();

        }

        internal static bool IsConfigured => USE_WEBHOOK && !string.IsNullOrEmpty(BASE_ADDRESS);

        private class WebHookMsgBody
        {
            public string EventType { get; set; }
            public string Channel { get; set; }
            public Dictionary<string, object> Payload { get; set; }

        }

        internal static WebHookClient Instance
        {
            get
            {
                if (instance == null) instance = new WebHookClient();
                return instance;
            }
        }

        internal struct ResultWebHook
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }



        internal async Task<ResultWebHook> SendWebHookAsync(DateTime now, CustomSettings.RecordSet recordSet, string eventType, string channel)
        {
            if (recordSet == null) throw new ArgumentNullException(nameof(recordSet));
            if (string.IsNullOrEmpty(eventType)) throw new ArgumentNullException(nameof(eventType));
            if (string.IsNullOrEmpty(channel)) throw new ArgumentNullException(nameof(channel));

            try
            {
                Loggers.GetIntance().WebHook.Debug($"Send WebHook {now}");
                var filteredRecordSet = recordSet.CustomSettingsForExternal();

                WebHookMsgBody body = new WebHookMsgBody
                {
                    EventType = eventType,
                    Channel = channel,
                    Payload = filteredRecordSet
                };

                var jsonBody = JsonConvert.SerializeObject(body);
                using var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

                HttpResponseMessage result = await clientWebHook.PostAsync($"/api/broadcastWebhook", content);

                Loggers.GetIntance().WebHook.Debug($"Message WebHook sended {now}");
                if (result.IsSuccessStatusCode)
                {
                    return new ResultWebHook { Success = true, Message = "WebHook sent successfully" };
                }
                else
                {
                    Loggers.GetIntance().WebHook.Debug($"Message WebHook not OkObjectResult  {now} ");
                    return new ResultWebHook { Success = false, Message = $"WebHook failed" };
                }

            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Error Sending message inside WebHook", ex);
                return new ResultWebHook { Success = false, Message = $"Error serializando objeto: {ex.Message}" };
            }
        }

        internal ResultWebHook SendWebHook(DateTime now, CustomSettings.RecordSet recordSet, string eventType, string channel)
        {
            if (recordSet == null) throw new ArgumentNullException(nameof(recordSet));
            if (string.IsNullOrEmpty(eventType)) throw new ArgumentNullException(nameof(eventType));
            if (string.IsNullOrEmpty(channel)) throw new ArgumentNullException(nameof(channel));

            try
            {
                Loggers.GetIntance().WebHook.Debug($"Send WebHook {now}");
                var filteredRecordSet = recordSet.CustomSettingsForExternal();

                WebhookPayloadRequest body = new WebhookPayloadRequest
                {
                    EventType = eventType,
                    Channel = channel,
                    Payload = filteredRecordSet
                };


                //string dataJson = JsonConvert.SerializeObject(body, Formatting.Indented);
                HttpRequestMessage httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, $"/api/broadcastWebhook");
                httpRequestMessage.Content = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
                HttpResponseMessage result = clientWebHook.Send(httpRequestMessage);

                Loggers.GetIntance().WebHook.Debug($"Message WebHook sended {now}");
                if (result.IsSuccessStatusCode)
                {
                    return new ResultWebHook { Success = true, Message = "WebHook sent successfully" };
                }
                else
                {
                    Loggers.GetIntance().WebHook.Debug($"Message WebHook not OkObjectResult  {now} ");
                    return new ResultWebHook { Success = false, Message = $"WebHook failed" };
                }

            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Error Sending message inside WebHook", ex);
                return new ResultWebHook { Success = false, Message = $"Error serializando objeto: {ex.Message}" };
            }
        }


        internal async Task<ResultWebHook> SendWebHookAsync(DateTime now, Dictionary<string,object> bodyRequest, string eventType, string channel)
        {
            if (bodyRequest == null) throw new ArgumentNullException(nameof(bodyRequest));
            if (string.IsNullOrEmpty(eventType)) throw new ArgumentNullException(nameof(eventType));
            if (string.IsNullOrEmpty(channel)) throw new ArgumentNullException(nameof(channel));

            try
            {
                Loggers.GetIntance().WebHook.Debug($"Send WebHook {now}");
             

                WebHookMsgBody body = new WebHookMsgBody
                {
                    EventType = eventType,
                    Channel = channel,
                    Payload = bodyRequest
                };

                var jsonBody = JsonConvert.SerializeObject(body);
                using var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

                HttpResponseMessage result = await clientWebHook.PostAsync($"/api/broadcastWebhook", content);

                Loggers.GetIntance().WebHook.Debug($"Message WebHook sended {now}");
                if (result.IsSuccessStatusCode)
                {
                    return new ResultWebHook { Success = true, Message = "WebHook sent successfully" };
                }
                else
                {
                    Loggers.GetIntance().WebHook.Debug($"Message WebHook not OkObjectResult  {now} ");
                    return new ResultWebHook { Success = false, Message = $"WebHook failed" };
                }

            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Error Sending message inside WebHook", ex);
                return new ResultWebHook { Success = false, Message = $"Error serializando objeto: {ex.Message}" };
            }
        }

    }
}
