﻿using GamesEngine.Exchange.Batch;
using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
	internal class SaleTransaction : Transaction
	{
		internal const int TEMPLATE_ID = 5;

		internal SaleTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, CustomerAccount account, string employeeName, Currency amount, DateTime now)
	: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Sale)
		{
			transactionDefinition.Batch.AddTransactionDenifition(this);
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted)
		{
			
		}

		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}
	}
	
}
