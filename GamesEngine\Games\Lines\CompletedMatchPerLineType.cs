﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedMatchPerLineType: Objeto
    {
        internal LineType LineType { get; }
        internal int DomainId { get; }
        internal int GameId => game.GameId;
        internal int TournamentId => game.TournamentId;
        internal string TeamAName => game.TeamAName;
        internal string TeamBName => game.TeamBName;
        internal string SportName => game.SportName;
        internal string LeagueName => game.LeagueName;
        internal DateTime StartDate => game.StartDate;
        readonly List<CompletedWager> wagers = new List<CompletedWager>();
        readonly CompletedMatchesPerLineTypes matchesPerLineTypes;
        readonly CompletedScoreGame game;

        public CompletedMatchPerLineType(CompletedMatchesPerLineTypes matchesPerLineTypes, CompletedScoreGame game, LineType type, int domainId)
        {
            if (matchesPerLineTypes == null) throw new ArgumentNullException(nameof(matchesPerLineTypes));
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater than zero");

            this.matchesPerLineTypes = matchesPerLineTypes;
            this.game = game;
            LineType = type;
            DomainId = domainId;
        }

        internal void Add(IEnumerable<CompletedWager> paramWagers)
        {
            if (!paramWagers.Any()) throw new GameEngineException($"{nameof(paramWagers)} cannot be empty");

            foreach (var wager in paramWagers)
            {
                Add(wager);
            }
        }

        internal void Add(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (wager.Game != game || wager.Line.LineType != LineType || wager.DomainId != DomainId) throw new GameEngineException($"{nameof(CompletedWager)} belong to different {nameof(LineType)} '{LineType}' and {nameof(DomainId)} '{DomainId}'");

            wagers.Add(wager);

            if (wager.IsWinner()) TotalWinners++;
            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
            matchesPerLineTypes.UpdateTotals(wager);
        }

        internal int TotalWinners { get; private set; }

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
