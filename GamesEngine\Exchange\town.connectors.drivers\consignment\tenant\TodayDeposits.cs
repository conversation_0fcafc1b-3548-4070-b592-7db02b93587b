﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class TodayDeposits : ConsignmentTenantDriver
    {
        public TodayDeposits() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetDepositsAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<DepositsResponse> GetDepositsAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseDepositsEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Get_deposits";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetDeposits.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetDeposits.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseDepositsEnvelope));
                responseXml = (ResponseDepositsEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetDeposits.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetDepositsAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.Get_depositsResponse.Get_depositsResult))
            {
                NotifyWarn(nameof(GetDepositsAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                XmlSerializer serializer = new XmlSerializer(typeof(DepositsResponse));
                StringReader reader = new StringReader(responseXml.Body.Get_depositsResponse.Get_depositsResult);
                var result = (DepositsResponse)serializer.Deserialize(reader);
                return result;
            }

            return new DepositsResponse();
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var status = recordSet.Mappings["status"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Get_deposits xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<status>").Append(status).AppendLine("</status>");
            xmlBuilder.AppendLine("</Get_deposits>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Get_deposits xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<status>").Append(status).AppendLine("</status>");
            xmlBuilderWithHiddenFields.AppendLine("</Get_deposits>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseDepositsEnvelope
        {
            public ResponseDepositsBody Body { get; set; }
        }

        public class ResponseDepositsBody
        {
            [XmlElement(ElementName = "Get_depositsResponse", Namespace = "http://tempuri.org/")]
            public GetDepositsResponse Get_depositsResponse { get; set; }
        }

        public class GetDepositsResponse
        {
            public string Get_depositsResult { get; set; }
        }

        [XmlRoot("xDeposits")]
        public class DepositsResponse
        {
            [XmlElement("Deposit")]
            public List<DepositResponse> Deposits { get; set; }
        }

        [XmlRoot("Deposit")]
        public class DepositResponse
        {
            [XmlAttribute("Id")]
            public string Id { get; set; }
            [XmlAttribute("MerchId")]
            public string MerchId { get; set; }
            [XmlAttribute("SendersName")]
            public string SendersName { get; set; }
            [XmlAttribute("Country")]
            public string Country { get; set; }
            [XmlAttribute("State")]
            public string State { get; set; }
            [XmlAttribute("City")]
            public string City { get; set; }
            [XmlAttribute("CreatedDate")]
            public string CreatedDate { get; set; }
            [XmlAttribute("ReceiversName")]
            public string ReceiversName { get; set; }
            [XmlAttribute("StatusId")]
            public string StatusId { get; set; }
            [XmlAttribute("StatusName")]
            public string StatusName { get; set; }
            [XmlAttribute("ControlNum")]
            public string ControlNum { get; set; }
            [XmlAttribute("Amount")]
            public string Amount { get; set; }
            [XmlAttribute("Notes")]
            public string Notes { get; set; }
            [XmlAttribute("Reference")]
            public string Reference { get; set; }
            [XmlAttribute("ConfirmedDate")]
            public string ConfirmedDate { get; set; }
            [XmlAttribute("Provider")]
            public string Provider { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("status");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
