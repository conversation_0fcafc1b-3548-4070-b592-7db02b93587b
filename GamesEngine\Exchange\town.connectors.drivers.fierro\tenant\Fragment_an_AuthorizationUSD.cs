﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Diagnostics;
using System.Net;
using System.Threading.Tasks;
using town.connectors.commons;

namespace town.connectors.drivers.fiero
{
	public class Fragment_an_AuthorizationUSD : FieroTenantDriver, IDriverUserProperties
    {
        public string CashierUrl { get; private set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private RestClient _postClient;

        public Fragment_an_AuthorizationUSD() : base(Tenant_Actions.Fragment, TransactionType.CreateFragment, "USD")
		{
		}

		public override void Prepare(DateTime now)
		{
            CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("authorizationNumber");
			CustomSettings.AddVariableParameter("fragments");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("currencyCode");
            CustomSettings.AddVariableParameter("total");
            CustomSettings.AddVariableParameter("useless");
            CustomSettings.AddVariableParameter("date");
            CustomSettings.AddVariableParameter("processorKey");
            CustomSettings.AddVariableParameter("allRisksAreEquals");

            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;

            //CustomSettings.Prepare();
        }

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
            string customerId = recordSet.Mappings["atAddress"].AsString;
            string currencyCode = recordSet.Mappings["currencyCode"].AsString;
            string processorKey = recordSet.Mappings["processorKey"].AsString;
            int ticketNumber = recordSet.Mappings["authorizationNumber"].AsInt;
            int storeId = recordSet.Mappings["storeId"].AsInt;
            decimal total = recordSet.Mappings["total"].AsDecimal;
            DateTime useless = recordSet.Mappings["useless"].AsDateTime;
            DateTime date = recordSet.Mappings["date"].AsDateTime;
            bool allRisksAreEquals = recordSet.Mappings["allRisksAreEquals"].AsBool;
            Fragment[] fragments = recordSet.Mappings["fragments"].As<Fragment[]>();

            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (fragments == null) throw new ArgumentNullException(nameof(fragments));
            if (fragments.Length == 0) throw new Exception($"At least one wager is required to send request {nameof(FragmentAuthorization)} TicketNumber: {ticketNumber} CustomerId: {customerId} on RegisterWagers");

            Debug.WriteLine($"Accounting service {nameof(FragmentAuthorization)} received {nameof(customerId)}:{customerId} {nameof(ticketNumber)}:{ticketNumber} {nameof(fragments)}:{fragments}");


            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = FragmentAnAuthorizationAppendedInsider.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(FragmentAnAuthorizationAppendedInsider.CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (FragmentAnAuthorizationAppendedInsider.CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) FragmentAnAuthorizationAppendedInsider.CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);


            if (_postClient == null)
            {
                _postClient = new RestClient(CashierUrl);
            }

            //DepositThenLock
            FragmentsCreationBodyDTO fragmentsCreationBody = new FragmentsCreationBodyDTO()
            {
                AtAddress = customerId,
                Fragments = fragments,
                StoreId = storeId,
                Total = total,
                CurrencyCode = currencyCode,
                Useless = useless,
                Date = date,
                AllRisksAreEquals = allRisksAreEquals,
                ProcessorKey = processorKey
            };

            var isMultipleAuthorization = ticketNumber == Authorization.FAKE_TICKET_NUMBER;
            if (isMultipleAuthorization)
            {
                DepositForMultiAuthorization(fragmentsCreationBody);
            }
            else
            {
                fragmentsCreationBody.AuthorizationNumber = ticketNumber;
                DepositSingleAuthorization(fragmentsCreationBody);
            }


                //FragmentAuthorizationAsync
            var result = FragmentAuthorization(customerId, ticketNumber, fragments);
            return (T)Convert.ChangeType(result, typeof(T));
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }

        
        private void DepositForMultiAuthorization(FragmentsCreationBodyDTO fragmentsCreation)
		{
            try
            {
                var url = $"api/fragment/multiple/depositThenLock";
                var jsonString = Commons.ToJson(fragmentsCreation);

               
                string responseString = "";

                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {FragmentAnAuthorizationAppendedInsider.CashierToken.access_token}");
                }
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var resultFromCashier = _postClient.Execute(request);
                if (resultFromCashier.StatusCode != HttpStatusCode.OK)
                {
                    string body = string.Empty;
                    ErrorsSender.Send(body, $@"DepositLock Fragment to {fragmentsCreation.AtAddress} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }
        }

        private void DepositSingleAuthorization(FragmentsCreationBodyDTO fragmentsCreation)
        {
            try
            {
                var url = $"api/single/depositThenLock";

               
                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {FragmentAnAuthorizationAppendedInsider.CashierToken.access_token}");
                }
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", fragmentsCreation, ParameterType.RequestBody);

                var resultFromCashier = _postClient.Execute(request);
                if (resultFromCashier.StatusCode != HttpStatusCode.OK)
                {
                    string body = string.Empty;
                    ErrorsSender.Send(body, $@"DepositLock Fragment to {fragmentsCreation.AtAddress} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }
        }

        private PostFreeFormWagerCollectionSuccessResponse FragmentAuthorization(string atAddress, int authorizationNumber, Fragment[] fragments)
		{
            var frgamentBody = new FragmentAuthorizationBody()
            {
                AtAddress = atAddress,
                AuthorizationNumber = authorizationNumber,
                Fragments = fragments
            };

            PostFreeFormWagerCollectionSuccessResponse responseResult = new PostFreeFormWagerCollectionSuccessResponse();
            try
            {
                var jsonString = Commons.ToJson(frgamentBody);
                //var url = $"{CashierUrl}api/fragments";
                var url = $"api/fragments";

                string responseString = "";
              
                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured()) request.AddHeader("Authorization", $"Bearer {FragmentAnAuthorizationAppendedInsider.CashierToken.access_token}");

                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var response = _postClient.Execute(request);
                responseString = response.Content;
                string body = string.Empty;
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    responseResult = JsonConvert.DeserializeObject<PostFreeFormWagerCollectionSuccessResponse>(responseString);
                }
                else
                {
                    responseResult.Error = responseString;
                    responseResult.Wagers = new PostFreeFormWager[0];
                    ErrorsSender.Send(body, $@"Create Fragment to {frgamentBody.AtAddress} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }

            return responseResult;
        }
    }
}
