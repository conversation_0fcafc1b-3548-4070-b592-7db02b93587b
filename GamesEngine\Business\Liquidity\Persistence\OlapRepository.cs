﻿using ClickHouse.Client.ADO;
using ClickHouse.Client.ADO.Parameters;
using ClickHouse.Client.Copy;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using static GamesEngine.Business.Liquidity.Persistence.OlapTableDefinitions;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using Puppeteer.EventSourcing;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class OlapRepository : OlapServiceBase, IOlapRepository
    {
        public OlapRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory) { }

        public void CreateDeposit(string kind, Deposit depositDetails)
        {
            string depositTableName = DynamicTableName(DepositTableName, kind);
            string insertSql = $@"
                INSERT INTO {depositTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";

            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = depositDetails.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DocumentNumber", Value = depositDetails.DocumentNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Amount", Value = depositDetails.Amount, DbType = DbType.Decimal },
                new ClickHouseDbParameter { ParameterName = "Date", Value = depositDetails.Date, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "Store", Value = depositDetails.StoreId, DbType = DbType.Byte },
                new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = depositDetails.AccountNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "DomainId", Value = depositDetails.DomainId, DbType = DbType.Int32 },
                new ClickHouseDbParameter { ParameterName = "Address", Value = depositDetails.Address, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = depositDetails.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateJar(string kind, long version, string description, DateTime created, long? originJarId = null)
        {
            string jarTableName = DynamicTableName(JarTableName, kind);
            string insertSql = $"INSERT INTO {jarTableName} (`Version`, `OriginJarId`, `Description`, `Created`) VALUES (@Version, @OriginJarId, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Version", Value = version, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "OriginJarId", Value = (object)originJarId ?? DBNull.Value, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankTableName = DynamicTableName(TankTableName, kind);
            string insertSql = $"INSERT INTO {tankTableName} (`Id`, `Description`, `Created`, `OriginType`, `OriginId`) VALUES (@Id, @Description, @Created, @OriginType, @OriginId);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = tank.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)tank.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = tank.Created, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "OriginType", Value = (object)tank.OriginType ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "OriginId", Value = (object)tank.OriginId ?? DBNull.Value, DbType = DbType.Int64 }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerTableName = DynamicTableName(TankerTableName, kind);
            string insertSql = $"INSERT INTO {tankerTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = tanker.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)tanker.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = tanker.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateJarDetailIfNotExists(string kind, long jarVersion, long depositId, DateTime created)
        {
            string jarDetailTableName = DynamicTableName(JarDetailTableName, kind);
            string insertSql = $@"INSERT INTO {jarDetailTableName} (`JarVersion`, `DepositId`, `Created`) VALUES (@JarVersion, @DepositId, @Created);";
            var insertParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "JarVersion", Value = jarVersion, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DepositId", Value = depositId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, insertParams);
        }

        public void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created)
        {
            string tankDetailTableName = DynamicTableName(TankDetailTableName, kind);
            string insertSql = $@"INSERT INTO {tankDetailTableName} (`TankId`, `DepositId`, `Created`) VALUES (@TankId, @DepositId, @Created);";
            var insertParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "TankId", Value = tankId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DepositId", Value = depositId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, insertParams);
        }


        public void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created)
        {
            if (depositIds == null || !depositIds.Any()) return;

            string tankDetailTableName = DynamicTableName(TankDetailTableName, kind);

            var values = depositIds.Select(depositId => new object[]
            {
                tankId,
                (long)depositId,
                created
            });

            if (!values.Any()) return;

            try
            {
                using (var connection = (ClickHouseConnection)_connectionFactory.CreateConnection())
                {
                    connection.Open();

                    using (var bulkCopy = new ClickHouseBulkCopy(connection)
                    {
                        DestinationTableName = tankDetailTableName,
                        BatchSize = 10000
                    })
                    {
                        bulkCopy.InitAsync().GetAwaiter().GetResult();
                        bulkCopy.WriteToServerAsync(values).GetAwaiter().GetResult();
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error in batch inserting TankDetails (TankId: {tankId}): {ex.Message}. Values count: {values.Count()}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankDetails)} batch insert (TankId: {tankId})");
                throw;
            }
        }

        public void CreateTankerDetailIfNotExists(string kind, long tankerId, long depositId, DateTime created)
        {
            string tankerDetailTableName = DynamicTableName(TankerDetailTableName, kind);
            string insertSql = $@"INSERT INTO {tankerDetailTableName} (`TankerId`, `DepositId`, `Created`) VALUES (@TankerId, @DepositId, @Created);";
            var insertParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "TankerId", Value = tankerId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DepositId", Value = depositId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, insertParams);
        }

        public void CreateWithdrawal(string kind, Withdrawal w)
        {
            string withdrawalTableName = DynamicTableName(WithdrawalTableName, kind);
            string insertSql = $@"
                INSERT INTO {withdrawalTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);
            ";

            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = w.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DocumentNumber", Value = w.DocumentNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Amount", Value = w.Amount, DbType = DbType.Decimal },
                new ClickHouseDbParameter { ParameterName = "Date", Value = w.Date, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "Store", Value = w.StoreId, DbType = DbType.Byte },
                new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = w.AccountNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "DomainId", Value = w.DomainId, DbType = DbType.Int32 },
                new ClickHouseDbParameter { ParameterName = "Address", Value = w.Address, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = w.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateBottle(string kind, Bottle b)
        {
            string bottleTableName = DynamicTableName(BottleTableName, kind);
            string insertSql = $"INSERT INTO {bottleTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = b.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)b.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = b.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateDispenser(string kind, Dispenser d)
        {
            string dispenserTableName = DynamicTableName(DispenserTableName, kind);
            string insertSql = $"INSERT INTO {dispenserTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = d.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)d.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = d.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created)
        {
            string bottleDetailTableName = DynamicTableName(BottleDetailTableName, kind);
            string insertSql = $@"INSERT INTO {bottleDetailTableName} (`WithdrawalId`, `BottleId`, `Created`) VALUES (@WithdrawalId, @BottleId, @Created);";
            var insertParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "WithdrawalId", Value = withdrawalId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "BottleId", Value = bottleId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, insertParams);
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created)
        {
            string dispenserDetailTableName = DynamicTableName(DispenserDetailTableName, kind);
            string insertSql = $@"INSERT INTO {dispenserDetailTableName} (`WithdrawalId`, `DispenserId`, `Created`) VALUES (@WithdrawalId, @DispenserId, @Created);";
            var insertParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "WithdrawalId", Value = withdrawalId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DispenserId", Value = dispenserId, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, insertParams);
        }
    }
}
