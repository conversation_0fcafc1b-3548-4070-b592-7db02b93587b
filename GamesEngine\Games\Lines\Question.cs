﻿using GamesEngine.Middleware;
using GamesEngine.Resources;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	[Puppet]
	internal abstract class Question : Objeto
	{
		private readonly Tier tier;
		private readonly int id;
		private string shortcut;
		protected readonly LinesCatalog catalog;
		private string text;
		private readonly CheckPeriods checkedPeriods;
		internal const int MINIMUM_LIVE_TIME = 10;

		private AvailabilityTime.Milestone timeKillerMoment = AvailabilityTime.Milestone.None;
		private int timeKillerInSeconds;
		private string timeKillerPeriodName;

		private AvailabilityTime.Milestone publishTimeMoment = AvailabilityTime.Milestone.None;
		private int publishTimeInSeconds;
		private string publishTimePeriodName;
		private readonly Aliases aliases;

		internal Question(int id, Tier tier, string shortcut, string text, LinesCatalog catalog)
		{
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (catalog == null) throw new ArgumentNullException(nameof(catalog));
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			this.id = id;
			this.tier = tier;
			this.shortcut = shortcut;
			this.catalog = catalog;
			this.catalog.Tournaments.SetLastQuestionId(id);
			this.text = text;
			this.checkedPeriods = new CheckPeriods(this.catalog.Sport);
			if (tier == Tier.TIER_ONE)
            {
				var period = this.catalog.Sport.OfficialPeriod("Pregame");
				checkedPeriods.Check(period);
            }

			aliases = new Aliases();
		}

		internal Aliases Aliases
		{
			get
			{
				return aliases;
			}
		}

		internal Sport Sport
		{
			get
			{
				return this.catalog.Sport;
			}
		}

		internal string Shortcut
		{
			get
			{
				return this.shortcut;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

				this.shortcut = value;
			}
		}

		internal int Id
		{
			get
			{
				return this.id;
			}
		}

		internal Tier Tier
		{
			get
			{
				return this.tier;
			}
		}

		internal string Text
		{
			get
			{
				return this.text;
			}

			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

				this.text = value;
			}
		}

		internal string Type => GetType().Name;

		internal IEnumerable<Period> ValidPeriodsToBePublished()
        {
			return this.checkedPeriods.ToList();
        }

		internal void AllowPublished(Period period)
		{
			if (tier == Tier.TIER_ONE && !period.IsPregame()) throw new GameEngineException("Tier One Line can only be published on pregame period.");
			if (period == null) throw new ArgumentException(nameof(period));

			this.checkedPeriods.Check(period);
		}

		internal void AllowPublished(IEnumerable<string> periodNames)
		{
			if (! periodNames.Any()) throw new ArgumentException(nameof(periodNames));

			if(tier == Tier.TIER_ONE)
			{
				foreach (var periodName in periodNames)
				{
					var period = catalog.Sport.OfficialPeriod(periodName);
					if ( ! period.IsPregame()) throw new GameEngineException("Tier One Line can only be published on pregame period.");
				}
			}

			foreach (var periodName in periodNames)
            {
				var period = catalog.Sport.OfficialPeriod(periodName);
				AllowPublished(period);
			}
		}
		internal void DenyPublished(Period period)
        {
			if (tier == Tier.TIER_ONE) throw new GameEngineException("Tier One Line can only be published on pregame period.");
			if (period == null) throw new ArgumentException(nameof(period));

			this.checkedPeriods.Uncheck(period);
        }

		internal bool IsChecked(Period period)
		{
			if (period == null) throw new ArgumentException(nameof(period));
			
			return this.checkedPeriods.IsChecked(period);
		}

		internal void TimeKiller(AvailabilityTime.Milestone moment, int timeInSeconds)
		{
			if (moment == AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException("Time killer only can be calculated for moments different than specific date time");
			if (timeInSeconds < MINIMUM_LIVE_TIME) throw new GameEngineException("Time in seconds must be greater than zero");

			this.timeKillerMoment = moment;
			this.timeKillerInSeconds = timeInSeconds;
		}

		internal void TimeKiller(AvailabilityTime.Milestone moment, string periodName, int timeInSeconds)
		{
			if (moment == AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException("Time killer only can be calculated for moments different than specific date time");
			if (string.IsNullOrWhiteSpace(periodName)) throw new ArgumentNullException(nameof(periodName));
			if (timeInSeconds < MINIMUM_LIVE_TIME) throw new GameEngineException("Time in seconds must be greater than zero");
			if (moment == AvailabilityTime.Milestone.None) throw new GameEngineException("Moment cannot be set as None");

			this.timeKillerMoment = moment;
			this.timeKillerPeriodName = periodName;
			this.timeKillerInSeconds = timeInSeconds;
		}

		internal void PublishTime(AvailabilityTime.Milestone moment, int timeInSeconds)
		{
			if (moment == AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException("Publish time only can be calculated for moments different than specific date time");
			if (moment == AvailabilityTime.Milestone.AfterLinePublishing) throw new GameEngineException("Publish time only can be calculated for moments different than after line publishing");
			if (timeInSeconds <= 0) throw new GameEngineException("Time in seconds must be greater than zero");

			this.publishTimeMoment = moment;
			this.publishTimeInSeconds = timeInSeconds;
		}

		internal void PublishTime(AvailabilityTime.Milestone moment, string periodName, int timeInSeconds)
		{
			if (moment == AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException("Publish time only can be calculated for moments different than specific date time");
			if (moment == AvailabilityTime.Milestone.AfterLinePublishing) throw new GameEngineException("Publish time only can be calculated for moments different than after line publishing");
			if (string.IsNullOrWhiteSpace(periodName)) throw new ArgumentNullException(nameof(periodName));
			if (timeInSeconds <= 0) throw new GameEngineException("Time in seconds must be greater than zero");
			if (moment == AvailabilityTime.Milestone.None) throw new GameEngineException("Moment cannot be set as None");

			this.publishTimeMoment = moment;
			this.publishTimePeriodName = periodName;
			this.publishTimeInSeconds = timeInSeconds;
		}
		internal void UnlimitedTime()
		{
			this.timeKillerMoment = AvailabilityTime.Milestone.None;
		}

		internal AvailabilityTime.Milestone TimeKillerMoment
		{
			get
			{
				return this.timeKillerMoment;
			}
		}

		internal string TimeKillerMomentAsString
		{
			get
			{
				return this.timeKillerMoment.ToString();
			}
		}

		internal bool HasTimeKiller()
		{
			if (this.TimeKillerMoment == AvailabilityTime.Milestone.None) return false;
			return true;
		}

		internal int TimeKillerInSeconds
		{
			get
			{
				if (this.timeKillerMoment == AvailabilityTime.Milestone.None) throw new GameEngineException("Time killer moment has not been set");

				return this.timeKillerInSeconds;
			}
			set 
			{
				if (this.timeKillerMoment != AvailabilityTime.Milestone.SpecificDateTime) throw new GameEngineException("Time killer moment only be set lazy for Specific Date Time");
				if (value < MINIMUM_LIVE_TIME) throw new GameEngineException("Time in seconds must be greater than zero");

				this.timeKillerInSeconds = value;
			}
		}

		internal string TimeKillerPeriodName
		{
			get
			{
				if (this.timeKillerMoment == AvailabilityTime.Milestone.None) throw new GameEngineException("Time killer moment has not been set");

				return this.timeKillerPeriodName;
			}
		}

		internal AvailabilityTime.Milestone PublishTimeMoment
		{
			get
			{
				return this.publishTimeMoment;
			}
		}

		internal int PublishTimeInSeconds
		{
			get
			{
				if (this.publishTimeMoment == AvailabilityTime.Milestone.None) throw new GameEngineException("Publish time moment has not been set");

				return this.publishTimeInSeconds;
			}
		}

		internal string PublishTimePeriodName
		{
			get
			{
				if (this.publishTimeMoment == AvailabilityTime.Milestone.None) throw new GameEngineException("Publish time moment has not been set");

				return this.publishTimePeriodName;
			}
		}

		internal bool HasBackground()
        {
			return background != null;
		}

		ImageResource background;
		internal ImageResource Background
		{
			get
			{
				if (background == null) throw new ArgumentNullException(nameof(background));

				return background;
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(value));

				background = value;
			}
		}
	}

	internal abstract class ABBaseQuestion : Question
	{
		private int teamAReward = int.MinValue;
		private int teamBReward = int.MinValue;

		protected ABBaseQuestion(int id, Tier tier, string shortcut, string text, LinesCatalog catalog) : base(id, tier, shortcut, text, catalog)
		{
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
		}

		protected void SetABParameters(int teamAReward, int teamBReward)
		{
			MoneyLine.ValidateReward(teamAReward, teamBReward);

			this.teamAReward = teamAReward;
			this.teamBReward = teamBReward;
		}

		internal int TeamAReward
		{
			get
			{
				return this.teamAReward;
			}
		}

		internal int TeamBReward
		{
			get
			{
				return this.teamBReward;
			}
		}
	}

	internal class ABQuestion : ABBaseQuestion
	{
		internal ABQuestion(int id, Tier tier, string shortcut, string text, LinesCatalog catalog) : base(id, tier, shortcut, text, catalog)
		{
		}

		internal void SetParameters(int teamAReward, int teamBReward)
		{
			base.SetABParameters(teamAReward, teamBReward);
		}
	}

	internal class SpreadQuestion : ABBaseQuestion
	{
		private double spread;

		internal SpreadQuestion(int id, Tier tier, string shortcut, LinesCatalog catalog) : base(id, tier, shortcut, SpreadLine.TEXT, catalog)
		{
		}

		internal void SetParameters(double spread, int teamAReward, int teamBReward)
		{
			if (spread < 0) throw new GameEngineException($"Spread {spread} is not a valid spread");
			Line.ValidateSpread(spread, base.catalog.Sport);
			SpreadLine.ValidateReward(teamAReward, teamBReward);

			base.SetABParameters(teamAReward, teamBReward);
			this.spread = spread;
		}

		internal double Spread
		{
			get
			{
				return this.spread;
			}
		}
	}

	internal class MoneyQuestion : ABBaseQuestion
	{
		internal MoneyQuestion(int id, Tier tier, string shortcut, LinesCatalog catalog) : base(id, tier, shortcut, MoneyLine.TEXT, catalog)
		{
		}

		internal void SetParameters(int teamAReward, int teamBReward)
		{
			base.SetABParameters(teamAReward, teamBReward);
		}
	}

	internal class MoneyDrawQuestion : ABBaseQuestion
	{
		private int tieReward= int.MinValue;

		internal MoneyDrawQuestion(int id, Tier tier, string shortcut, LinesCatalog catalog) : base(id, tier, shortcut, MoneyDrawLine.TEXT, catalog)
		{
		}

		internal void SetParameters(int teamAReward, int tieReward, int teamBReward)
		{
			MoneyLine.ValidateReward(teamAReward, teamBReward);
			base.SetABParameters(teamAReward, teamBReward);
			this.tieReward = tieReward;
		}

		internal int TieReward
		{
			get
			{
				return this.tieReward;
			}
		}
	}

	internal class TotalPointsQuestion : OverUnderQuestion
	{
		internal TotalPointsQuestion(int id, Tier tier, string shortcut, LinesCatalog catalog) : base(id, tier, shortcut, TotalPointsLine.TEXT, catalog)
		{
		}
	}

	internal class YesNoQuestion : Question
	{
		private int yesReward = int.MinValue;
		private int noReward = int.MinValue;

		internal YesNoQuestion(int id, Tier tier, string shortcut, string text, LinesCatalog catalog) : base(id, tier, shortcut, text, catalog)
		{

		}

		internal void SetParameters(int yesReward, int noReward)
		{
			YesNoLine.ValidateReward(yesReward, noReward);

			this.yesReward = yesReward;
			this.noReward = noReward;
		}

		internal int YesReward
		{
			get
			{
				if (this.yesReward == int.MinValue) throw new GameEngineException("The yes reward must be initialized");

				return this.yesReward;
			}
		}
		internal int NoReward
		{
			get
			{
				if (this.noReward == int.MinValue) throw new GameEngineException("The no reward must be initialized");

				return this.noReward;
			}
		}
	}

	internal class OverUnderQuestion : Question
	{
		private double score = double.MinValue;
		private int overReward = int.MinValue;
		private int underReward = int.MinValue;

		internal OverUnderQuestion(int id, Tier tier, string shortcut, string text, LinesCatalog catalog) : base(id, tier, shortcut, text, catalog)
		{

		}

		internal void SetParameters(double score, int overReward, int underReward)
		{
			if (score <= 0) throw new GameEngineException("Score must be greater than zero");
			OverUnderLine.ValidateReward(overReward, underReward);

			this.score = score;
			this.overReward = overReward;
			this.underReward = underReward;
		}

		internal int OverReward
		{
			get
			{
				if (this.overReward == int.MinValue) throw new GameEngineException("The over reward must be initialized");

				return this.overReward;
			}
		}
		internal int UnderReward
		{
			get
			{
				if (this.underReward == int.MinValue) throw new GameEngineException("The under reward must be initialized");

				return this.underReward;
			}
		}

		internal double Score
		{
			get
			{
				if (this.score == double.MinValue) throw new GameEngineException("The score must be initialized");

				return this.score;
			}
		}
	}

	internal class FixedQuestion : Question
	{
		private IEnumerable<string> options;
		private IEnumerable<int> rewards;

		internal FixedQuestion(int id, Tier tier, string shortcut, string text, LinesCatalog catalog) : base(id, tier, shortcut, text, catalog)
		{

		}

		internal void SetParameters(IEnumerable<string> options, IEnumerable<int> rewards)
		{
			if (options == null) throw new ArgumentNullException(nameof(options));
			if (rewards == null) throw new ArgumentNullException(nameof(rewards));
			if (options.Any(x => string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(options));
			if (options.Count() != rewards.Count()) throw new GameEngineException("Options and rewards must have the same length");
			FixedLine.ValidateRewards(rewards);

			List<string> newOptions = new List<string>(options);
			List<int> newRewards = new List<int>(rewards);
			this.options = newOptions;
			this.rewards = newRewards;
		}

		internal IEnumerable<int> Rewards
		{
			get
			{
				if (this.rewards == null) throw new GameEngineException("Rewards must be initialized");

				return this.rewards;
			}
		}
		internal IEnumerable<string> Options
		{
			get
			{
				if (this.options == null) throw new GameEngineException("Options must be initialized");

				return this.options;
			}
		}
	}
}
