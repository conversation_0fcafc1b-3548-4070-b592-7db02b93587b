﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class Grade : ASITenantDriver
	{
		private RestClient _gradeFreeFormWagerCollectionClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;
        public string CompanyBaseUrlServices { get; private set; }
		public Grade() : base(Tenant_Actions.Grade, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
		{
			throw new NotImplementedException();
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("wagers");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
			if (_gradeFreeFormWagerCollectionClient == null)
			{
				_gradeFreeFormWagerCollectionClient = new RestClient(CompanyBaseUrlServices);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_gradeFreeFormWagerCollectionClient = new RestClient(CompanyBaseUrlServices);
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			var Wagers = recordSet.Mappings["wagers"];

			List<PayFragmentsMessage> wagers =  Wagers.As<List<PayFragmentsMessage>>() ;

			var result = GradeTicket(now, wagers);
			return (T)Convert.ChangeType(result, typeof(T));

		}

		private GradeFreeFormWagersResponse GradeTicket(DateTime now, List<PayFragmentsMessage> wagers)
		{
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Count == 0) throw new Exception($"At least one wager is required to send request {nameof(GradeFreeFormWagerCollection)} on GradeTicket");

			Debug.WriteLine($"Accounting service {nameof(GradeFreeFormWagerCollection)} received {nameof(wagers)}:{wagers}");
			var result = GradeFreeFormWagerCollection(now, wagers);
			return result;
		}

		private GradeFreeFormWagersResponse GradeFreeFormWagerCollection(DateTime now, List<PayFragmentsMessage> wagers)
		{
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Count == 0) throw new Exception($"At least one wager is required to send request {nameof(GradeFreeFormWagerCollection)}");

			const string url = "/v1/5dimesAPI/GradeFreeFormWagerCollection";
			var validWagers = wagers;
			var wagersWithFakeOrWrongNumber = wagers.Where(x => String.IsNullOrWhiteSpace(x.TicketNumber) || int.Parse(x.TicketNumber) <= 0 || x.WagerNumber == 0);
			if (wagersWithFakeOrWrongNumber.Count() > 0)
			{
				validWagers = wagers.Where(x => !wagersWithFakeOrWrongNumber.Contains(x)).ToList();
				if (validWagers.Count == 0)
				{
					NotifyWarn(nameof(GradeFreeFormWagerCollection), $"Url:{url}", $"At least one wager is required to send request {nameof(GradeFreeFormWagerCollection)}");
					return CreateFakeGradeFreeFormWagerCollectionResponse(now, wagers, "Wagers are empty");
				}

				GradeFreeFormWagers gradeFreeFormWagersWithFakeOrWrongNumber = new GradeFreeFormWagers()
				{
					SystemId = SystemId,
					SystemPassword = SystemPassword,
					ClerkId = ClerkId,
					Wagers = wagersWithFakeOrWrongNumber.ToArray()
				};

				var valuesWithFakeOrWrongNumber = new GradeFreeFormWagerCollectionBody()
				{
					Collection = gradeFreeFormWagersWithFakeOrWrongNumber
				};

				var jsonStringWithFakeOrWrongNumber = Commons.ToJson(valuesWithFakeOrWrongNumber);
				NotifyError(nameof(GradeFreeFormWagerCollection), $"Url:{url} Request: {ASIJsonUtils.HideSensitiveData(gradeFreeFormWagersWithFakeOrWrongNumber)}", $"Any wager has TicketNumber or WagerNumber equal to 0 or fake value");
			}

			GradeFreeFormWagersResponse finalResponse = null;
			List<List<PayFragmentsMessage>> listOfGradesChunked = ASIJsonUtils.SplitList(validWagers, 100);
			foreach (List<PayFragmentsMessage> wagersChunck in listOfGradesChunked)
			{

				GradeFreeFormWagersResponse response = GradeFreeFormWagerCollectionChunked(now, url, wagersChunck);
				if (finalResponse == null)
				{
					finalResponse = response;
				}
				else
				{
					finalResponse.Merge(response);
				}
			}
			return finalResponse;

		}

		private GradeFreeFormWagersResponse GradeFreeFormWagerCollectionChunked(DateTime now, string url, List<PayFragmentsMessage> wagers)
		{
			GradeFreeFormWagers gradeFreeFormWagers = new GradeFreeFormWagers()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				Wagers = wagers.ToArray()
			};

			var values = new GradeFreeFormWagerCollectionBody()
			{
				Collection = gradeFreeFormWagers
			};

			var jsonString = Commons.ToJson(values);
			string responseString = "";
			int retryNumber = 0;
			const int MAX_RETRIES_WAGER_COLLECTION = 5;


			string valuesWithHiddenFields = ASIJsonUtils.HideSensitiveData(values, Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection);

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var request = new RestRequest(url, Method.Post);
					request.AddHeader("Content-Type", "application/json");
					request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
					var response = _gradeFreeFormWagerCollectionClient.Execute(request);
					responseString = response.Content;

					Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIGradeFreeFormWagerCollection.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";

					Thread.Sleep(2000);

					if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
					{
						InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
						return CreateFakeGradeFreeFormWagerCollectionResponse(now, wagers, "Maximum number of retries was reached");
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"Response can not be empty");
				return CreateFakeGradeFreeFormWagerCollectionResponse(now, wagers, "Accounting response was empty");
			}
			else
			{
				var objectResponse = Commons.FromJson<GradeFreeFormWagersResponse>(responseString);
				if (objectResponse.Error != null)
				{
					NotifyWarn(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"Response threw an error");
					return CreateFakeGradeFreeFormWagerCollectionResponse(now, wagers, objectResponse.Error.Message);
				}
				else
				{
					if (String.IsNullOrWhiteSpace(objectResponse.ClerkId))
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"{nameof(objectResponse.ClerkId)} is null");
					}
					else if (String.IsNullOrWhiteSpace(objectResponse.SystemId))
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"{nameof(objectResponse.SystemId)} is null");
					}
					else if (String.IsNullOrWhiteSpace(objectResponse.SystemPassword))
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"{nameof(objectResponse.SystemPassword)} is null");
					}
					else if (objectResponse.Wagers == null)
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"{nameof(objectResponse.Wagers)} are null");
					}
					else if (objectResponse.Wagers.Any(x => !x.IsValidTicketNumber))
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"At least one wager has an invalid ticketNumber on GradeFreeFormWagerCollectionChunked.");
					}
					else if (objectResponse.Wagers.Length != wagers.Count)
					{
						NotifyError(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {jsonString}\nResponse: {responseString}", $"Wagers count is not the same. Body has {wagers.Count} wagers and response has {objectResponse.Wagers.Length} wagers");
					}
					return objectResponse;
				}

			}
		}
		private GradeFreeFormWagersResponse CreateFakeGradeFreeFormWagerCollectionResponse(DateTime now, List<PayFragmentsMessage> wagers, string errorMessage)
		{
			var fakeResponse = new GradeFreeFormWagersResponse()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				FailedWagers = wagers?.ToArray(),
				Error = new ErrorResponse() { Code = string.Empty, Message = errorMessage }
			};

			if (fakeResponse.Wagers != null)
			{
				for (int index = 0; index < fakeResponse.Wagers.Length; index++)
				{
					var wager = fakeResponse.Wagers[index];
					wager.IsValidTicketNumber = true;
					wager.TicketNumber = fakeTicketNumber.ToString();
				}
				fakeTicketNumber--;
			}

			return fakeResponse;
		}
    }
}
