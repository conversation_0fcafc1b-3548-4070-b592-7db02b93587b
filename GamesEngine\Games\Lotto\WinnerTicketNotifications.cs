﻿using GamesEngine.Bets;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Messaging;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lotto
{
    abstract class WinnerTicketNotifications : Objeto
    {
        protected List<Ticket> tickets = new List<Ticket>();

        internal abstract void Add(bool itIsThePresent, DateTime now, Ticket ticket);

        internal void Send(bool itIsThePresent, DateTime now)
        {
            if (itIsThePresent)
            {
                var ticketEvent = new TicketWinnerEvent(now, tickets);
                PlatformMonitor.GetInstance().WhenNewEvent(ticketEvent);
            }
        }

        internal void RemoveAll(bool itIsThePresent)
        {
            foreach (var ticket in tickets)
            {
                Remove(itIsThePresent, ticket);
            }
        }

        internal void Remove(bool itIsThePresent, Ticket ticket)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            tickets.RemoveAll(x => x == ticket);
            Subscriber fromSubscriber = ticket.Lottery.PicksLotteryGame;
            Subscriber toSubscriber = ticket.Player;
            if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                if (ticket.IsNoAction())
                {
                    Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForNotifications,
                    new NotificationMessage(NotificationMessageType.REMOVE, fromSubscriber, toSubscriber, $"You won with the TXN {ticket.TicketNumber} selection {ticket.NoActionDraw.PreviousSequenceOfNumber}"));
                }
                else if (ticket.IsGraded() || ticket.IsRegraded())
                {
                    Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForNotifications,
                    new NotificationMessage(NotificationMessageType.REMOVE, fromSubscriber, toSubscriber, $"You won with the TXN {ticket.TicketNumber} selection {ticket.Draw.PreviousSequenceOfNumber}"));
                }
                else
                {
                    throw new GameEngineException("Notification can not be removed because there is an invalid grading status");
                }
            }
        }

        internal bool IsEmpty()
        {
            return !tickets.Any();
        }

    }

    class WinnerTicketLottoNotifications : WinnerTicketNotifications
    {
        internal override void Add(bool itIsThePresent, DateTime now, Ticket ticket)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!ticket.IsWinner()) throw new GameEngineException("Notification is only added for winner ticket");

            tickets.Add(ticket);
            Subscriber fromSubscriber = ticket.Lottery.PicksLotteryGame;
            Subscriber toSubscriber = ticket.Player;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForNotifications,
                    new NotificationMessage(NotificationMessageType.ADD, fromSubscriber, toSubscriber, $"You won with the TXN {ticket.TicketNumber} selection {ticket.Draw.SequenceOfNumbers}"));
            }
        }
    }

    class WinnerTicketKenoNotifications : WinnerTicketNotifications
    {
        internal override void Add(bool itIsThePresent, DateTime now, Ticket ticket)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (!ticket.IsWinner()) throw new GameEngineException("Notification is only added for winner ticket");
            if (ticket is not TicketKeno) throw new GameEngineException("Ticket does not belong to keno");

            tickets.Add(ticket);
            Subscriber fromSubscriber = ticket.Lottery.PicksLotteryGame;
            Subscriber toSubscriber = ticket.Player;
            if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
            {
                var ticketKeno = (TicketKeno)ticket;
                string description = $"Congratulations! You won with TXN {ticket.TicketNumber} selection {ticketKeno.SelectedNumbers}";
                if (ticketKeno.NumbersMatches == 0) description = $"TXN {ticket.TicketNumber} has not won but no worries try again, an amount has been credited to your account.";
                Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForNotifications,
                    new NotificationMessage(NotificationMessageType.ADD, fromSubscriber, toSubscriber, description));
            }
        }
    }
}