﻿using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static ExchangeAPI.Security;
using static GamesEngine.Finance.Currencies;
using static town.connectors.drivers.Result;

namespace ExchangeAPI.Controllers
{
	public class AdminController : AuthorizeController
	{
		[HttpPost("api/markerplace/settings")]
		[Authorize(Roles = "s300")] //change role.
		public async Task<IActionResult> AccountBalanceAsync([FromBody]Accounts body)
		{
			if (body == null) return BadRequest("Body is required");
			StringBuilder addAccountScript = new StringBuilder();

			addAccountScript.Append("{");
			addAccountScript.Append("accounts = SetOfBalances();");
			foreach (var accountIn in body.AdminAccountsIn)
			{
				if (String.IsNullOrWhiteSpace(accountIn.AccountNumber)) return NotFound($"Parameter {nameof(accountIn.AccountNumber)} is required");
				if (accountIn.Amount <= 0) return NotFound($"{nameof(accountIn.Amount)} is required");

				addAccountScript.Append($" accounts.AddAccountIn('{accountIn.AccountNumber}', Currency('{accountIn.CurrencyCode}',{accountIn.Amount}));");
			}
			
			foreach (var accountOut in body.AdminAccountsOut)
			{
				if (String.IsNullOrWhiteSpace(accountOut.AccountNumber)) return NotFound($"Parameter {nameof(accountOut.AccountNumber)} is required");
				if (accountOut.Amount <= 0) return NotFound($"{nameof(accountOut.Amount)} is required");

				addAccountScript.Append($" accounts.AddAccountOut('{accountOut.AccountNumber}', Currency('{accountOut.CurrencyCode}',{accountOut.Amount}));");
			}

			addAccountScript.Append(" marketplace.RealAccounts = accounts;");
			addAccountScript.Append("}");
			return await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, addAccountScript.ToString());
		}

		[HttpGet("api/marketplace/settings/transactionstypes/allowed")]
		[Authorize(Roles = "c59")]
		public async Task<IActionResult> ListAllowedTransactionsAsync()
		{
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					for( domain : company.Sales.AllDomains )
					{{
						print domain.Id domainId;
						print domain.Url domain;
						allowedTransactions = domain.AllowedTransactions.List();
						for( allowedTransaction : allowedTransactions )
						{{
							print allowedTransaction.Type type;
							print allowedTransaction.Enabled enabled;
						}}
					}}
				}}
				");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}

		[HttpPost("api/marketplace/settings/transactionstypes")]
		[Authorize(Roles = "c60")]
		public async Task<IActionResult> ListAllowedTransactionsAsync([FromBody]TransactionTypeAllowed body)
		{
			if (string.IsNullOrWhiteSpace(body.Domain)) return NotFound($"{nameof(body.Domain)} is required");
			TransactionType transaction;
			if (body.TransactionType.Equals("Deposit Then Lock", StringComparison.OrdinalIgnoreCase)) transaction = TransactionType.Deposit_Then_Lock;
			else if (!Enum.TryParse(body.TransactionType, out transaction)) return BadRequest($"{nameof(body.TransactionType)} is not valid");
			string command = $@"
				{{
					domain = company.Sales.DomainFrom('{body.Domain}');
					domain.AllowedTransactions.{((body.Enable) ? "Disable" : "Enable")}({transaction});
				}}
				";
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}
		
		[HttpGet("api/marketplace/settings/transactionstypes/withdrawals")]
		[Authorize(Roles = "c65")]
		public async Task<IActionResult> GetWithdrawalsSettingAsync()
		{
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					print marketplace.AreAllWithdrawalsManagedAsDisbursements areAllWithdrawalsManagedAsDisbursements;
				}}
				");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}

		[HttpPost("api/marketplace/settings/transactionstypes/withdrawals")]
		[Authorize(Roles = "c66")]
		public async Task<IActionResult> SetWithdrawalsSettingsAsync([FromBody] WithdrawalsSetting body)
		{
			if (body == null) return BadRequest("Body is required");
			string command = $@"
				{{
					marketplace.AreAllWithdrawalsManagedAsDisbursements = {body.AreAllWithdrawalsManagedAsDisbursements};
				}}
				";
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}

		[HttpGet("api/marketplace/settings/processors")]
		[Authorize(Roles = "c63,c83,c84")]
		public async Task<IActionResult> ListProcessorsAsync(int domainId)
		{
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					for( processorsCashAvailable : processors.ListAllCashProcessor() )
					{{
						print processorsCashAvailable.Id id;
						print processorsCashAvailable.Name name;
					}}
					for( processorsBankAvailable : processors.ListAllBanksProcessor() )
					{{
						print processorsBankAvailable.Id id;
						print processorsBankAvailable.Name name;
					}}
					for( processorsCardAvailable : processors.ListAllCardsProcessor() )
					{{
						print processorsCardAvailable.Id id;
						print processorsCardAvailable.Name name;
					}}
					for( processorsBtcAvailable : processors.ListAllBtcProcessor() )
					{{
						print processorsBtcAvailable.Id id;
						print processorsBtcAvailable.Name name;
					}}
					for( processorsThirdPartyAvailable : processors.ListAllThirdPartyProcessor() )
					{{
						print processorsThirdPartyAvailable.Id id;
						print processorsThirdPartyAvailable.Name name;
					}}
					for( processorsFinancialServicesAvailable : processors.ListAllFinancialServicesProcessor() )
					{{
						print processorsFinancialServicesAvailable.Id id;
						print processorsFinancialServicesAvailable.Name name;
					}}
					for( domain : company.Sales.AllDomains )
					{{
						print domain.Id domainId;
						print domain.Url domain;

						for( allowedCashProcessor : processors.ListAllCashProcessor(domain) )
						{{
							print allowedCashProcessor.Id id;
							print allowedCashProcessor.Name name;
						}}
						for( allowedBankProcessor : processors.ListAllBanksProcessor(domain) )
						{{
							print allowedBankProcessor.Id id;
							print allowedBankProcessor.Name name;
						}}
						for( allowedCardProcessor : processors.ListAllCardsProcessor(domain) )
						{{
							print allowedCardProcessor.Id id;
							print allowedCardProcessor.Name name;
						}}
						for( allowedBtcProcessor : processors.ListAllBtcProcessor(domain) )
						{{
							print allowedBtcProcessor.Id id;
							print allowedBtcProcessor.Name name;
						}}
						for( allowedThirdPartyProcessor : processors.ListAllThirdPartyProcessor(domain) )
						{{
							print allowedThirdPartyProcessor.Id id;
							print allowedThirdPartyProcessor.Name name;
						}}
						for( allowedFinancialServicesProcessor : processors.ListAllFinancialServicesProcessor(domain) )
						{{
							print allowedFinancialServicesProcessor.Id id;
							print allowedFinancialServicesProcessor.Name name;
						}}
					}}
				}}
				");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}

		[HttpGet("api/marketplace/settings/processors/{processorId}")]
		[Authorize(Roles = "c70")]
		public async Task<IActionResult> SettingsForProcessorAsync(string processorId, int domainId)
		{
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"{nameof(processorId)} is required");
			if (domainId <= 0) return BadRequest($"{nameof(domainId)} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({domainId});
					for(settings : processors.ListSettings({processorId}, domain))
					{{
						print settings.TransactionType.ToString() transactionType;
						print settings.CurrencyCode.ToString() currencyCode;
						print settings.IsTransactionEnabled isTransactionEnabled;
						for(notifications : settings.ListNotificationsSetting())
						{{
							print notifications.Enabled enabled;
							print notifications.Status.ToString() status;
						}}
					}}
				}}
				");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}

		[HttpPost("api/marketplace/settings/processors/{processorId}/notification/enabled")]
		[Authorize(Roles = "c85")]
		public async Task<IActionResult> EnableNotificationSettingForProcessorAsync(string processorId, [FromBody] NotificationSettingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"{nameof(processorId)} is required");
			if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					existsSetting = processors.ExistsSetting({processorId}, domain, {body.TransactionType});
					print existsSetting existsSetting;
					if (existsSetting)
					{{
						isNotificationEnabled = processors.IsNotificationEnabled({processorId}, domain, {body.TransactionType}, {body.Status});
						print isNotificationEnabled isNotificationEnabled;
					}}
				}}
			");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var settingExistence = JsonConvert.DeserializeObject<NotificationSettingExistence>(json);
			if (!settingExistence.IsNotificationEnabled)
            {
				IActionResult resultCmd = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					processors.EnableNotification('{processorId}', domain, {body.TransactionType}, {body.Status});
				}}
				");
				if (!(resultCmd is OkObjectResult))
				{
					throw new GameEngineException($@"Error:{((ObjectResult)resultCmd).Value.ToString()}");
				}

				return resultCmd;
			}
			else
			{
				return PrefabBadRequest("Sorry, notification setting cannot be enabled");
			}
		}

		[HttpPost("api/marketplace/settings/processors/{processorId}/transactionType/enabled")]
		[Authorize(Roles = "c82")]
		public async Task<IActionResult> EnableTransactionTypeSettingAsync(string processorId, [FromBody] TransactionSettingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"{nameof(processorId)} is required");
			if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					existsSetting = processors.ExistsSetting({processorId}, domain, {body.TransactionType});
					print existsSetting existsSetting;
					if (existsSetting)
					{{
						isTransactionTypeEnabled = processors.IsTransactionTypeEnabled({processorId}, domain, {body.TransactionType});
						print isTransactionTypeEnabled isTransactionTypeEnabled;
					}}
				}}
			");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var settingExistence = JsonConvert.DeserializeObject<TransactionTypeSettingExistence>(json);
			if (! settingExistence.IsTransactionTypeEnabled)
			{
				IActionResult resultCmd = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					processors.EnableTransactionType('{processorId}', domain, {body.TransactionType});
				}}
				");
				if (!(resultCmd is OkObjectResult))
				{
					throw new GameEngineException($@"Error:{((ObjectResult)resultCmd).Value.ToString()}");
				}

				return resultCmd;
			}
			else
			{
				return PrefabBadRequest("Sorry, notification setting cannot be enabled");
			}
		}

		[HttpPost("api/marketplace/settings/processors/{processorId}/notification/disabled")]
		[Authorize(Roles = "c85")]
		public async Task<IActionResult> DisableNotificationSettingForProcessorAsync(string processorId, [FromBody] NotificationSettingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"{nameof(processorId)} is required");
			if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					existsSetting = processors.ExistsSetting({processorId}, domain, {body.TransactionType});
					print existsSetting existsSetting;
					if (existsSetting)
					{{
						isNotificationEnabled = processors.IsNotificationEnabled({processorId}, domain, {body.TransactionType}, {body.Status});
						print isNotificationEnabled isNotificationEnabled;
					}}
				}}
			");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var settingExistence = JsonConvert.DeserializeObject<NotificationSettingExistence>(json);
			if (settingExistence.IsNotificationEnabled)
			{
				IActionResult resultCmd = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						domain = company.Sales.DomainFrom({body.DomainId});
						processors.DisableNotification('{processorId}', domain, {body.TransactionType}, {body.Status});
					}}
				");
				if (!(resultCmd is OkObjectResult))
				{
					throw new GameEngineException($@"Error:{((ObjectResult)resultCmd).Value.ToString()}");
				}

				return resultCmd;
			}
			else
			{
				return PrefabBadRequest("Sorry, notification setting cannot be disabled");
			}
		}

		[HttpPost("api/marketplace/settings/processors/{processorId}/transactionType/disabled")]
		[Authorize(Roles = "c82")]
		public async Task<IActionResult> DisableTransactionTypeSettingAsync(string processorId, [FromBody] TransactionSettingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(processorId)) return BadRequest($"{nameof(processorId)} is required");
			if (body.DomainId <= 0) return BadRequest($"{nameof(body.DomainId)} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.DomainId});
					existsSetting = processors.ExistsSetting({processorId}, domain, {body.TransactionType});
					print existsSetting existsSetting;
					if (existsSetting)
					{{
						isTransactionTypeEnabled = processors.IsTransactionTypeEnabled({processorId}, domain, {body.TransactionType});
						print isTransactionTypeEnabled isTransactionTypeEnabled;
					}}
				}}
			");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var settingExistence = JsonConvert.DeserializeObject<TransactionTypeSettingExistence>(json);
			if (settingExistence.IsTransactionTypeEnabled)
			{
				IActionResult resultCmd = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						domain = company.Sales.DomainFrom({body.DomainId});
						processors.DisableTransactionType('{processorId}', domain, {body.TransactionType});
					}}
				");
				if (!(resultCmd is OkObjectResult))
				{
					throw new GameEngineException($@"Error:{((ObjectResult)resultCmd).Value.ToString()}");
				}

				return resultCmd;
			}
			else
			{
				return PrefabBadRequest("Sorry, transaction setting cannot be disabled");
			}
		}

		[HttpPut("api/marketplace/settings/domains/{domainId}/processors")]
		[Authorize(Roles = "c64")]
		public async Task<IActionResult> EnableProcessorInADomainAsync(int domainId, [FromBody]ProcessorbyDomain payload)
		{
			string command = "";
			if (payload.Action == ProcessorbyDomain.Actions.Enable)
			{
				command = "processors.Enable(domain, processor);";
			}
			else if (payload.Action == ProcessorbyDomain.Actions.Disable)
			{
				command = "processors.Disable(domain, processor);";
			}
			else
			{
				return NotFound($"No {nameof(ProcessorbyDomain.Actions)} configured yet.");
			}

			string program = $@"
				{{
					domain = company.Sales.DomainFrom({domainId});
					processor = processors.SearchById('{payload.ProcessorId}');
					{command}
				}}
				";
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, program);
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			return result;
		}


		public ContentResult PrefabBadRequest(string message, string stack = "No stack.", string comando = "No command error info.", string info = "No Error info.")
		{
			ApiError error = new ApiError(message,
					stack,
					comando,
					info);

			var contentResult = new ContentResult
			{
				ContentType = "application/json",
				Content = JsonConvert.SerializeObject(error),
				StatusCode = 400
			};
			return contentResult;
		}


        [HttpGet("api/marketplace/store")]
        [AllowAnonymous]
        public async Task<IActionResult> StoreIdAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					store = company.Sales.CurrentStore;
                    print store.Id storeId;
                    print store.SubscriberId from;
                }}
            ");
			return result;
		}

		[DataContract(Name = "WithdrawalsSettings")]
		public class WithdrawalsSetting
		{
			[DataMember(Name = "areAllWithdrawalsManagedAsDisbursements")]
			public bool AreAllWithdrawalsManagedAsDisbursements { get; set; }
		}

		[DataContract(Name = "transactionTypeAllowed")]
		public class TransactionTypeAllowed
		{
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
			[DataMember(Name = "enable")]
			public bool Enable { get; set; }
			[DataMember(Name = "domain")]
			public string Domain { get; set; }
		}

		[DataContract(Name = "accounts")]
		public class Accounts
		{
			[DataMember(Name = "accountsIn")]
			public List<Account> AdminAccountsIn { get; set; }
			[DataMember(Name = "accountsOut")]
			public List<Account> AdminAccountsOut { get; set; }
		}

		[DataContract(Name = "account")]
		public class Account
		{
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
		}

		[DataContract(Name = "TransactionSettingBody")]
		public class TransactionSettingBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
		}

		[DataContract(Name = "NotificationSettingBody")]
		public class NotificationSettingBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
			[DataMember(Name = "status")]
			public TransactionStatus Status { get; set; }
		}

		[DataContract(Name = "NotificationSettingExistence")]
		public class NotificationSettingExistence
		{
			[DataMember(Name = "existsSetting")]
			public bool ExistsSetting { get; set; }
			[DataMember(Name = "isNotificationEnabled")]
			public bool IsNotificationEnabled { get; set; }
		}

		[DataContract(Name = "TransactionTypeSettingExistence")]
		public class TransactionTypeSettingExistence
		{
			[DataMember(Name = "existsSetting")]
			public bool ExistsSetting { get; set; }
			[DataMember(Name = "isTransactionTypeEnabled")]
			public bool IsTransactionTypeEnabled { get; set; }
		}

		[DataContract(Name = "ProcessorbyDomain")]
		public class ProcessorbyDomain
		{
			public enum Actions { Enable=0, Disable=1}
			[DataMember(Name = "action")]
			public Actions Action { get; set; }
			[DataMember(Name = "processorId")]
			public string ProcessorId { get; set; }
		}

		[DataContract(Name = "CustomParameterExistence")]
		public class CustomParameterExistence
		{
			[DataMember(Name = "existsCustomParameter")]
			public bool ExistsCustomParameter { get; set; }
		}

		[DataContract(Name = "CustomFeeExistence")]
		public class CustomFeeExistence
		{
			[DataMember(Name = "isParameterAddedAsFee")]
			public bool IsParameterAddedAsFee { get; set; }
		}

		[DataContract(Name = "EnabledOrDisabledJournalEntryTemplateBody")]
		public class EnabledOrDisabledJournalEntryTemplateBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
		}

		[DataContract(Name = "EnabledOrDisabledFeeBody")]
		public class EnabledOrDisabledFeeBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
		}

		[DataContract(Name = "CustomFeeBody")]
		public class CustomFeeBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
			[DataMember(Name = "name")]
			public string Name { get; set; }
		}

		[DataContract(Name = "CustomParameterBody")]
		public class CustomParameterBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
			[DataMember(Name = "name")]
			public string Name { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "formula")]
			public string Formula { get; set; }
		}

		[DataContract(Name = "JournalEntryLinesBody")]
		public class JournalEntryLinesBody
		{
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "transactionType")]
			public string TransactionType { get; set; }
			[DataMember(Name = "journalEntryLines")]
			public List<JournalEntryLineBody> JournalEntryLines { get; set; }
		}

		[DataContract(Name = "JournalEntryLineBody")]
		public class JournalEntryLineBody
		{
			[DataMember(Name = "name")]
			public string Name { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "isDebit")]
			public bool IsDebit { get; set; }
		}
	}
}
