﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal struct Bulleye
    {
        private readonly bool isEnable;

        public bool Exists { get { return isEnable;  } }

        internal Bulleye(bool isEnable)
        {
            this.isEnable = isEnable;
        }

        public static bool operator ==(Bulleye obj1, Bulleye obj2)
        {
            return obj1.isEnable == obj2.isEnable;
        }

        public static bool operator !=(Bulleye obj1, Bulleye obj2)
        {
            return obj1.isEnable != obj2.isEnable;
        }

        public override string ToString()
        {
            return (isEnable) ? "1" : "0";
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }
        internal int ToInt()
        {
            return (isEnable) ? 1 : 0;
        }
    }
}
