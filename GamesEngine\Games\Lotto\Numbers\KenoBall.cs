﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lotto
{
    internal struct KenoBall
    {
        private readonly byte number;

        internal KenoBall(int number)
        {
            if (number < 1 || number > 80) throw new GameEngineException($"White numbers must be from 1 to 80. {number} is not valid.");
            this.number = (byte) number;
        }
        public static bool operator ==(KenoBall obj1, KenoBall obj2)
        {
            return obj1.number == obj2.number;
        }

        public static bool operator !=(KenoBall obj1, KenoBall obj2)
        {
            return obj1.number != obj2.number;
        }

        public override string ToString()
        {
            return $"{number}";
        }

        internal bool LowerOrEqualsThan(KenoBall pair1)
        {
            return number <= pair1.number;
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        private static KenoBall GenerateOneRandom()
        {
            var random = new Random();
            int number = random.Next(1,80);
            return new KenoBall(number);
        }

        internal static KenoBall GenerateOneRandomDistinctOf(IEnumerable<KenoBall> whiteBalls)
        {
            KenoBall whiteBall;
            do
            {
                whiteBall = GenerateOneRandom();
            } while (whiteBalls.Contains(whiteBall));

            return whiteBall;
        }

        internal int ToInt32()
        {
            return this.number;
        }
    }
}
