﻿using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text;

namespace ASIStub.Controllers
{
    public class ConsigmentsStub : AuthorizeController
	{
		static int authorization = 100;

		[HttpPost("")]
		[AllowAnonymous]
		public string ConsignmentsWebServices(string op)
		{
			authorization++;
			switch (op)
            {
				case "Add_deposit":
					StringBuilder builder = new StringBuilder();
					builder.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
					builder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
					builder.AppendLine("<soap:Body>");
					builder.AppendLine("<Add_depositResponse xmlns=\"http://tempuri.org/\">");
					builder.AppendLine($"<Add_depositResult>{authorization}</Add_depositResult>");
					builder.AppendLine("</Add_depositResponse>");
					builder.AppendLine("</soap:Body>");
					builder.AppendLine("</soap:Envelope>");
					return builder.ToString();
				case "Add_payout":
					builder = new StringBuilder();
					builder.AppendLine("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
					builder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
					builder.AppendLine("<soap:Body>");
					builder.AppendLine("<Add_payoutResponse xmlns=\"http://tempuri.org/\">");
					builder.AppendLine($"<Add_payoutResult>1,Payout Inserted,{authorization}</Add_payoutResult>");
					builder.AppendLine("</Add_payoutResponse>");
					builder.AppendLine("</soap:Body>");
					builder.AppendLine("</soap:Envelope>");
					return builder.ToString();
				default:
					return $"No service with op={op}";
			}
		}

	}
}
