﻿using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

namespace GamesEngine.Games.Lines
{
    public class ReceiverOfHistorical
    {
        internal const string TABLE_LINES_SCORES = "linesscores";
        internal const string TABLE_LINES_VERSIONS = "linesversions";
        internal const string TABLE_WAGERS_WINNERS = "wagerswinners";
        internal const string TABLE_WAGERS_LOSERS = "wagerslosers";
        internal const string TABLE_WAGERS_NOACTIONS = "wagersnoactions";
        internal const string TABLE_SCORES_PERIODS = "scoresPeriods";
        internal HistoricalDatabaseType DBType { get; private set; }

        private LinesStorage linesStorage;
        public void InitHistorical(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (dbType == HistoricalDatabaseType.MySQL)
            {
                linesStorage = new LinesStorageMySQL(connectionString);
                linesStorage.MakeSureThatTableExists();
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                linesStorage = new LinesStorageSQLServer(connectionString);
                linesStorage.MakeSureThatTableExists();
            }
            else if (dbType == HistoricalDatabaseType.InMemory)
            {
                linesStorage = new LinesStorageInMemory(connectionString);
                linesStorage.MakeSureThatTableExists();
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(LinesStorage)} implementation.");
            }
            DBType = dbType;
        }

        public void Receive(LinesScoreByPeriodsMessageGame message)
        {
            if (message == null) throw new GameEngineException($"{nameof(message)} cannot have default value");
            if (linesStorage == null) throw new GameEngineException($"Storage is not initialized.");

            linesStorage.InsertScoresByPeriods(
                message.TournamentId,
                message.GameNumber,
                message.TeamAName,
                message.TeamBName,
                message.TeamAShortName,
                message.TeamBShortName,
                message.Period,
                message.Moment,
                message.ScoreTeamA,
                message.ScoreTeamB,
                message.Who
            );
        }

        public void Receive(LinesScoreMessageGame message)
        {
            if (message == null) throw new GameEngineException($"{nameof(message)} cannot have default value");
            if (linesStorage == null) throw new GameEngineException($"Storage is not initialized.");

            linesStorage.InsertScores(
                message.TournamentId, 
                message.GameNumber, 
                message.TeamAName, 
                message.TeamAShortName, 
                message.TeamBName, 
                message.TeamBShortName, 
                message.Favorite,
                message.StartGameDate,
                message.EndGameDate,
                message.ScoreTeamA,
                message.ScoreTeamB,
                message.GradedBy,
                message.GradeDate,
                message.League,
                message.Sport,
                message.Lines
            );
        }

        public void Receive(LineLoserInfo message)
        {
            if (message == null) throw new GameEngineException($"{nameof(message)} cannot have default value");
            if (linesStorage == null) throw new GameEngineException($"Storage is not initialized.");

            linesStorage.InsertLoserWager(
                message.TournamentId,
                message.GameNumber,
                message.GradedBy,
                message.LineId,
                message.LineVersion,
                message.GradedDate,
                message.AuthorizationId,
                message.AccountNumber,
                message.Risk,
                message.ToWin,
                message.ChosenAnswer,
                message.FixedChosenAnswer,
                message.CreationDate,
                message.Grading
            );
        }

        public void Receive(LineWinnerInfo message)
        {
            if (message == null) throw new GameEngineException($"{nameof(message)} cannot have default value");
            if (linesStorage == null) throw new GameEngineException($"Storage is not initialized.");

            linesStorage.InsertWinnerWager(
                message.TournamentId,
                message.GameNumber,
                message.GradedBy,
                message.LineId,
                message.LineVersion,
                message.GradedDate,
                message.AuthorizationId,
                message.AccountNumber,
                message.Risk,
                message.ToWin,
                message.ChosenAnswer,
                message.FixedChosenAnswer,
                message.CreationDate,
                message.Grading
            );
        }

        public void Receive(LineNoActionInfo message)
        {
            if (message == null) throw new GameEngineException($"{nameof(message)} cannot have default value");
            if (linesStorage == null) throw new GameEngineException($"Storage is not initialized.");

            linesStorage.InsertNoActionWager(
                message.TournamentId,
                message.GameNumber, 
                message.GradedBy, 
                message.LineId, 
                message.LineVersion,
                message.GradedDate,
                message.AuthorizationId,
                message.AccountNumber,
                message.Risk,
                message.ToWin,
                message.ChosenAnswer,
                message.FixedChosenAnswer,
                message.CreationDate,
                message.Grading
            );
        }

        private abstract class LinesStorage
        {
            protected readonly string connectionString;

            protected string INSERT_VERSIONS_CMD;

            protected LinesStorage(string connectionString)
            {
                this.connectionString = connectionString;
            }

            internal abstract void MakeSureThatTableExists();

            internal abstract void InsertScores(
                int tournamentId, 
                int gameNumber, 
                string teamAName, 
                string teamAShortName,
                string teamBName,
                string teamBShortName,
                int favorite,
                DateTime startGameDate,
                DateTime endGameDate,
                int scoreTeamA,
                int scoreTeamB,
                string gradedBy,
                DateTime gradeDate,
                string league,
                string sport,
                IEnumerable<LineInfo> lines
            );

            internal abstract void InsertLoserWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus
            );
            
            internal abstract void InsertWinnerWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus
            );

            internal abstract void InsertNoActionWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus
            );

            internal abstract void InsertScoresByPeriods(int tournamentId,
                int gameNumber,
                string teamAName,
                string teamBName,
                string teamAShortname,
                string teamBShortname,
                string period,
                DateTime moment,
                int scoreTeamA,
                int scoreTeamB,
                string who
            );
        }

		private class LinesStorageInMemory : LinesStorage
		{
            internal LinesStorageInMemory(string connectionString) : base(connectionString)
			{
			}

			internal override void InsertLoserWager(int tournamentId, int gameNumber, string gradedBy, int lineId, int lineVersion, DateTime gradedDate, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creationDate, GameboardStatus gradingStatus)
			{
			
			}

			internal override void InsertNoActionWager(int tournamentId, int gameNumber, string gradedBy, int lineId, int lineVersion, DateTime gradedDate, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creationDate, GameboardStatus gradingStatus)
			{
			
			}

			internal override void InsertScores(int tournamentId, int gameNumber, string teamAName, string teamAShortName, string teamBName, string teamBShortName, int favorite, DateTime startGameDate, DateTime endGameDate, int scoreTeamA, int scoreTeamB, string gradedBy, DateTime gradeDate, string league, string sport, IEnumerable<LineInfo> lines)
			{
	
			}

			internal override void InsertScoresByPeriods(int tournamentId, int gameNumber, string teamAName, string teamBName, string teamAShortname, string teamBShortname, string period, DateTime moment, int scoreTeamA, int scoreTeamB, string who)
			{
				
			}

			internal override void InsertWinnerWager(int tournamentId, int gameNumber, string gradedBy, int lineId, int lineVersion, DateTime gradedDate, int authorizationId, string accountNumber, decimal risk, decimal toWin, string chosenAnswer, string fixedChosenAnswer, DateTime creationDate, GameboardStatus gradingStatus)
			{
	
			}

			internal override void MakeSureThatTableExists()
			{

			}
		}
		private class LinesStorageMySQL : LinesStorage
        {
            internal LinesStorageMySQL(string connectionString) : base(connectionString)
            {

            }

            private void ExecuteCommand(string cmdText)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(cmdText, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void MakeSureThatTableExists()
            {
                if (!ExistTable())
                {
                    CreateStorage();
                }
            }

            private bool ExistTable()
            {
                bool exists = true;
                string sql = $"SELECT 1 FROM {TABLE_LINES_SCORES} LIMIT 1";
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{sql} type:{e.GetType()} error:{e.Message}", e);
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            private string EscapeDoubleQoute(string text)
            {
                var result = text.IndexOf('"') == -1 ? text : text.Replace("\"", "\\\"");
                return result;
            }

            private string ToDateString(int yyyy, int mm, int dd, int hh, int min, int s)
            {
                DateTime result = new DateTime(yyyy, mm, dd, hh, min, s);
                return result.ToString("yyyy-MM-dd HH:mm:ss");
            }

            internal override void InsertScores(
                int tournamentId,
                int gameNumber,
                string teamAName,
                string teamAShortName,
                string teamBName,
                string teamBShortName,
                int favorite,
                DateTime startGameDate,
                DateTime endGameDate,
                int scoreTeamA,
                int scoreTeamB,
                string gradedBy,
                DateTime gradeDate,
                string league,
                string sport,
                IEnumerable<LineInfo> lines
            )
            {
                StringBuilder statement = new StringBuilder();
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;
              
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LINES_SCORES)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LINES_VERSIONS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_WINNERS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_LOSERS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_NOACTIONS)
                    .Append(" SET TIMESTAMP = TIMESTAMP + 1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_LINES_SCORES)
                    .Append(" (TOURNAMENT, GAME, TEAMANAME, TEAMBNAME, TEAMASHORTNAME,")
                    .Append(" TEAMBSHORTNAME, FAVORITE, STARTGAMEDATE, ENDGAMEDATE,")
                    .Append(" SCORETEAMA, SCORETEAMB, GRADEDBY, GRADEDATE, TIMESTAMP, LEAGUE, SPORT) Values (")
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"').Append(EscapeDoubleQoute(teamAName)).Append('"').Append(',')
                    .Append('"').Append(EscapeDoubleQoute(teamBName)).Append('"').Append(',')
                    .Append('"').Append(EscapeDoubleQoute(teamAShortName)).Append('"').Append(',')
                    .Append('"').Append(EscapeDoubleQoute(teamBShortName)).Append('"').Append(',')
                    .Append(favorite).Append(',')
                    .Append('\'').Append( ToDateString(startGameDate.Year, startGameDate.Month, startGameDate.Day, startGameDate.Hour, startGameDate.Minute, 0) ).Append("',")
                    .Append('\'').Append( ToDateString(endGameDate.Year, endGameDate.Month, endGameDate.Day, endGameDate.Hour, endGameDate.Minute, 0) ).Append("',")
                    .Append(scoreTeamA).Append(',')
                    .Append(scoreTeamB).Append(',')
                    .Append('"').Append(EscapeDoubleQoute(gradedBy)).Append('"').Append(',')
                    .Append('\'').Append( ToDateString(gradeDate.Year, gradeDate.Month, gradeDate.Day, gradeDate.Hour, gradeDate.Minute, 0) ).Append("',")
                    .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                    .Append('"').Append(EscapeDoubleQoute(league)).Append('"').Append(',')
                    .Append('"').Append(EscapeDoubleQoute(sport)).Append('"')
                    .Append(");");


                foreach (LineInfo line in lines)
                {
                    switch (line)
                    {
                        case MoneyDrawLineInfo mdl:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TIEREWARD, TEAMBREWARD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.MONEYDRAW_LINE).Append(',')
                                .Append(mdl.LineId).Append(',')
                                .Append(mdl.Version).Append(',')
                                .Append(mdl.TeamAReward).Append(',')
                                .Append(mdl.TieReward).Append(',')
                                .Append(mdl.TeamBReward).Append(',')
                                .Append($"\"{mdl.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(mdl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(mdl.QuestionId)
                                .Append(");");
                            break;

                        case MoneyLineInfo ml:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TEAMBREWARD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.MONEY_LINE).Append(',')
                                .Append(ml.LineId).Append(',')
                                .Append(ml.Version).Append(',')
                                .Append(ml.TeamAReward).Append(',')
                                .Append(ml.TeamBReward).Append(',')
                                .Append($"\"{ml.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(ml.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(ml.QuestionId)
                                .Append(");");
                            break;

                        case SpreadLineInfo sl:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TEAMBREWARD, SPREAD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.SPREAD_LINE).Append(',')
                                .Append(sl.LineId).Append(',')
                                .Append(sl.Version).Append(',')
                                .Append(sl.TeamAReward).Append(',')
                                .Append(sl.TeamBReward).Append(',')
                                .Append(sl.Spread).Append(',')
                                .Append($"\"{sl.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(sl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(sl.QuestionId)
                                .Append(");");
                            break;
                        case TotalPointsLineInfo tP:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" OVERREWARD, UNDERREWARD, SPREAD, OVERUNDERANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.TOTAL_POINTS_LINE).Append(',')
                                .Append(tP.LineId).Append(',')
                                .Append(tP.Version).Append(',')
                                .Append(tP.OverReward).Append(',')
                                .Append(tP.UnderReward).Append(',')
                                .Append(tP.Score).Append(',')
                                .Append('"').Append(tP.TheAnswer).Append('"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(tP.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(tP.QuestionId)
                                .Append(");");
                            break;
                        case YesNoLineInfo yn:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" YESREWARD, NOREWARD, TEXT, YESNOANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.YES_NO_LINE).Append(',')
                                .Append(yn.LineId).Append(',')
                                .Append(yn.Version).Append(',')
                                .Append(yn.YesReward).Append(',')
                                .Append(yn.NoReward).Append(',')
                                .Append('"' + EscapeDoubleQoute(yn.Text) + '"').Append(',')
                                .Append('"').Append(yn.TheAnswer).Append('"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(yn.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(yn.QuestionId)
                                .Append(");");
                            break;
                        case OverUnderLineInfo ou:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,") 
                                .Append(" OVERREWARD, UNDERREWARD, SPREAD, TEXT, OVERUNDERANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.OVER_UNDER_LINE).Append(',')
                                .Append(ou.LineId).Append(',')
                                .Append(ou.Version).Append(',')
                                .Append(ou.OverReward).Append(',')
                                .Append(ou.UnderReward).Append(',')
                                .Append(ou.Score).Append(',')
                                .Append('"' + EscapeDoubleQoute(ou.Text) + '"').Append(',')
                                .Append('"').Append(ou.TheAnswer).Append('"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(ou.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(ou.QuestionId)
                                .Append(");");
                            break;
                        case FixedLineInfo fl:
                            var options = string.Join(',', fl.Options);
                            var regards = string.Join(',', fl.Rewards);
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" FIXEDOPTIONS, FIXEDREWARDS, TEXT, FIXEDANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.FIXED_LINE).Append(',')
                                .Append(fl.LineId).Append(',')
                                .Append(fl.Version).Append(',')
                                .Append('"' + EscapeDoubleQoute(options) + '"').Append(',')
                                .Append('"' + regards + '"').Append(',')
                                .Append('"' + EscapeDoubleQoute(fl.Text) + '"').Append(',')
                                .Append('"' + EscapeDoubleQoute(fl.TheAnswer) + '"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(fl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(fl.QuestionId)
                                .Append(");");
                            break;
                        default:
                            throw new GameEngineException($"Line info {line.ToString()} has not been implemented.");
                    }
                }

                ExecuteCommand(statement.ToString());
                

                //TODO ver que hacia kenneth para dejar consistente como la loto. AABB5
                //recordsForReportsAccumulator.CheckIfUpdateIsRequiredForDailyTotalProfitReport();

            }

            internal override void InsertLoserWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer, 
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
			{
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeDoubleQoute(fixedChosenAnswer);
                }
                
                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_LOSERS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeDoubleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append($"'{ ToDateString(gradedDate.Year, gradedDate.Month, gradedDate.Day, gradedDate.Hour, gradedDate.Minute, 0)}'").Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeDoubleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append($"'{ ToDateString(creationDate.Year, creationDate.Month, creationDate.Day, creationDate.Hour, creationDate.Minute, 0)}'").Append(',')
                    .Append(0).Append(',')
                    .Append('"').Append(gradingStatus.GradingAsChar()).Append('"')
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            internal override void InsertWinnerWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
			{
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeDoubleQoute(fixedChosenAnswer);
                }

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_WINNERS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeDoubleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append($"'{ ToDateString(gradedDate.Year, gradedDate.Month, gradedDate.Day, gradedDate.Hour, gradedDate.Minute, 0)}'").Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeDoubleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append($"'{ ToDateString(creationDate.Year, creationDate.Month, creationDate.Day, creationDate.Hour, creationDate.Minute, 0)}'").Append(',')
                    .Append(0).Append(',')
                    .Append('"').Append(gradingStatus.GradingAsChar()).Append('"')
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            internal override void InsertNoActionWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
            {
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeDoubleQoute(fixedChosenAnswer);
                }

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_NOACTIONS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeDoubleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append($"'{ ToDateString(gradedDate.Year, gradedDate.Month, gradedDate.Day, gradedDate.Hour, gradedDate.Minute, 0)}'").Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeDoubleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append($"'{ ToDateString(creationDate.Year, creationDate.Month, creationDate.Day, creationDate.Hour, creationDate.Minute, 0)}'").Append(',')
                    .Append(0).Append(',')
                    .Append('"').Append(gradingStatus.GradingAsChar()).Append('"')
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            private void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_LINES_VERSIONS)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("LINETYPE SMALLINT UNSIGNED NOT NULL, ")
                    .Append("LINE SMALLINT NOT NULL, ")
                    .Append("LINEVERSION SMALLINT NOT NULL, ")

                    .Append("TEAMAREWARD SMALLINT NULL, ")
                    .Append("TIEREWARD SMALLINT NULL, ")
                    .Append("TEAMBREWARD SMALLINT NULL, ")
                    .Append("SPREAD DECIMAL(6, 2) NULL, ")

                    .Append("ABANSWER CHAR(1) NULL, ")

                    .Append("YESREWARD SMALLINT NULL, ")
                    .Append("NOREWARD SMALLINT NULL, ")

                    .Append("YESNOANSWER CHAR(1) NULL, ")

                    .Append("OVERREWARD SMALLINT NULL, ")
                    .Append("UNDERREWARD SMALLINT NULL, ")

                    .Append("OVERUNDERANSWER CHAR(1) NULL, ")

                    .Append("FIXEDOPTIONS TEXT NULL, ")
                    .Append("FIXEDREWARDS TEXT NULL, ")

                    .Append("FIXEDANSWER TEXT NULL, ")

                    .Append("TEXT TEXT NULL, ")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL, ")
                    .Append("QUESTIONID INT UNSIGNED NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;")


                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_LINES_SCORES)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("TEAMANAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMBNAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMASHORTNAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMBSHORTNAME VARCHAR(100) NOT NULL, ")
                    .Append("FAVORITE INT UNSIGNED NOT NULL, ")
                    .Append("STARTGAMEDATE DATETIME NOT NULL, ")
                    .Append("ENDGAMEDATE DATETIME NOT NULL, ")
                    .Append("SCORETEAMA SMALLINT NOT NULL, ")
                    .Append("SCORETEAMB SMALLINT NOT NULL, ")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL, ")
                    .Append("GRADEDATE DATETIME NOT NULL, ")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("LEAGUE VARCHAR(100) NOT NULL, ")
                    .Append("SPORT VARCHAR(100) NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;")


                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_SCORES_PERIODS)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("TEAMANAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMBNAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMASHORTNAME VARCHAR(100) NOT NULL, ")
                    .Append("TEAMBSHORTNAME VARCHAR(100) NOT NULL, ")
                    .Append("PERIOD VARCHAR(50) NOT NULL, ")
                    .Append("MOMENT DATETIME NOT NULL, ")
                    .Append("SCORETEAMA SMALLINT NOT NULL, ")
                    .Append("SCORETEAMB SMALLINT NOT NULL, ")
                    .Append("WHO VARCHAR(50) NOT NULL, ")
                    .Append("TIMESTAMP SMALLINT NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;")


                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_WAGERS_WINNERS)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL, ")
                    .Append("LINE SMALLINT NOT NULL, ")
                    .Append("LINEVERSION SMALLINT NOT NULL, ")
                    .Append("GRADEDDATE DATETIME NOT NULL, ")
                    .Append("AUTHORIZATIONID INT UNSIGNED NOT NULL, ")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL, ")
                    .Append("RISK DECIMAL(6, 2) NULL, ")
                    .Append("TOWIN DECIMAL(6, 2) NULL, ")
                    .Append("CHOSENANSWER CHAR(1) NULL,")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;")

                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_WAGERS_LOSERS)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL, ")
                    .Append("LINE SMALLINT NOT NULL, ")
                    .Append("LINEVERSION SMALLINT NOT NULL, ")
                    .Append("GRADEDDATE DATETIME NOT NULL, ")
                    .Append("AUTHORIZATIONID INT UNSIGNED NOT NULL, ")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL, ")
                    .Append("RISK DECIMAL(6, 2) NULL, ")
                    .Append("TOWIN DECIMAL(6, 2) NULL, ")
                    .Append("CHOSENANSWER CHAR(1) NULL,")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;")

                    .Append("CREATE TABLE IF NOT EXISTS `").Append(TABLE_WAGERS_NOACTIONS)
                    .Append("` ( ")
                    .Append("TOURNAMENT INT UNSIGNED NOT NULL, ")
                    .Append("GAME INT UNSIGNED NOT NULL, ")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL, ")
                    .Append("LINE SMALLINT NOT NULL, ")
                    .Append("LINEVERSION SMALLINT NOT NULL, ")
                    .Append("GRADEDDATE DATETIME NOT NULL, ")
                    .Append("AUTHORIZATIONID INT UNSIGNED NOT NULL, ")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL, ")
                    .Append("RISK DECIMAL(6, 2) NULL, ")
                    .Append("TOWIN DECIMAL(6, 2) NULL, ")
                    .Append("CHOSENANSWER CHAR(1) NULL,")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(") ENGINE=InnoDB CHARSET=utf8;");




                statement.AppendLine($@"CREATE INDEX idx_{TABLE_LINES_SCORES}
                ON {TABLE_LINES_SCORES}(timestamp, tournament, game);

                CREATE INDEX idx_{TABLE_LINES_VERSIONS}
                ON {TABLE_LINES_VERSIONS}(timestamp, tournament, game, line);

                CREATE INDEX idx_{TABLE_WAGERS_LOSERS}
                ON {TABLE_WAGERS_LOSERS}(timestamp, accountnumber, tournament, game, line);

                CREATE INDEX idx_{TABLE_WAGERS_WINNERS}
                ON {TABLE_WAGERS_WINNERS}(timestamp, accountnumber, tournament, game, line);

                CREATE INDEX idx_{TABLE_WAGERS_NOACTIONS}
                ON {TABLE_WAGERS_NOACTIONS}(timestamp, accountnumber, tournament, game, line);");

                ExecuteCommand(statement.ToString());
            }

			internal override void InsertScoresByPeriods(int tournamentId, int gameNumber, string teamAName, string teamBName, string teamAShortname, string teamBShortname, string period, DateTime moment, int scoreTeamA, int scoreTeamB, string who)
			{
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_SCORES_PERIODS)
                    .Append(" (TOURNAMENT, GAME, TEAMANAME, TEAMBNAME, TEAMASHORTNAME, TEAMBSHORTNAME, " +
                    "PERIOD, MOMENT, SCORETEAMA, SCORETEAMB, WHO, TIMESTAMP) VALUES (")
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeDoubleQoute(teamAName) + '"').Append(',')
                    .Append('"' + EscapeDoubleQoute(teamBName) + '"').Append(',')
                    .Append('"' + EscapeDoubleQoute(teamAShortname) + '"').Append(',')
                    .Append('"' + EscapeDoubleQoute(teamBShortname) + '"').Append(',')
                    .Append('"' + EscapeDoubleQoute(period) + '"').Append(',')
                    .Append($"'{ ToDateString(moment.Year, moment.Month, moment.Day, moment.Hour, moment.Minute, 0)}'").Append(',')
                    .Append(scoreTeamA).Append(',')
                    .Append(scoreTeamB).Append(',')
                    .Append('"' + EscapeDoubleQoute(who) + '"').Append(',')
                    .Append(0)
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }
		}

        private class LinesStorageSQLServer : LinesStorage
        {
            internal LinesStorageSQLServer(string connectionString) : base(connectionString)
            {
            }

            private void ExecuteCommand(string cmdText)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(cmdText, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        Loggers.GetIntance().Db.Error($@"sql:{cmdText} type:{e.GetType()} error:{e.Message}", e);
                        throw new GameEngineException("SQLServer Error [" + cmdText + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }
            private static string EscapeSingleQoute(string text)
            {
                var result = text.IndexOf('\'') == -1 ? text : text.Replace("'", "''");
                return result;
            }

            internal override void InsertScores(
                int tournamentId,
                int gameNumber,
                string teamAName,
                string teamAShortName,
                string teamBName,
                string teamBShortName,
                int favorite,
                DateTime startGameDate,
                DateTime endGameDate,
                int scoreTeamA,
                int scoreTeamB,
                string gradedBy,
                DateTime gradeDate,
                string league,
                string sport,
                IEnumerable<LineInfo> lines
            )
            {
                StringBuilder statement = new StringBuilder();
                const int TIME_STAMP_THIS_IS_THE_CURRENT_VERSION = 0;

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LINES_SCORES)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_LINES_VERSIONS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_WINNERS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_LOSERS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("UPDATE ")
                    .Append(TABLE_WAGERS_NOACTIONS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_LINES_SCORES)
                    .Append(" (TOURNAMENT, GAME, TEAMANAME, TEAMBNAME, TEAMASHORTNAME,")
                    .Append(" TEAMBSHORTNAME, FAVORITE, STARTGAMEDATE, ENDGAMEDATE, SCORETEAMA,") 
                    .Append(" SCORETEAMB, GRADEDBY, GRADEDATE, TIMESTAMP, LEAGUE, SPORT) Values (")
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"').Append(EscapeSingleQoute(teamAName)).Append('"').Append(',')
                    .Append('"').Append(EscapeSingleQoute(teamBName)).Append('"').Append(',')
                    .Append('"').Append(EscapeSingleQoute(teamAShortName)).Append(',')
                    .Append('"').Append(EscapeSingleQoute(teamBShortName)).Append('"').Append(',')
                    .Append( favorite ).Append(',')
                    .Append(startGameDate).Append(',')
                    .Append(endGameDate).Append(',')
                    .Append(scoreTeamA).Append(',')
                    .Append(scoreTeamB).Append(',')
                    .Append('"').Append(EscapeSingleQoute(gradedBy)).Append('"').Append(',')
                    .Append(gradeDate).Append(',')
                    .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                    .Append('"').Append(EscapeSingleQoute(league)).Append('"').Append(',')
                    .Append('"').Append(EscapeSingleQoute(sport)).Append('"')
                    .Append(");");


                foreach (LineInfo line in lines)
                {
                    switch (lines)
                    {
                        case MoneyDrawLineInfo mdl:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TIEREWARD, TEAMBREWARD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.MONEY_LINE).Append(',')
                                .Append(mdl.LineId).Append(',')
                                .Append(mdl.Version).Append(',')
                                .Append(mdl.TeamAReward).Append(',')
                                .Append(mdl.TieReward).Append(',')
                                .Append(mdl.TeamBReward).Append(',')
                                .Append($"\"{mdl.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(mdl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(mdl.QuestionId)
                                .Append(");");
                            break;

                        case MoneyLineInfo ml:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TEAMBREWARD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.MONEY_LINE).Append(',')
                                .Append(ml.LineId).Append(',')
                                .Append(ml.Version).Append(',')
                                .Append(ml.TeamAReward).Append(',')
                                .Append(ml.TeamBReward).Append(',')
                                .Append($"\"{ml.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(ml.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(ml.QuestionId)
                                .Append(");");
                            break;

                        case SpreadLineInfo sl:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" TEAMAREWARD, TEAMBREWARD, SPREAD, ABANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.SPREAD_LINE).Append(',')
                                .Append(sl.LineId).Append(',')
                                .Append(sl.Version).Append(',')
                                .Append(sl.TeamAReward).Append(',')
                                .Append(sl.TeamBReward).Append(',')
                                .Append(sl.Spread).Append(',')
                                .Append($"\"{sl.TheAnswer}\"").Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(sl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(sl.QuestionId)
                                .Append(");");
                            break;
                        case TotalPointsLineInfo tP:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" OVERREWARD, UNDERREWARD, SPREAD, OVERUNDERANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.TOTAL_POINTS_LINE).Append(',')
                                .Append(tP.LineId).Append(',')
                                .Append(tP.Version).Append(',')
                                .Append(tP.OverReward).Append(',')
                                .Append(tP.UnderReward).Append(',')
                                .Append(tP.Score).Append(',')
                                .Append('"').Append(tP.TheAnswer).Append('"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(tP.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(tP.QuestionId)
                                .Append(");");
                            break;
                        case YesNoLineInfo yn:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" YESREWARD, NOREWARD, TEXT, YESNOANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.YES_NO_LINE).Append(',')
                                .Append(yn.LineId).Append(',')
                                .Append(yn.Version).Append(',')
                                .Append(yn.YesReward).Append(',')
                                .Append(yn.NoReward).Append(',')
                                .Append('"' + EscapeSingleQoute(yn.Text) + '"').Append(',')
                                .Append('"').Append(yn.TheAnswer).Append( '"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(yn.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(yn.QuestionId)
                                .Append(");");
                            break;
                        case OverUnderLineInfo ou:
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" OVERREWARD, UNDERREWARD, SPREAD, TEXT, OVERUNDERANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.OVER_UNDER_LINE).Append(',')
                                .Append(ou.LineId).Append(',')
                                .Append(ou.Version).Append(',')
                                .Append(ou.OverReward).Append(',')
                                .Append(ou.UnderReward).Append(',')
                                .Append(ou.Score).Append(',')
                                .Append('"' + EscapeSingleQoute(ou.Text) + '"').Append(',')
                                .Append('"').Append(ou.TheAnswer).Append('"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(ou.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(ou.QuestionId)
                                .Append(");");
                            break;
                        case FixedLineInfo fl:
                            var options = string.Join(',', fl.Options);
                            var regards = string.Join(',', fl.Rewards);
                            statement
                                .Append("INSERT INTO ")
                                .Append(TABLE_LINES_VERSIONS)
                                .Append(" (TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION,")
                                .Append(" FIXEDOPTIONS, FIXEDREWARDS, TEXT, FIXEDANSWER, TIMESTAMP, GRADINGSTATUS, QUESTIONID) VALUES (")
                                .Append(tournamentId).Append(',')
                                .Append(gameNumber).Append(',')
                                .Append((int)LineType.FIXED_LINE).Append(',')
                                .Append(fl.LineId).Append(',')
                                .Append(fl.Version).Append(',')
                                .Append('"' + EscapeSingleQoute(options) + '"').Append(',')
                                .Append('"' + regards + '"').Append(',')
                                .Append('"' + EscapeSingleQoute(fl.Text) + '"').Append(',')
                                .Append('"' + EscapeSingleQoute(fl.TheAnswer) + '"').Append(',')
                                .Append(TIME_STAMP_THIS_IS_THE_CURRENT_VERSION).Append(',')
                                .Append('"').Append(fl.Grading.GradingAsChar()).Append('"').Append(',')
                                .Append(fl.QuestionId)
                                .Append(");");
                            break;
                        default:
                            throw new GameEngineException($"Line info {line.ToString()} has not been implemented.");
                    }
                }

                ExecuteCommand(statement.ToString());
                
            }

            internal override void InsertLoserWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
            {
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeSingleQoute(fixedChosenAnswer);
                }

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_LOSERS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeSingleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append(gradedDate).Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeSingleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append(creationDate).Append(',')
                    .Append(0).Append(',')
                    .Append(gradingStatus)
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            internal override void InsertWinnerWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
            {
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeSingleQoute(fixedChosenAnswer);
                }

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_WINNERS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeSingleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append(gradedDate).Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeSingleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append(creationDate).Append(',')
                    .Append(0).Append(',')
                    .Append(gradingStatus)
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            internal override void InsertNoActionWager(int tournamentId,
                int gameNumber,
                string gradedBy,
                int lineId,
                int lineVersion,
                DateTime gradedDate,
                int authorizationId,
                string accountNumber,
                decimal risk,
                decimal toWin,
                string chosenAnswer,
                string fixedChosenAnswer,
                DateTime creationDate,
                GameboardStatus gradingStatus)
            {
                StringBuilder statement = new StringBuilder();
                var fieldsToInsert = string.Empty;
                var escapedChosenAnswer = string.Empty;
                if (fixedChosenAnswer == LineMessage.NO_DATA)
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, CHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = chosenAnswer;
                }
                else
                {
                    fieldsToInsert = "(TOURNAMENT, GAME, GRADEDBY, LINE, LINEVERSION, GRADEDDATE, AUTHORIZATIONID, ACCOUNTNUMBER, RISK, TOWIN, FIXEDCHOSENANSWER, CREATIONDATE, TIMESTAMP, GRADINGSTATUS) VALUES (";
                    escapedChosenAnswer = string.IsNullOrWhiteSpace(fixedChosenAnswer) ? string.Empty : EscapeSingleQoute(fixedChosenAnswer);
                }

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_WAGERS_NOACTIONS)
                    .Append(fieldsToInsert)
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeSingleQoute(gradedBy) + '"').Append(',')
                    .Append(lineId).Append(',')
                    .Append(lineVersion).Append(',')
                    .Append(gradedDate).Append(',')
                    .Append(authorizationId).Append(',')
                    .Append('"' + EscapeSingleQoute(accountNumber) + '"').Append(',')
                    .Append(risk).Append(',')
                    .Append(toWin).Append(',')
                    .Append('"' + escapedChosenAnswer + '"').Append(',')
                    .Append(creationDate).Append(',')
                    .Append(0).Append(',')
                    .Append(gradingStatus)
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }

            internal override void MakeSureThatTableExists()
            {
                CrearStorage();
                INSERT_VERSIONS_CMD = $"INSERT INTO {TABLE_LINES_SCORES}(TOURNAMENT, GAME, LINETYPE, LINE, LINEVERSION, TEAMAREWARD, TEAMBREWARD, TEAMAGANARPORALMENOS, TEAMBGANARPORALMENOS, ISACTIVE) VALUES ";
            }

            private void CrearStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append($"WHERE TABLE_NAME = '{TABLE_LINES_VERSIONS}')")
                    .Append("BEGIN")
                    .Append($"CREATE TABLE {TABLE_LINES_VERSIONS}")
                    .Append("(")
                    .Append("TOURNAMENT INT NOT NULL,")
                    .Append("GAME INT NOT NULL,")
                    .Append("LINETYPE SMALLINT NOT NULL,")
                    .Append("LINE SMALLINT NOT NULL,")
                    .Append("LINEVERSION SMALLINT NOT NULL,")

                    .Append("TEAMAREWARD SMALLINT NULL,")
                    .Append("TIEREWARD SMALLINT NULL,")
                    .Append("TEAMBREWARD SMALLINT NULL,")
                    .Append("SPREAD DECIMAL(6,2) NULL,")

                    .Append("ABANSWER CHAR(1) NULL, ")

                    .Append("YESREWARD SMALLINT NULL,")
                    .Append("NOREWARD SMALLINT NULL,")

                    .Append("YESNOANSWER CHAR(1) NULL, ")

                    .Append("OVERREWARD SMALLINT NULL,")
                    .Append("UNDERREWARD SMALLINT NULL,")

                    .Append("OVERUNDERANSWER CHAR(1) NULL, ")

                    .Append("FIXEDOPTIONS TEXT NULL, ")
                    .Append("FIXEDREWARDS TEXT NULL, ")

                    .Append("FIXEDANSWER TEXT NULL, ")

                    .Append("TEXT TEXT NULL, ")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append("QUESTIONID INT NOT NULL")
                    .Append(");")
                    .Append("END");

                statement
                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append($"WHERE TABLE_NAME = '{TABLE_LINES_SCORES}')")
                    .Append("BEGIN")
                    .Append($"CREATE TABLE {TABLE_LINES_SCORES}")
                    .Append("(")
                    .Append("TOURNAMENT INT NOT NULL,")
                    .Append("GAME INT NOT NULL,")
                    .Append("TEAMANAME VARCHAR(100) NOT NULL,")
                    .Append("TEAMBNAME VARCHAR(100) NOT NULL,")
                    .Append("TEAMASHORTNAME VARCHAR(100) NOT NULL,")
                    .Append("TEAMBSHORTNAME VARCHAR(100) NOT NULL,")
                    .Append("FAVORITE INT NOT NULL,")
                    .Append("STARTGAMEDATE DATETIME NOT NULL,")
                    .Append("ENDGAMEDATE DATETIME NOT NULL,")
                    .Append("SCORETEAMA SMALLINT NOT NULL,")
                    .Append("SCORETEAMB SMALLINT NOT NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("GRADEDDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL,")
                    .Append("LEAGUE VARCHAR(100) NOT NULL,")
                    .Append("SPORT VARCHAR(100) NOT NULL")
                    .Append(");")
                    .Append("END")

                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append($"WHERE TABLE_NAME = '{TABLE_WAGERS_WINNERS}')")
                    .Append("BEGIN")
                    .Append($"CREATE TABLE {TABLE_WAGERS_WINNERS}")
                    .Append("(")
                    .Append("TOURNAMENT INT NOT NULL,")
                    .Append("GAME INT NOT NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("LINE SMALLINT NOT NULL,")
                    .Append("LINEVERSION SMALLINT NOT NULL,")
                    .Append("GRADEDDATE DATETIME NOT NULL,")
                    .Append("AUTHORIZATIONID INT NOT NULL,")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL,")
                    .Append("RISK DECIMAL(6,2) NULL,")
                    .Append("TOWIN DECIMAL(6,2) NULL,")
                    .Append("CHOSENANSWER CHAR(1) NULL, ")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL, ")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(");")
                    .Append("END")

                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append($"WHERE TABLE_NAME = '{TABLE_WAGERS_LOSERS}')")
                    .Append("BEGIN")
                    .Append($"CREATE TABLE {TABLE_WAGERS_LOSERS}")
                    .Append("(")
                    .Append("TOURNAMENT INT NOT NULL,")
                    .Append("GAME INT NOT NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("LINE SMALLINT NOT NULL,")
                    .Append("LINEVERSION SMALLINT NOT NULL,")
                    .Append("GRADEDDATE DATETIME NOT NULL,")
                    .Append("AUTHORIZATIONID INT NOT NULL,")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL,")
                    .Append("RISK DECIMAL(6,2) NULL,")
                    .Append("TOWIN DECIMAL(6,2) NULL,")
                    .Append("CHOSENANSWER CHAR(1) NULL,")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(");")
                    .Append("END")

                    .Append("IF NOT EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append($"WHERE TABLE_NAME = '{TABLE_WAGERS_NOACTIONS}')")
                    .Append("BEGIN")
                    .Append($"CREATE TABLE {TABLE_WAGERS_NOACTIONS}")
                    .Append("(")
                    .Append("TOURNAMENT INT NOT NULL,")
                    .Append("GAME INT NOT NULL,")
                    .Append("GRADEDBY VARCHAR(50) NOT NULL,")
                    .Append("LINE SMALLINT NOT NULL,")
                    .Append("LINEVERSION SMALLINT NOT NULL,")
                    .Append("GRADEDDATE DATETIME NOT NULL,")
                    .Append("AUTHORIZATIONID INT NOT NULL,")
                    .Append("ACCOUNTNUMBER VARCHAR(100) NOT NULL,")
                    .Append("RISK DECIMAL(6,2) NULL,")
                    .Append("TOWIN DECIMAL(6,2) NULL,")
                    .Append("CHOSENANSWER CHAR(1) NULL,")
                    .Append("FIXEDCHOSENANSWER VARCHAR(25) NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("TIMESTAMP SMALLINT NOT NULL, ")
                    .Append("GRADINGSTATUS CHAR(1) NOT NULL")
                    .Append(");")
                    .Append("END");

                statement.AppendLine($@"
                IF NOT EXISTS(SELECT 1        
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_NAME='{TABLE_LINES_SCORES}' AND INDEX_NAME='idx_{TABLE_LINES_SCORES}') BEGIN
                CREATE INDEX idx_{TABLE_LINES_SCORES}
                ON {TABLE_LINES_SCORES}(timestamp, tournament, game);
                END

                IF NOT EXISTS(SELECT 1        
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_NAME='{TABLE_LINES_VERSIONS}' AND INDEX_NAME='idx_{TABLE_LINES_VERSIONS}') BEGIN
                CREATE INDEX idx_{TABLE_LINES_VERSIONS}
                ON {TABLE_LINES_VERSIONS}(timestamp, tournament, game, line);
                END

                IF NOT EXISTS(SELECT 1        
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_NAME='{TABLE_WAGERS_LOSERS}' AND INDEX_NAME='idx_{TABLE_WAGERS_LOSERS}') BEGIN
                CREATE INDEX idx_{TABLE_WAGERS_LOSERS}
                ON {TABLE_WAGERS_LOSERS}(timestamp, accountnumber, tournament, game, line);
                END

                IF NOT EXISTS(SELECT 1        
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_NAME='{TABLE_WAGERS_WINNERS}' AND INDEX_NAME='idx_{TABLE_WAGERS_WINNERS}') BEGIN
                CREATE INDEX idx_{TABLE_WAGERS_WINNERS}
                ON {TABLE_WAGERS_WINNERS}(timestamp, accountnumber, tournament, game, line);
                END

                IF NOT EXISTS(SELECT 1        
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_NAME='{TABLE_WAGERS_NOACTIONS}' AND INDEX_NAME='idx_{TABLE_WAGERS_NOACTIONS}') BEGIN
                CREATE INDEX idx_{TABLE_WAGERS_NOACTIONS}
                ON {TABLE_WAGERS_NOACTIONS}(timestamp, accountnumber, tournament, game, line);
                END
                ");

                ExecuteCommand(statement.ToString());
                
            }

			internal override void InsertScoresByPeriods(int tournamentId, int gameNumber, string teamAName, string teamBName, string teamAShortname, string teamBShortname, string period, DateTime moment, int scoreTeamA, int scoreTeamB, string who)
			{
                StringBuilder statement = new StringBuilder();
                statement
                    .Append("UPDATE ")
                    .Append(TABLE_SCORES_PERIODS)
                    .Append(" SET TIMESTAMP=TIMESTAMP+1 ")
                    .Append("WHERE TOURNAMENT=")
                    .Append(tournamentId)
                    .Append(" AND GAME=")
                    .Append(gameNumber).Append(';');

                statement
                    .Append("INSERT INTO ")
                    .Append(TABLE_SCORES_PERIODS)
                    .Append(" (TOURNAMENT, GAME, TEAMANAME, TEAMBNAME, TEAMASHORTNAME, TEAMBSHORTNAME, " +
                    "PERIOD, MOMENT, SCORETEAMA, SCORETEAMB, WHO, TIMESTAMP) VALUES (")
                    .Append(tournamentId).Append(',')
                    .Append(gameNumber).Append(',')
                    .Append('"' + EscapeSingleQoute(teamAName) + '"').Append(',')
                    .Append('"' + EscapeSingleQoute(teamBName) + '"').Append(',')
                    .Append('"' + EscapeSingleQoute(teamAShortname) + '"').Append(',')
                    .Append('"' + EscapeSingleQoute(teamBShortname) + '"').Append(',')
                    .Append('"' + EscapeSingleQoute(period) + '"').Append(',')
                    .Append(moment).Append(',')
                    .Append(scoreTeamA).Append(',')
                    .Append(scoreTeamB).Append(',')
                    .Append('"' + EscapeSingleQoute(who) + '"').Append(',')
                    .Append(0)
                    .Append(");");

                ExecuteCommand(statement.ToString());
            }
		}
    }
}