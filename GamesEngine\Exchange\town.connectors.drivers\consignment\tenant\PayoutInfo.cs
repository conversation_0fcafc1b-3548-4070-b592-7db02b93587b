﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class PayoutInfo : ConsignmentTenantDriver
    {
        public PayoutInfo() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetPayoutInfoAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<PayoutInfoResponse> GetPayoutInfoAsync(CustomSettings.RecordSet recordSet)
        {
            ResponsePayoutInfoEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Get_PayoutInfo";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetPayoutInfo.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetPayoutInfo.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponsePayoutInfoEnvelope));
                responseXml = (ResponsePayoutInfoEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetPayoutInfo.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetPayoutInfoAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.Get_PayoutInfoResponse.Get_PayoutInfoResult))
            {
                NotifyWarn(nameof(GetPayoutInfoAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                XmlSerializer serializer = new XmlSerializer(typeof(PayoutInfoResponse));
                StringReader reader = new StringReader(responseXml.Body.Get_PayoutInfoResponse.Get_PayoutInfoResult);
                var result = (PayoutInfoResponse)serializer.Deserialize(reader);
                return result;
            }

            return new PayoutInfoResponse();
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var id = recordSet.Mappings["id"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Get_PayoutInfo xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<id>").Append(id).AppendLine("</id>");
            xmlBuilder.AppendLine("</Get_PayoutInfo>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Get_PayoutInfo xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<id>").Append(id).AppendLine("</id>");
            xmlBuilderWithHiddenFields.AppendLine("</Get_PayoutInfo>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponsePayoutInfoEnvelope
        {
            public ResponsePayoutInfoBody Body { get; set; }
        }

        public class ResponsePayoutInfoBody
        {
            [XmlElement(ElementName = "Get_PayoutInfoResponse", Namespace = "http://tempuri.org/")]
            public GetPayoutInfoResponse Get_PayoutInfoResponse { get; set; }
        }

        public class GetPayoutInfoResponse
        {
            public string Get_PayoutInfoResult { get; set; }
        }

        [XmlRoot("payout")]
        public class PayoutInfoResponse
        {
            [XmlElement("Id_Status")]
            public string StatusId { get; set; }
            [XmlElement("statusName")]
            public string StatusName { get; set; }
            [XmlElement("notes")]
            public string Notes { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("id");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
