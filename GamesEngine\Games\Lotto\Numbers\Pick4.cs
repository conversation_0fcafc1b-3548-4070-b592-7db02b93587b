﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    public struct Pick4 : IPick
	{
        private readonly Pattern number1;
        private readonly <PERSON>tern number2;
        private readonly Pattern number3;
        private readonly <PERSON>tern number4;

        private Pick4 (<PERSON>tern number1, <PERSON><PERSON> number2, <PERSON><PERSON> number3, <PERSON>tern number4)
        {
            this.number1 = number1;
            this.number2 = number2;
            this.number3 = number3;
            this.number4 = number4;
            this.subticket = null;
        }

		public Pick4(string number1, string number2, string number3, string number4) :
			this(new <PERSON><PERSON>(number1), new <PERSON><PERSON>(number2), new <PERSON><PERSON>(number3), new <PERSON>tern(number4))
		{ }

		public Pick4(int number1, int number2, int number3, int number4) :
			this(new <PERSON><PERSON>(number1), new <PERSON><PERSON>(number2), new <PERSON><PERSON>(number3), new <PERSON>tern(number4))
		{ }

        public Pick4(string sequenceOfNumbers)
        {
            if (sequenceOfNumbers == null) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 4) throw new GameEngineException($"Number '{sequenceOfNumbers}' is not a valid {nameof(Pick4)} Lottery number");
            char digit1 = sequenceOfNumbers[0];
            char digit2 = sequenceOfNumbers[1];
            char digit3 = sequenceOfNumbers[2];
            char digit4 = sequenceOfNumbers[3];
            number1 = new Pattern(digit1);
            number2 = new Pattern(digit2);
            number3 = new Pattern(digit3);
            number4 = new Pattern(digit4);
            subticket = null;
        }

        Pattern IPick.this[int i] => this[i];
        internal Pattern this[int i]
        {
            get
            {
                if (i == 1) return number1;
                if (i == 2) return number2;
                if (i == 3) return number3;
                if (i == 4) return number4;
                throw new GameEngineException($"{nameof(Pick4)} does not have index {i}");
            }
        }

        IEnumerable<IPick> IPick.Permute() => Permute();
        internal IEnumerable<IPick> Permute()
        {
            if (Count == 1)
            {
                var result = new HashSet<IPick>();
                int[] idx = new int[4];
                Pattern[] values = new Pattern[] { number1, number2, number3, number4 };
                for (idx[0] = 0; idx[0] < 4; idx[0]++)
                {
                    for (idx[1] = 0; idx[1] < 4; idx[1]++)
                    {
                        for (idx[2] = 0; idx[2] < 4; idx[2]++)
                        {
                            for (idx[3] = 0; idx[3] < 4; idx[3]++)
                            {
                                if (allIndexesAreDifferent(idx))
                                {
                                    var pick = new Pick4(values[idx[0]], values[idx[1]], values[idx[2]], values[idx[3]]);
                                    result.Add(pick);
                                }
                            }
                        }
                    }
                }
                return result;
            }
            else
            {
                var result = new List<IPick>();
                foreach (var subticket in SubTickets())
                {
                    result.AddRange(subticket.Permute());
                }
                return result;
            }
        }

        internal bool allIndexesAreDifferent(int[] indexes)
        {
            for (int i = 0; i < indexes.Length; i++)
            {
                for (int j = 0; j < indexes.Length; j++)
                {
                    if (i != j && indexes[i] == indexes[j])
                    {
                        return false;
                    }
                }
            }
            return true;
        }

        internal bool IsBoxed(int digit1, int digit2, int digit3, int digit4)
        {
            int[] idx = new int[4];
            int[] values = new int[] { digit1, digit2, digit3, digit4};
            for (idx[0] = 0; idx[0] < 4; idx[0]++)
            {
                for (idx[1] = 0; idx[1] < 4; idx[1]++)
                {
                    for (idx[2] = 0; idx[2] < 4; idx[2]++)
                    {
                        for (idx[3] = 0; idx[3] < 4; idx[3]++)
                        {
                            if (allIndexesAreDifferent(idx) && IsMarked(values[idx[0]], values[idx[1]], values[idx[2]], values[idx[3]]))
                            {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }

        internal bool IsMarked(int digit1, int digit2, int digit3, int digit4)
        {
            bool result = number1.IsMarked(digit1) && number2.IsMarked(digit2) && number3.IsMarked(digit3) && number4.IsMarked(digit4);
            return result;
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            if (Count == 1)
            {
                result.Add(this);
            }
            else
            {
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        foreach (int digit3 in number3.Digits())
                        {
                            foreach (int digit4 in number4.Digits())
                            {
                                result.Add(new Pick4(digit1, digit2, digit3, digit4));
                            }
                        }
                    }
                }
            }
            return result;
        }

        private SubTicket<IPick> subticket;
        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            if (Count == 1)
            {
                if (subticket == null) subticket = new SubTicket<IPick>(this);
                result.Add(subticket);
            }
			else
			{
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        foreach (int digit3 in number3.Digits())
                        {
                            foreach (int digit4 in number4.Digits())
                            {
                                result.Add(new SubTicket<IPick>(digit1, digit2, digit3, digit4));
                            }
                        }
                    }
                }
            }                
            return result;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is Pick4)) return false;
            Pick4 other = (Pick4)obj;
            return number1.Equals(other.number1) && number2.Equals(other.number2) && number3.Equals(other.number3) && number4.Equals(other.number4);
        }

        public override int GetHashCode()
        {
            return this.number1.SingleDigit * 1000 + this.number2.SingleDigit * 100 + this.number3.SingleDigit * 10 + this.number4.SingleDigit;
        }

        int IPick.Length => Length;
        public int Length
        {
            get
            {
                return 4;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return number1.Count * number2.Count * number3.Count * number4.Count;
            }
        }

        string IPick.AsString() => AsString();
        internal string AsString()
        {
            return $"[{number1},{number2},{number3},{number4}]";
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            return $"{number1.AsStringForAccounting()}-{number2.AsStringForAccounting()}-{number3.AsStringForAccounting()}-{number4.AsStringForAccounting()}";
        }

        public long ToInt64()
        {
            if (number1.Count != 1 || number2.Count != 1 || number3.Count != 1 || number4.Count != 1) throw new GameEngineException($"Pick {nameof(Pick4)} cannot be converted to Int32");
            return this.number1.SingleDigit * 1000 + this.number2.SingleDigit * 100 + this.number3.SingleDigit * 10 + this.number4.SingleDigit;
        }
    }
}
