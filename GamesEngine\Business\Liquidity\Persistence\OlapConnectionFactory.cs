﻿using ClickHouse.Client.ADO;
using ClickHouse.Client.Copy;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class OlapConnectionFactory : IDbConnectionFactory
    {
        private readonly string _connectionString;
        public OlapConnectionFactory(string connectionString) 
        { 
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString)); 
        }

        public ClickHouseConnection CreateConnection() => new ClickHouseConnection(_connectionString);

        public async Task<IAppConnection> CreateAndOpenConnectionAsync(CancellationToken cancellationToken = default)
        {
            var realConnection = new ClickHouseConnection(_connectionString);
            await realConnection.OpenAsync(cancellationToken);
            return new AppConnectionWrapper(realConnection);
        }

        public IAppBulkCopy CreateAppBulkCopy(ClickHouseConnection connection) => new AppOlapBulkCopyWrapper(connection);
    }
}
