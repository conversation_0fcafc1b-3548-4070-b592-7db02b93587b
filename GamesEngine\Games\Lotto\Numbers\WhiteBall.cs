﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lotto
{
    internal struct WhiteBall
    {
        private readonly byte number;

        internal WhiteBall(int number)
        {
            if (number < 1 || number > 69) throw new GameEngineException($"White numbers must be from 1 to 69. {number} is not valid.");
            this.number = (byte) number;
        }

        internal WhiteBall(string number)
        {
            if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));
            this.number = 0;
            switch (number.Length)
            {
                case 1:
                    if (number[0] >= '1' && number[0] <= '9')
                    {
                        this.number = (byte) (number[0] - '0');
                    }
                    break;
                case 2:
                    if (number[0] == '0' && number[1] >= '1' && number[1] <= '9')
                    {
                        this.number = (byte)(number[1] - '0');
                    }
                    else if (number[0] >= '0' && number[0] <= '6' && number[1] >= '0' && number[1] <= '9')
                    {
                        this.number = (byte) ((number[0] - '0') * 10 + (number[1] - '0'));
                    }
                    break;
            }
            if (this.number == 0) throw new GameEngineException($"White numbers must be from 1 to 69. {number} is not valid.");
        }

        public static bool operator ==(WhiteBall obj1, WhiteBall obj2)
        {
            return obj1.number == obj2.number;
        }

        public static bool operator !=(WhiteBall obj1, WhiteBall obj2)
        {
            return obj1.number != obj2.number;
        }

        public override string ToString()
        {
            return number < 10 ? $"0{number}" : "" + number;
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        private static WhiteBall GenerateOneRandom()
        {
            var random = new Random();
            int number = random.Next(1,70);
            return new WhiteBall(number);
        }

        internal static WhiteBall GenerateOneRandomDistinctOf(IEnumerable<WhiteBall> whiteBalls)
        {
            WhiteBall whiteBall;
            do
            {
                whiteBall = GenerateOneRandom();
            } while (whiteBalls.Contains(whiteBall));

            return whiteBall;
        }

        internal int ToInt32()
        {
            return this.number;
        }
    }
}
