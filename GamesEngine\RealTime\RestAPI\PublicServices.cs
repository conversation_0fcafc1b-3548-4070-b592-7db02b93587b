﻿using Elastic.Clients.Elasticsearch.Fluent;
using GamesEngine.Logs;
using GamesEngine.Messaging;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static town.connectors.CustomSettings.CustomSetting;
[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace GamesEngine.RealTime.RestAPI
{
   

    internal class ProxyAccessEndpoint:Objeto
    {
        private EndpointSubscribers endpointSubscribers;
#if DEBUG
        private const string PREFIX = "pk_test";
#else
        private const string PREFIX = "pk_live";
#endif
        private const int SECRET_RANDOM_SIZE = 32; // Size of the random secret in bytes

        internal ProxyAccessEndpoint(string name, string baseUrl, string endPoint)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Service name cannot be null or empty.", nameof(name));
            if (string.IsNullOrWhiteSpace(baseUrl)) throw new ArgumentException("Method cannot be null or empty.", nameof(baseUrl));
            if (string.IsNullOrWhiteSpace(endPoint)) throw new ArgumentException("EndPoint cannot be null or empty.", nameof(endPoint));
            
            this.endpointSubscribers = new EndpointSubscribers(this);

            Name = name;
            BaseUrl = baseUrl;
            EndPoint = endPoint;
        }


        private int subscriberConsecutive = 0;
        internal int NextSubscriberId()
        {
            return subscriberConsecutive + 1;
        }


        internal string Name { get; private set; }

        internal string BaseUrl { get; private set; }
        internal string EndPoint { get; private set; }

        internal IEnumerable<EndpointSubscriber> Subscribers => endpointSubscribers.Subscribers;

        internal EndpointSubscriber Add(bool itIsThePresent, int id, DateTime createdAt, string organization, string secret, string description, string who)
        {
            if (id <= 0) throw new ArgumentException("Id must be a positive integer.", nameof(id));
            if (createdAt == DateTime.MinValue) throw new ArgumentException("Created date cannot be the minimum value.", nameof(createdAt));
            if (string.IsNullOrWhiteSpace(organization)) throw new ArgumentException("Destinatario cannot be null or empty.", nameof(organization));
            if (string.IsNullOrWhiteSpace(secret)) throw new ArgumentException("Secret cannot be null or empty.", nameof(secret));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentException("Description cannot be null or empty.", nameof(description));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));

            var result = endpointSubscribers.Add(itIsThePresent, id, createdAt, organization, secret, description, who);
            subscriberConsecutive = id;
            return result;
        }


        internal string GenerateSecret()
        {
            byte[] bytes = new byte[SECRET_RANDOM_SIZE];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }

            string randomHex = BitConverter.ToString(bytes).Replace("-", "").ToLower();
            return $"{PREFIX}_{randomHex}";
        }

        

        internal bool ExistSubscriber(int id)
        {
            if (id <= 0) throw new ArgumentException("Id must be a positive integer.", nameof(id));

            foreach (var subscriber in Subscribers)
            {
                if (subscriber.Id == id)
                {
                    return true;
                }
            }

            return false;
        }

        internal EndpointSubscriber FindSubscriber(int subscriberId)
        {
            if (subscriberId <= 0) throw new ArgumentException("Subscriber ID must be a positive integer.", nameof(subscriberId));

            foreach (var subscriber in Subscribers)
            {
                if (subscriber.Id == subscriberId)
                {
                    return subscriber;
                }
            }
            throw new ArgumentException($"No subscriber found with the name '{this.Name}' in the service '{subscriberId}'.");
        }

        internal void RemoveEndpointSubscriber(bool itIsThePresent, int subscriberId)
        {
            if (subscriberId <= 0) throw new ArgumentException("Subscriber ID must be a positive integer.", nameof(subscriberId));
            if (!ExistSubscriber(subscriberId))
            {
                throw new GameEngineException($"No subscriber found with the ID '{subscriberId}'.");
            }
            
            endpointSubscribers.Remove(itIsThePresent, subscriberId);

        }

    }

    internal class EndpointSubscribers:Objeto
    {
        private List<EndpointSubscriber> subscribers;
        private ProxyAccessEndpoint proxyAccessEndpoint;

        internal EndpointSubscribers(ProxyAccessEndpoint proxyAccessEndpoint)
        {
            subscribers = new List<EndpointSubscriber>();
            this.proxyAccessEndpoint = proxyAccessEndpoint;
        }

        internal EndpointSubscriber Add(bool itIsThePresent, int id, DateTime createdAt, string organization, string secret, string description, string who)
        {
            if (id <= 0) throw new ArgumentException("Id must be a positive integer.", nameof(id));
            if (createdAt == DateTime.MinValue) throw new ArgumentException("Created date cannot be the minimum value.", nameof(createdAt));
            if (string.IsNullOrWhiteSpace(organization)) throw new ArgumentException("Destinatario cannot be null or empty.", nameof(organization));
            if (string.IsNullOrWhiteSpace(secret)) throw new ArgumentException("Secret cannot be null or empty.", nameof(secret));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentException("Description cannot be null or empty.", nameof(description));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));

            if (subscribers.Any(c => c.Organization.Equals(organization, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"A consumer with the name '{organization}' already exists.");
            }
            var consumer = new EndpointSubscriber(this, id, createdAt, organization, secret, description, who);
            subscribers.Add(consumer);
            if (itIsThePresent)
            {
                _ = Task.Run(async () => await ProxyEndpointsSettings.AddConsumerAsync(
                    proxyAccessEndpoint.Name,
                    proxyAccessEndpoint.EndPoint,
                    ActiveSubscribers(),
                    organization,
                    secret,
                    description
                ));
            }
            return consumer;
        }

        internal  List<string> ActiveSubscribers()
        {
            List<string> enabledSubscribers = new List<string>();
            for (int i = 0; i < subscribers.Count; i++)
            {
                var subscriber = subscribers[i];
                if (subscriber.Enabled)
                {
                    enabledSubscribers.Add(subscriber.Organization.Replace(" ", ""));
                }
            }

            return enabledSubscribers;
        }

        internal EndpointSubscriber this[string name]
        {
            get
            {
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Consumer name cannot be null or empty.", nameof(name));
                foreach (var consumer in subscribers)
                {
                    if (consumer.Organization.Equals(name, StringComparison.OrdinalIgnoreCase))
                    {
                        return consumer;
                    }
                }
                throw new KeyNotFoundException($"No consumer found with the name '{name}'.");
            }
        }

        internal void Remove(bool itIsThePresent, int id)
        {
           if(id <= 0) throw new ArgumentException("Id must be a positive integer.", nameof(id));
            int removeIndex = -1;
            EndpointSubscriber currentSubscriber = null;
            for (int i = 0; i < subscribers.Count; i++)
            {
                if (subscribers[i].Id == id)
                {
                    removeIndex = i;
                    currentSubscriber = subscribers[i];
                    break;
                }
            }
            if (removeIndex == -1)
            {
                throw new GameEngineException($"No subscriber found with the ID '{id}'.");
            }
            subscribers.RemoveAt(removeIndex);

            if (itIsThePresent)
            {
                _ = Task.Run(async () => await ProxyEndpointsSettings.RemoveConsumerAsync(
                    proxyAccessEndpoint.Name,
                    proxyAccessEndpoint.EndPoint,
                ActiveSubscribers(),
                    currentSubscriber.Organization
                ));
            }

        }

            
        internal IEnumerable<EndpointSubscriber> Subscribers => subscribers;
        internal ProxyAccessEndpoint ProxyAccessEndpoint => proxyAccessEndpoint;
    }

    internal class EndpointSubscriber:Objeto
    {
        private const int CAPACITY = 5;

        private EndpointSubscribers endpointSubscribers;

        internal EndpointSubscriber(EndpointSubscribers endpointSubscribers, int id, DateTime createdAt, string organization, string secret, string description, string who)
        {
            if (id <= 0) throw new ArgumentException("Id must be a positive integer.", nameof(id));
            if (createdAt == DateTime.MinValue) throw new ArgumentException("Created date cannot be the minimum value.", nameof(createdAt));
            if (string.IsNullOrWhiteSpace(organization)) throw new ArgumentException("Organization cannot be null or empty.", nameof(organization));
            if (string.IsNullOrWhiteSpace(secret)) throw new ArgumentException("Secret cannot be null or empty.", nameof(secret));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentException("Description cannot be null or empty.", nameof(description));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));

            Id = id;
            CreatedAt = createdAt;
            Organization = organization;
            Secret = secret;
            Description = description;
            Log = new Log($"logs-{organization}-{id}", CAPACITY);
            this.endpointSubscribers = endpointSubscribers;

            Log.AddEntry(createdAt, who, $"Subscriber '{this.Organization}' has been created with ID {this.Id} and secret '{this.Secret}'.");
        }

        internal DateTime CreatedAt { get; private set; }

        internal string Secret { get; private set; }

        internal int Id { get; private set; }

        internal string Organization { get; private set; }

        internal string Description { get; set; }

        internal bool Enabled { get; private set; } = true;

        internal Log Log { get; private set; }

        internal void UpdateSecret(bool itIsThePresent, DateTime now, string who, string secret)
        {
            if(now == DateTime.MinValue)
            {
                throw new ArgumentException("Date cannot be the minimum value.", nameof(now));
            }
            if(string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));
            if (string.IsNullOrWhiteSpace(secret)) throw new ArgumentException("Secret cannot be null or empty.", nameof(secret));

            Secret = secret;
            Log.AddEntry(now, who, $"Subscriber '{this.Organization}' secret has been updated.");

            if (itIsThePresent)
            {
                _ = Task.Run(async () => await ProxyEndpointsSettings.RefreshSecretAsync(this.Organization,secret));
            }
        }


        internal void Disable(bool itIsThePresent, DateTime now,string who, string reason)
        {
            if (string.IsNullOrWhiteSpace(reason)) throw new ArgumentException("Reason cannot be null or empty.", nameof(reason));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));
            if (now == DateTime.MinValue)
            {
                throw new ArgumentException("Date cannot be the minimum value.", nameof(now));
            }
            if (!this.Enabled)
            {
                throw new GameEngineException($"The subscriber '{this.Organization}' is already disabled.");
            }

            this.Enabled = false;
            Log.AddEntry(now, who, $"Subscriber '{this.Organization}' has been disabled. Reason: {reason}.");
            if (itIsThePresent)
            {
                _ = Task.Run(async () => await ProxyEndpointsSettings.UpdateWhiteListRouteAsync(
                    endpointSubscribers.ProxyAccessEndpoint.Name,
                    endpointSubscribers.ActiveSubscribers(),
                    endpointSubscribers.ProxyAccessEndpoint.EndPoint
                ));
            }
        }

        internal void Enable(bool itIsThePresent, DateTime now, string who, string reason)
        {
            if (string.IsNullOrWhiteSpace(reason)) throw new ArgumentException("Reason cannot be null or empty.", nameof(reason));
            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentException("Who cannot be null or empty.", nameof(who));
            if(now == DateTime.MinValue)
            {
                throw new ArgumentException("Date cannot be the minimum value.", nameof(now));
            }

            if (this.Enabled == true)
            {
                throw new GameEngineException($"The subscriber '{this.Organization}' is already enabled.");
            }

            this.Enabled = true;
            Log.AddEntry(now, who, $"Subscriber '{this.Organization}' has been enabled. Reason: {reason}.");
            if (itIsThePresent)
            {
                _ = Task.Run(async () => await ProxyEndpointsSettings.UpdateWhiteListRouteAsync(
                    endpointSubscribers.ProxyAccessEndpoint.Name,
                    endpointSubscribers.ActiveSubscribers(),
                    endpointSubscribers.ProxyAccessEndpoint.EndPoint
                ));
            }
        }

    }

    internal class EndpointServiceMonitor : Objeto
    {
        private readonly List<ProxyAccessEndpoint> proxyEndpoints;

        private EndpointServiceMonitor()
        {
            this.proxyEndpoints = new List<ProxyAccessEndpoint>();
        }

        internal static EndpointServiceMonitor monitor;
        internal static EndpointServiceMonitor Instance()
        {
            if (monitor == null)
            {
                monitor = new EndpointServiceMonitor();
            }

            return monitor;
        }

        internal ProxyAccessEndpoint Add(bool itIsThePresent, string name, string endPoint, string baseUrl)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Service name cannot be null or empty.", nameof(name));
            if (string.IsNullOrWhiteSpace(baseUrl)) throw new ArgumentException("Method cannot be null or empty.", nameof(baseUrl));
            if (string.IsNullOrWhiteSpace(endPoint)) throw new ArgumentException("EndPoint cannot be null or empty.", nameof(endPoint));


            if (proxyEndpoints.Any(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase) && s.EndPoint.Equals(endPoint, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"A public service with the name '{name}' and endpoint '{endPoint}' already exists.");
            }
            if (proxyEndpoints.Any(s => s.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"A public service with the name '{name}' already exists.");
            }
            if (proxyEndpoints.Any(s => s.EndPoint.Equals(endPoint, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"A public service with the endpoint '{endPoint}' already exists.");
            }
            var service = new ProxyAccessEndpoint(name, baseUrl, endPoint);
            proxyEndpoints.Add(service);
            return service;
        }

        internal ProxyAccessEndpoint this[string name]
        {
            get
            {
                if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Service name cannot be null or empty.", nameof(name));
                foreach (var service in proxyEndpoints)
                {
                    if (service.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                    {
                        return service;
                    }
                }
                throw new KeyNotFoundException($"No public service found with the name '{name}'.");
            }
        }

        internal ProxyAccessEndpoint Find(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Service name cannot be null or empty.", nameof(name));
            foreach (var service in proxyEndpoints)
            {
                if (service.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                {
                    return service;
                }
            }
          throw new GameEngineException($"No public service found with the name '{name}'.");
        }

        internal bool Exist(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Service name cannot be null or empty.", nameof(name));
            foreach (var service in proxyEndpoints)
            {
                if (service.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            return false;
        }

        internal ICollection<ProxyAccessEndpoint> ProxyAccessEndpoints => proxyEndpoints;

    }

}
