﻿using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Gameboards.MarchMadness;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Tools;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;

namespace GamesEngine.Business
{
	[Puppet]
	internal sealed class MockAccounting:Accounting
	{
		private decimal founds = 80000;

		internal MockAccounting() { }

		internal override bool CustomerHasFoundsFor(Order order, decimal foundsArg)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));

			var hasFounds = foundsArg >= order.Total();
			return hasFounds;
		}

		internal override void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (order.Customer == null) throw new ArgumentNullException(nameof(order.Customer));
			var customer = order.Customer;
			if (String.IsNullOrWhiteSpace(customer.AccountNumber)) throw new ArgumentNullException(nameof(customer.AccountNumber));
			decimal total = order.Total();
			if (total <= 0) throw new GameEngineException("Total in order must be greater than 0");

			int orderNumber = order.Number;
			int lowBetId = order.LookForTheLowestBetNumber();
			int highBetId = order.LookForTheHighestBetNumber();
			if (lowBetId <= 0) throw new GameEngineException($"{nameof(lowBetId)} must be greater than 0");
			if (highBetId <= 0) throw new GameEngineException($"{nameof(highBetId)} must be greater than 0");

			bool itemCouldBeATicketBracketOrLine = lowBetId == highBetId;
			if (itemCouldBeATicketBracketOrLine)
			{
				var betNumber = lowBetId;
				var company = order.Customer.Company;
				var gameboard = company.FindBetById(betNumber).Gameboard;
				switch (gameboard)
				{
					case Ticket t:
						PicksLotteryGame lotto900 = order.Company.Lotto900();
						SendFragmentsCreation(itIsThePresent, customer, t.TicketNumber, lowBetId, highBetId, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.Domain.Url);
						break;
					case Bracket b:
						string description = $"Brackets: Bracket # {b.Id} Purchase";
						string additionalInfo = $"Bracket # + {b.Id} price {total}";

						if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new OrderTransaction(orderNumber, customer.AccountNumber, total, description, additionalInfo, now, betNumber));
						break;
					case Wager w:

						break;
					default:
						throw new GameEngineException($"This gameboard {gameboard.GetType().Name} is not handled yet");
				}
			}
			else
			{
				PicksLotteryGame lotto900 = customer.Company.Lotto900();
				if (order.IsMultipleAuthorization)
				{
					var company = order.Customer.Company;
					var bets = company.FindBetsInRange(lowBetId, highBetId);
                    var ticketWagers = bets.Select(b => b.Gameboard as Ticket).SelectMany(t => t.Wagers);

                    SendFragmentsCreation(itIsThePresent, customer, order.Domain, ticketWagers, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.ProcessorKey);
                }
				else
				{
					int ticketNumber = order.AuthorizationId;
					SendFragmentsCreation(itIsThePresent, customer, ticketNumber, lowBetId, highBetId, orderNumber, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.Domain.Url);
				}
			}

			founds -= total;
		}

		internal override void Purchase(bool itIsThePresent, OrderCompleted order, DateTime now, IEnumerable<Ticket> tickets)
		{
            PicksLotteryGame lotto900 = order.Customer.Company.Lotto900();
            var ticketWagers = tickets.SelectMany(t => t.Wagers);
            var total = order.Total();
            SendFragmentsCreation(itIsThePresent, order.Customer, order.Domain, ticketWagers, order.Number, order.Coin, lotto900, total, order.Customer.Player.Agent, now, order.ProcessorKey);
        }

		internal override void GradeTicketWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			if ( ! Integration.UseKafka || ! Integration.UseKafkaForAuto) return;

			var ticket = wager.Ticket;
			var storeId = ticket.SoldBy.Id;
			Action<KafkaMessages> beforeSend = (KafkaMessages kafkaMessages) =>
			{
                kafkaMessages.Add((int)Integration.Localization);
                kafkaMessages.Add(employeeName);
				kafkaMessages.Add(storeId);
				kafkaMessages.Add(ticket.Draw.SometimeWereTicketsSentToAccounting);
			};

			using (FragmentPaymentCompressor bufferForAll = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
			{
				bufferForAll.BeforeSendTheFirstMessage = bufferForAll.GenerateHeader(employeeName, storeId, WholePaymentProcessor.NoPaymentProcessor, ticket.Draw.SometimeWereTicketsSentToAccounting, now);

				using (KafkaMessagesBuffer bufferForWinners = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
				{
					bufferForWinners.BeforeSendTheFirstMessage = beforeSend;

					FragmentPaymentMessage gradedWagerMessage;
					var hasSequenceOfNumbers = ticket.Draw.HasSequenceOfNumbers;

					switch (ticket.Prizing)
					{
						case GameboardStatus.LOSER:
							string message = string.Empty;
							gradedWagerMessage = wager.FragmentPaymentMessage(now);

							bufferForAll.Send(gradedWagerMessage);

							break;
						case GameboardStatus.WINNER:
							var isWinnerWager = hasSequenceOfNumbers && wager.IsWinner();
							if (isWinnerWager)
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);
								bufferForWinners.Send(gradedWagerMessage);
							}
							else
							{
								gradedWagerMessage = wager.FragmentPaymentMessage(now);
								bufferForAll.Send(gradedWagerMessage);
							}

							founds += ticket.CalculatedPrize();
							break;
						default:
							throw new GameEngineException("At this point ticket must be winner or loser");
					}
				}
			}
		}

		internal override void DeleteTickets(bool itIsThePresent, IEnumerable<Ticket> tickets, Store store, DateTime now, string employeeName)
		{
			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				FragmentPaymentMessage gradedWagerMessage;
				using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
				{
					buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(employeeName, store.Id, WholePaymentProcessor.NoPaymentProcessor, false, now);

					foreach (var ticket in tickets)
					{

						foreach (var wager in ticket.Wagers)
						{
							gradedWagerMessage = wager.GenerateDeletedFragmentMessage(now); 
							founds += wager.Risk;
							buffer.Send(gradedWagerMessage);
						}
					}
				}
			}
		}

		internal override void DeleteWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				using (FragmentPaymentCompressor buffer = new FragmentPaymentCompressor(itIsThePresent, Integration.Kafka.TopicForFragmentPaymentsForAll))
				{
					buffer.BeforeSendTheFirstMessage = buffer.GenerateHeader(employeeName, wager.Ticket.SoldBy.Id, WholePaymentProcessor.NoPaymentProcessor, false, now);

					FragmentPaymentMessage gradedWagerMessage = wager.GenerateDeletedFragmentMessage(now); 

					founds += wager.Risk;
					buffer.Send(gradedWagerMessage);
				}
			}
			founds += wager.Risk;
		}

		internal override void PayPrize(bool itIsThePresent, int betId, string accountNumber, decimal prize, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");
            Tools.Commons.ValidateAmount(prize);

			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.PayPrize, betId, accountNumber, prize, description, additionalInfo, now));

			founds += prize;
		}

		internal override void Refund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.Refund, betId, accountNumber, amount, description, additionalInfo, now));

			founds += amount;
		}

		internal override void ReturnPrize(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.ReturnPrize, betId, accountNumber, amount, description, additionalInfo, now));

			founds -= amount;
		}

		internal override void RevertRefund(bool itIsThePresent, int betId, string accountNumber, decimal amount, string description, string additionalInfo, DateTime now)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
			if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
            Tools.Commons.ValidateAmount(amount);

			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForTransacctions, new BetsTransaction(TransactionType.RevertRefund, betId, accountNumber, amount, description, additionalInfo, now));

			founds -= amount;
		}
	}
}
