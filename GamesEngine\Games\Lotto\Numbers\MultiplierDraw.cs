﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    public struct MultiplierDraw
    {
        public const string NO_MULTIPLIER = "None";
        private readonly int multiplier;

        internal MultiplierDraw(int multiplier)
        {
            this.multiplier = multiplier;
        }

        public static bool operator ==(MultiplierDraw obj1, MultiplierDraw obj2)
        {
            return obj1.multiplier == obj2.multiplier;
        }

        public static bool operator !=(MultiplierDraw obj1, MultiplierDraw obj2)
        {
            return obj1.multiplier != obj2.multiplier;
        }

        public override string ToString()
        {
            return $"{multiplier}";
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        internal decimal Multiply(decimal payout)
        {
            return multiplier * payout;
        }
    }
}
