﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Text;
using town.connectors.drivers;

namespace GamesEngine.Exchange
{
    internal sealed class CreditNoteTransaction : Transaction
    {
		internal const int TEMPLATE_ID = 6;
		internal CreditNoteTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, string employeeName, Currency amount, DateTime now)
            : base(transactionDefinition, conversionSpread, amount, now,  TransactionType.CreditNote)
        {
			transactionDefinition.Batch.AddTransactionDenifition(this);
		}


		
		
		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted)
        {
			if (!itsThePresent) return;

			CustomerAccount toAccount = TransactionDefinition.Account;
			string description = $"Credit note {amountToCustomer.CurrencyCode} {amountToCustomer.Value} to {toAccount.Identificator}";

            //SendDepositMessage(
            //    itsThePresent,
            //    toAccount.CurrencyCode.ToString(),
            //    toAccount.CustomerAccountNumber,
            //    amountToCustomer.Value,
            //    description,
            //    this.Id,
            //    employeeName,
            //    toAccount.Identificator,
            //    ProcessorAccountId,
            //    PaymentChannels.Agents.INSIDER
            //    );

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{

				var authorizationId = 0; //TODO
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new ApprovedTransactionMessage(TransactionType.CreditNote, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, ProcessorAccountId)
				);
			}
		}

        internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
        {
			TransactionDefinition.Deny(this);

			if (!itsThePresent) return;

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new DeniedTransactionMessage(TransactionType.CreditNote, Id, date, reason, employeeName)
				);
			}
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

	}

    
}
