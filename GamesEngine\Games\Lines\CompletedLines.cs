﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedLines: Objeto
    {
        List<CompletedLine> lines = new List<CompletedLine>();

        internal IEnumerable<CompletedLine> GetAll
        {
            get
            {
                return lines;
            }
        }

        internal void Add(CompletedLine line)
        {
            if (line == null) throw new ArgumentNullException(nameof(line));

            lines.Add(line);
        }
    }
}
