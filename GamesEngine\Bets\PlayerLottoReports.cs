﻿using GamesEngine.Games.Lotto;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using GamesEngine.Business;

namespace GamesEngine.Bets
{
    internal class PlayerLottoReports : Objeto
    {
        private readonly Player player;
        private readonly IEnumerable<Ticket> tickets;
        private readonly Customer customer;

        enum GameType
        {
            Pick = 1,
            Powerball = 2,
            All = 3
        }

        [Flags]
        public enum TicketTypeForReports { P2S = 1, P2B = 2, P3S = 4, P3B = 8, P4S = 16, P4B = 32, P5S = 64, P5B = 128, PBS = 256, PBP = 512 }

        internal PlayerLottoReports(Player player, IEnumerable<Ticket> tickets, Customer customer)
        {
            this.player = player;
            this.tickets = tickets;
            this.customer = customer;
        }

        struct TicketByPrizeKeySortedByDrawDate : IComparable
        {
            private DateTime drawDate;
            private DateTime creationDate;
            private string gameType;
            private string state;
            private string numbers;
            private int gameboardId;

            internal TicketByPrizeKeySortedByDrawDate(int gameboardId, DateTime drawDate, DateTime creationDate, string state, string gameType, string numbers)
            {
                this.drawDate = drawDate;
                this.creationDate = creationDate;
                this.gameType = gameType;
                this.state = state;
                this.numbers = numbers;
                this.gameboardId = gameboardId;
            }

            public int CompareTo(object obj)
            {
                var otherKey = (TicketByPrizeKeySortedByDrawDate)obj;

                if (otherKey.drawDate != drawDate)
                {
                    return -DateTime.Compare(otherKey.drawDate, drawDate);
                }
                else
                {
                    if (otherKey.state != state)
                    {
                        return -String.Compare(otherKey.state, state);
                    }
                    else
                    {
                        if (otherKey.gameType != gameType)
                        {
                            return -String.Compare(otherKey.gameType, gameType);
                        }
                        else
                        {
                            if (otherKey.creationDate != creationDate)
                            {
                                return -DateTime.Compare(otherKey.creationDate, creationDate);
                            }
                            else
                            {
                                if (otherKey.gameboardId != gameboardId)
                                {
                                    return otherKey.gameboardId - gameboardId;
                                }
                                else
                                    return String.Compare(otherKey.numbers, numbers);
                            }

                        }
                    }
                }
            }

        }

        struct PlayedTicketByPrizeKeySortedByDrawDate : IComparable
        {
            private DateTime drawDate;
            private DateTime creationDate;
            private string gameType;
            private string state;
            private string numbers;
            private int gameboardId;

            internal PlayedTicketByPrizeKeySortedByDrawDate(int gameboardId, DateTime drawDate, DateTime creationDate, string state, string gameType, string numbers)
            {
                this.drawDate = drawDate;
                this.creationDate = creationDate;
                this.gameType = gameType;
                this.state = state;
                this.numbers = numbers;
                this.gameboardId = gameboardId;
            }

            public int CompareTo(object obj)
            {
                var otherKey = (PlayedTicketByPrizeKeySortedByDrawDate)obj;
                if (otherKey.drawDate != drawDate)
                {
                    return DateTime.Compare(otherKey.drawDate, drawDate);
                }
                else
                {
                    if (otherKey.state != state)
                    {
                        return -String.Compare(otherKey.state, state);
                    }
                    else
                    {
                        if (otherKey.gameType != gameType)
                        {
                            return -String.Compare(otherKey.gameType, gameType);
                        }
                        else
                        {
                            if (otherKey.creationDate != creationDate)
                            {
                                return -DateTime.Compare(otherKey.creationDate, creationDate);
                            }
                            if (otherKey.gameboardId != gameboardId)
                            {
                                return otherKey.gameboardId - gameboardId;
                            }
                            else
                                return String.Compare(otherKey.numbers, numbers);
                        }
                    }
                }
            }
        }

        struct TicketByPrizeKeySortedByState : IComparable
        {
            private DateTime drawDate;
            private DateTime creationDate;
            private string gameType;
            private string state;
            private string numbers;

            internal TicketByPrizeKeySortedByState(DateTime drawDate, DateTime creationDate, string state, string gameType, string numbers)
            {
                this.drawDate = drawDate;
                this.creationDate = creationDate;
                this.gameType = gameType;
                this.state = state;
                this.numbers = numbers;
            }

            public int CompareTo(object obj)
            {
                var otherKey = (TicketByPrizeKeySortedByState)obj;
                if (otherKey.state != state)
                {
                    return -String.Compare(otherKey.state, state);
                }
                else
                {
                    if (otherKey.drawDate != drawDate)
                    {
                        return -DateTime.Compare(otherKey.drawDate, drawDate);
                    }
                    else
                    {
                        if (otherKey.gameType != gameType)
                        {
                            return String.Compare(otherKey.gameType, gameType);
                        }
                        else
                        {
                            if (otherKey.creationDate != creationDate)
                                return -DateTime.Compare(otherKey.creationDate, creationDate);
                            else
                                return String.Compare(otherKey.numbers, numbers);
                        }
                    }
                }
            }
        }

        struct PlayedTicketByPrizeKeySortedByState : IComparable
        {
            private DateTime drawDate;
            private DateTime creationDate;
            private string gameType;
            private string state;
            private string numbers;

            internal PlayedTicketByPrizeKeySortedByState(DateTime drawDate, DateTime creationDate, string state, string gameType, string numbers)
            {
                this.drawDate = drawDate;
                this.creationDate = creationDate;
                this.gameType = gameType;
                this.state = state;
                this.numbers = numbers;
            }

            public int CompareTo(object obj)
            {
                var otherKey = (PlayedTicketByPrizeKeySortedByState)obj;
                if (otherKey.state != state)
                {
                    return -String.Compare(otherKey.state, state);
                }
                else
                {
                    if (otherKey.drawDate != drawDate)
                    {
                        return DateTime.Compare(otherKey.drawDate, drawDate);
                    }
                    else
                    {
                        if (otherKey.gameType != gameType)
                        {
                            return String.Compare(otherKey.gameType, gameType);
                        }
                        else
                        {
                            if (otherKey.creationDate != creationDate)
                                return -DateTime.Compare(otherKey.creationDate, creationDate);
                            else
                                return String.Compare(otherKey.numbers, numbers);
                        }
                    }
                }
            }
        }

        decimal CalculatePrize(Ticket ticket, SubTicket<IPick> subticket)
        {
            return CalculatePrizeForOneDollar(ticket, subticket);
        }

        decimal CalculatePrizeForOneDollar(Ticket ticket, SubTicket<IPick> subticket)
        {
            var prizes = player.Company.Lotto900().Prizes;
            var versionNumber = ticket.Prizes.VersionNumber;
            var prizeCriteria = prizes.WayOfSubticket(ticket.IdOfType(), subticket);
            return prizes.Prize(versionNumber, ticket.IdOfType(), prizeCriteria);
        }

        internal IEnumerable<TicketsByOrderAndPrize> MyAlivePendingTickets()
        {
            var ticketsByOrderAndPrize = new Dictionary<string, TicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                if (ticket.IsUnprized() && ticket.IsPending())
                {
                    foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                    {
                        var prize = CalculatePrize(ticket, subticket);
                        var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                        var key = $"{ticket.Order.Number}{prize}{ticket.DrawDate}{ticket.LotteryState().Abbreviation}{ticket.Lottery.GetTypeNumberSequence()}{fireball}";
                        if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                        {
                            existingTicket.Add(ticket, prize);
                        }
                        else
                        {
                            ticketsByOrderAndPrize.Add(key, new TicketsByOrderAndPrize(ticket, prize));
                        }
                    }
                }
            }

            return ticketsByOrderAndPrize.Values.OrderByDescending(ticketByPrize => ticketByPrize.CreationDate).ToList();
        }

        internal IEnumerable<TicketsByOrderAndPrize> MyAlivePendingTickets(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate < startDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var ticketsByOrderAndPrize = new Dictionary<string, TicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                var drawDay = ticket.DrawDate.Date;
                if (ticket.IsUnprized() && ticket.IsPending() && drawDay >= startDate && drawDay <= endDate)
                {
                    foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                    {
                        var prize = CalculatePrize(ticket, subticket);
                        var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                        var key = $"{ticket.Order.Number}{prize}{ticket.DrawDate}{ticket.LotteryState().Abbreviation}{ticket.Lottery.GetTypeNumberSequence()}{fireball}";
                        if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                        {
                            existingTicket.Add(ticket, prize);
                        }
                        else
                        {
                            ticketsByOrderAndPrize.Add(key, new TicketsByOrderAndPrize(ticket, prize));
                        }
                    }
                }
            }

            return ticketsByOrderAndPrize.Values.OrderBy(ticketByPrize => ticketByPrize.DrawDate).ToList();
        }

        internal IEnumerable<Ticket> MyAlivePendingKenoTickets()
        {
            var result = Enumerable.Empty<Ticket>();
            foreach (var ticketTemp in tickets)
            {
                if (ticketTemp is TicketKeno ticket)
                {
                    if (ticket.IsUnprized() && ticket.IsPending())
                    {
                        result = result.Concat(new[] { ticket });
                    }
                }
            }

            return result.OrderByDescending(ticket => ticket.CreationDate).ToList();
        }

        internal IEnumerable<Ticket> MyAlivePendingKenoTickets(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate < startDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var result = Enumerable.Empty<Ticket>();
            foreach (var ticketTemp in tickets)
            {
                if (ticketTemp is TicketKeno ticket)
                {
                    var drawDay = ticket.DrawDate.Date;
                    if (ticket.IsUnprized() && ticket.IsPending() && drawDay >= startDate && drawDay <= endDate)
                    {
                        result = result.Concat(new[] { ticket });
                    }
                }
            }

            return result.OrderByDescending(ticket => ticket.CreationDate).ToList();
        }

        internal IEnumerable<TicketsByOrderAndPrize> MyRecentAlivePendingTickets(DateTime startDate, DateTime now)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(now)}");

            var ticketsByOrderAndPrize = new Dictionary<string, TicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                var drawDate = ticket.DrawDate;
                var creationDate = ticket.CreationDate;
                if (ticket.IsUnprized() && ticket.IsPending() && ((drawDate >= startDate && drawDate <= now) || (creationDate >= startDate && creationDate <= now)))
                {
                    foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                    {
                        var prize = CalculatePrize(ticket, subticket);
                        var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                        var key = $"{ticket.Order.Number}{prize}{ticket.DrawDate}{ticket.LotteryState().Abbreviation}{ticket.Lottery.GetTypeNumberSequence()}{fireball}";
                        if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                        {
                            existingTicket.Add(ticket, prize);
                        }
                        else
                        {
                            ticketsByOrderAndPrize.Add(key, new TicketsByOrderAndPrize(ticket, prize));
                        }
                    }
                }
            }

            return ticketsByOrderAndPrize.Values.OrderBy(ticketByPrize => ticketByPrize.DrawDate).OrderByDescending(ticketByPrize => ticketByPrize.CreationDate).ToList();
        }

        internal IEnumerable<Ticket> MyRecentAlivePendingKenoTickets(DateTime startDate, DateTime now)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (now < startDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(now)}");

            var result = Enumerable.Empty<Ticket>();
            foreach (var ticketTemp in tickets)
            {
                if (ticketTemp is TicketKeno ticket)
                {
                    var drawDate = ticket.DrawDate;
                    var creationDate = ticket.CreationDate;
                    if (ticket.IsUnprized() && ticket.IsPending() && ((drawDate >= startDate && drawDate <= now) || (creationDate >= startDate && creationDate <= now)))
                    {
                        result = result.Concat(new[] { ticket });
                    }
                }
            }

            return result.OrderBy(ticket => ticket.DrawDate).ThenByDescending(ticket => ticket.CreationDate).ToList();
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByTimeFrom(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && date <= ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = ticket.TicketsByPrize();
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByTimeBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && startedDate <= ticket.DrawDate && endedDate >= ticket.DrawDate)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByTimeAll(string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction())
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByTimeIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && drawDates.Contains(ticket.DrawDate.Date))
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        private List<DateTime> DatesFrom(string dates)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));

            var stringDates = dates.Split(",");
            foreach (string dateString in stringDates)
            {
                DateTime dateValue;
                if (!DateTime.TryParseExact(dateString, "M/d/yyyy", Integration.CultureInfoEnUS, DateTimeStyles.None, out dateValue))
                    throw new GameEngineException($"Any date in {nameof(stringDates)} have not the correct format");
            }

            var stringDatesFormatted = new string[stringDates.Length];
            DateTime drawDate;
            List<DateTime> drawDates = new List<DateTime>();
            for (int index = 0; index < stringDates.Length; index++)
            {
                drawDate = DateTime.ParseExact(stringDates[index], "M/d/yyyy", Integration.CultureInfoEnUS);
                drawDates.Add(drawDate);
            }
            return drawDates;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByStateFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && date <= ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByStateBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && startedDate <= ticket.DrawDate && endedDate >= ticket.DrawDate)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByStateAll(string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction())
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAlivePendingTicketsByStateIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var sortedTicketsByPrize = new SortedList<TicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && !ticket.IsNoAction() && drawDates.Contains(ticket.DrawDate.Date))
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new TicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByTimeFrom(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && date <= ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = ticket.TicketsByPrize();
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByTimeBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && startedDate <= ticket.DrawDate && endedDate >= ticket.DrawDate)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByTimeAll(string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction())
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByTimeIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && drawDates.Contains(ticket.DrawDate.Date))
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByStateFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && date <= ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByStateBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && startedDate <= ticket.DrawDate && endedDate >= ticket.DrawDate)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByStateAll(string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction())
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveNoActionTicketsByStateIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (ticket.IsUnprized() && ticket.IsNoAction() && drawDates.Contains(ticket.DrawDate.Date))
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                            sortedTicketsByPrize.Add(key, ticketByPrize);
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveWinnerTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (! ticket.IsUnprized() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            if (ticketByPrize.IsWinner())
                            {
                                var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                                sortedTicketsByPrize.Add(key, ticketByPrize);
                            }
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveWinnerTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (! ticket.IsUnprized() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            if (ticketByPrize.IsWinner())
                            {
                                var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                                sortedTicketsByPrize.Add(key, ticketByPrize);
                            }
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveLoserTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByDrawDate, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (! ticket.IsUnprized() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            if (!ticketByPrize.IsWinner())
                            {
                                var key = new PlayedTicketByPrizeKeySortedByDrawDate(ticket.Id, ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                                sortedTicketsByPrize.Add(key, ticketByPrize);
                            }
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        internal TicketsByPrizeWithTotalSubtickets MyAliveLoserTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var sortedTicketsByPrize = new SortedList<PlayedTicketByPrizeKeySortedByState, TicketByPrize>();
            foreach (var ticket in tickets)
            {
                var isForAll = gameType == GameType.All.ToString() && ticket is Ticket;
                var isForPowerball = !isForAll && gameType == GameType.Powerball.ToString() && ticket is TicketPowerBall;
                var isForPick = !isForAll && !isForPowerball && gameType == GameType.Pick.ToString() && (ticket is TicketPick2Boxed || ticket is TicketPick2Straight || ticket is TicketPick3Boxed || ticket is TicketPick3Straight || ticket is TicketPick4Boxed || ticket is TicketPick4Straight || ticket is TicketPick5Boxed || ticket is TicketPick5Straight);
                if (isForAll || isForPowerball || isForPick)
                {
                    if (! ticket.IsUnprized() && date == ticket.DrawDate.Date)
                    {
                        var ticketsByPrize = withLR ? ticket.TicketsByPrize().Where(x => x.Ticket.WasPurchasedForFree) : ticket.TicketsByPrize().Where(x => !x.Ticket.WasPurchasedForFree);
                        foreach (var ticketByPrize in ticketsByPrize)
                        {
                            if (!ticketByPrize.IsWinner())
                            {
                                var key = new PlayedTicketByPrizeKeySortedByState(ticket.DrawDate, ticket.CreationDate, ticket.LotteryState().Abbreviation, ticket.GameType(), ticketByPrize.AsString());
                                sortedTicketsByPrize.Add(key, ticketByPrize);
                            }
                        }
                    }
                }
            }

            var result = new TicketsByPrizeWithTotalSubtickets(sortedTicketsByPrize.Values);
            return result;
        }

        private IEnumerable<TicketPick<T>> AliveTicketsAt<T>(DateTime creationDate) where T : IPick
        {
            var result = tickets.Where(bet => bet is TicketPick<T> &&
                creationDate.Year == bet.CreationDate.Year &&
                creationDate.Month == bet.CreationDate.Month &&
                creationDate.Day == bet.CreationDate.Day &&
                creationDate.Hour == bet.CreationDate.Hour &&
                creationDate.Minute == bet.CreationDate.Minute &&
                creationDate.Second == bet.CreationDate.Second).
                Cast<TicketPick<T>>();
            return result;
        }

        internal TicketByPrize GetAliveTicketBy(State state, DateTime creationDate, DateTime drawDate, decimal prize, string gameType, bool wasCreatedByPattern)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            TicketByPrize result = null;
            switch (gameType)
            {
                case "TicketPick2Straight":
                case "TicketPick2Boxed":
                    {
                        result = AliveTicketsAt<Pick2>(creationDate).
                            Where(ticket => ticket.DrawDate == drawDate && ticket.LotteryState() == state && ticket.WasCreatedByPattern() == wasCreatedByPattern).
                            SelectMany(ticket => ticket.TicketsByPrize()).
                            FirstOrDefault(x => x.Prize == prize);
                        if (result != null) return result;
                        break;
                    }
                case "TicketPick3Straight":
                case "TicketPick3Boxed":
                    {
                        result = AliveTicketsAt<Pick3>(creationDate).
                            Where(ticket => ticket.DrawDate == drawDate && ticket.LotteryState() == state && ticket.WasCreatedByPattern() == wasCreatedByPattern).
                            SelectMany(ticket => ticket.TicketsByPrize()).
                            FirstOrDefault(x => x.Prize == prize);
                        if (result != null) return result;
                        break;
                    }
                case "TicketPick4Straight":
                case "TicketPick4Boxed":
                    {
                        result = AliveTicketsAt<Pick4>(creationDate).
                            Where(ticket => ticket.DrawDate == drawDate && ticket.LotteryState() == state && ticket.WasCreatedByPattern() == wasCreatedByPattern).
                            SelectMany(ticket => ticket.TicketsByPrize()).
                            FirstOrDefault(x => x.Prize == prize);
                        if (result != null) return result;
                        break;
                    }
                case "TicketPick5Straight":
                case "TicketPick5Boxed":
                    {
                        result = AliveTicketsAt<Pick5>(creationDate).
                            Where(ticket => ticket.DrawDate == drawDate && ticket.LotteryState() == state && ticket.WasCreatedByPattern() == wasCreatedByPattern).
                            SelectMany(ticket => ticket.TicketsByPrize()).
                            FirstOrDefault(x => x.Prize == prize);
                        if (result != null) return result;
                        break;
                    }
                default:
                    throw new Exception($"The {nameof(gameType)} {gameType} is not valid.");
            }

            if (result == null)
            {
                return new TicketByPrizeEmpty();
            }
            return result;
        }

        private IEnumerable<LoserInfoByPrize> TransformWinnerInfoToLoserInfoByPrize(params WinnerInfo[] winners)
        {
            var tickets = new List<LoserInfoByPrize>();
            string type = "";
            foreach (var winnerInfo in winners)
            {
                type = winnerInfo.GameType();
                switch (type)
                {
                    case "TicketPick2Straight":
                    case "TicketPick3Straight":
                    case "TicketPick4Straight":
                    case "TicketPick5Straight":
                    case "TicketPowerBallSingle":
                    case "TicketPowerBallPowerPlay":
                        {
                            break;
                        }
                    case "TicketPick2Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_2_WAY);

                            if (!winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize2.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize2.TransformToLoserInfoByPrizeBoxed());
                            }
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick3Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_3_WAY);

                            if (!winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize2.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize2.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize3.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize3.TransformToLoserInfoByPrizeBoxed());
                            }
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick4Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_4_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_12_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_24_WAY);

                            if (!winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize2.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize2.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize3.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize3.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize4.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize4.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize5.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize5.TransformToLoserInfoByPrizeBoxed());
                            }
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick5Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_5_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_10_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_20_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_30_WAY);
                            var winnerInfoByPrize6 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_60_WAY);
                            var winnerInfoByPrize7 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_120_WAY);

                            if (!winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize2.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize2.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize3.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize3.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize4.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize4.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize5.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize5.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize6.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize6.TransformToLoserInfoByPrizeBoxed());
                            }
                            if (!winnerInfoByPrize7.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize7.TransformToLoserInfoByPrizeBoxed());
                            }
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    default:
                        throw new Exception($"The type {type} is not valid.");
                }
            }

            return tickets;
        }

        private TicketTypeForReports ValidTicketTypesFor(string gameType)
        {
            TicketTypeForReports ticketTypes;
            switch (gameType)
            {
                case "All":
                    ticketTypes = TicketTypeForReports.P2B | TicketTypeForReports.P2S | TicketTypeForReports.P3B | TicketTypeForReports.P3S | TicketTypeForReports.P4B | TicketTypeForReports.P4S | TicketTypeForReports.P5B | TicketTypeForReports.P5S | TicketTypeForReports.PBS | TicketTypeForReports.PBP;
                    break;
                case "Pick":
                    ticketTypes = TicketTypeForReports.P2B | TicketTypeForReports.P2S | TicketTypeForReports.P3B | TicketTypeForReports.P3S | TicketTypeForReports.P4B | TicketTypeForReports.P4S | TicketTypeForReports.P5B | TicketTypeForReports.P5S;
                    break;
                case "Powerball":
                    ticketTypes = TicketTypeForReports.PBS | TicketTypeForReports.PBP;
                    break;
                default:
                    throw new Exception($"{nameof(gameType)} {gameType} is not valid.");
            }
            return ticketTypes;
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeAt(date, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByTimeFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeFrom(date, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByTimeIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeIn(drawDates, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByTimeBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        const int OrderNumberDefault = -1;
        internal List<CompletedWinnersTicketsByOrderAndPrize> MyWinnerTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.WinnerTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);

            var ticketsByOrderAndPrize = new Dictionary<string, CompletedWinnersTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                var key = ticket.OrderNumber == OrderNumberDefault ? 
                    $"{ticket.TicketNumber}{ticket.PrizeToWinForOneDollar}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}" :
                    $"{ticket.OrderNumber}{ticket.PrizeToWinForOneDollar}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}";
                if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                {
                    existingTicket.Add(ticket);
                }
                else
                {
                    ticketsByOrderAndPrize.Add(key, new CompletedWinnersTicketsByOrderAndPrize(ticket));
                }
            }
            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyWinnerKenoTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.WinnerTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);
            return result.ToList();
        }

        internal List<CompletedLosersTicketsByOrderAndPrize> MyLoserTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.LoserTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);

            var ticketsByOrderAndPrize = new Dictionary<string, CompletedLosersTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                {
                    var prize = CalculatePrizeForOneDollar(player.Company, ticket, subticket);
                    var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                    var key = ticket.OrderNumber == OrderNumberDefault ? 
                        $"{ticket.TicketNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}" : 
                        $"{ticket.OrderNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}";
                    if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                    {
                        existingTicket.Add(ticket, prize);
                    }
                    else
                    {
                        ticketsByOrderAndPrize.Add(key, new CompletedLosersTicketsByOrderAndPrize(ticket, prize));
                    }
                }
            }
            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyLoserKenoTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.LoserTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);
            return result.ToList();
        }

        internal List<CompletedNoActionTicketsByOrderAndPrize> MyNoActionTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.NoActionTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);

            var ticketsByOrderAndPrize = new Dictionary<string, CompletedNoActionTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                {
                    var prize = CalculatePrizeForOneDollar(player.Company, ticket, subticket);
                    var key = ticket.OrderNumber == OrderNumberDefault ? $"{ticket.TicketNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}" : $"{ticket.OrderNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}";
                    if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                    {
                        existingTicket.Add(ticket, prize);
                    }
                    else
                    {
                        ticketsByOrderAndPrize.Add(key, new CompletedNoActionTicketsByOrderAndPrize(ticket, prize));
                    }
                }
            }
            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyNoActionKenoTicketsBetween(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.NoActionTicketsOfPlayerBetween(startDate, endDate, customer.AccountNumber);
            return result.ToList();
        }

        decimal CalculatePrizeForOneDollar(Company company, TicketMessage messageInfo, SubTicket<IPick> subticket)
        {
            var prizes = company.Lotto900().Prizes;
            var versionNumber = messageInfo.PrizesVersion;
            var prizeCriteria = prizes.WayOfSubticket(messageInfo.TicketType, subticket);
            return prizes.Prize(versionNumber, messageInfo.TicketType, prizeCriteria);
        }

        internal List<CompletedWinnersTicketsByOrderAndPrize> MyWinnerTicketsOfDrawing(DateTime drawDate, string state)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.WinnerTicketsOfPlayer(drawDate, state, customer.AccountNumber);
            var ticketsByOrderAndPrize = new Dictionary<string, CompletedWinnersTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                var key = ticket.OrderNumber == OrderNumberDefault ? 
                    $"{ticket.TicketNumber}{ticket.PrizeToWinForOneDollar}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}" : 
                    $"{ticket.OrderNumber}{ticket.PrizeToWinForOneDollar}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}";
                if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                {
                    existingTicket.Add(ticket);
                }
                else
                {
                    ticketsByOrderAndPrize.Add(key, new CompletedWinnersTicketsByOrderAndPrize(ticket));
                }
            }

            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyWinnerKenoTicketsOfDrawing(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.WinnerTicketsOfPlayer(drawDate, customer.AccountNumber);
            return result.ToList();
        }

        internal List<CompletedLosersTicketsByOrderAndPrize> MyLoserTicketsOfDrawing(DateTime drawDate, string state)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.LoserTicketsOfPlayer(drawDate, state, customer.AccountNumber);

            var ticketsByOrderAndPrize = new Dictionary<string, CompletedLosersTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                {
                    var prize = CalculatePrizeForOneDollar(player.Company, ticket, subticket);
                    var fireball = ticket.BelongsToFireBallDraw ? "FB" : string.Empty;
                    var key = ticket.OrderNumber == OrderNumberDefault ? 
                        $"{ticket.TicketNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}" : 
                        $"{ticket.OrderNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}{ticket.TypeNumberSequenceAsText()}{fireball}";
                    if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                    {
                        existingTicket.Add(ticket, prize);
                    }
                    else
                    {
                        ticketsByOrderAndPrize.Add(key, new CompletedLosersTicketsByOrderAndPrize(ticket, prize));
                    }
                }
            }
            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyLoserKenoTicketsOfDrawing(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.LoserTicketsOfPlayer(drawDate, customer.AccountNumber);
            return result.ToList();
        }

        internal List<CompletedNoActionTicketsByOrderAndPrize> MyNoActionTicketsOfDrawing(DateTime drawDate, string state)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var tickets = queryMaker.NoActionTicketsOfPlayer(drawDate, state, customer.AccountNumber);

            var ticketsByOrderAndPrize = new Dictionary<string, CompletedNoActionTicketsByOrderAndPrize>();
            foreach (var ticket in tickets)
            {
                foreach (SubTicket<IPick> subticket in ticket.SubTickets())
                {
                    var prize = CalculatePrizeForOneDollar(player.Company, ticket, subticket);
                    var key = ticket.OrderNumber == OrderNumberDefault ? $"{ticket.TicketNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}" : $"{ticket.OrderNumber}{prize}{ticket.DrawDate()}{ticket.StateAbb}";
                    if (ticketsByOrderAndPrize.TryGetValue(key, out var existingTicket))
                    {
                        existingTicket.Add(ticket, prize);
                    }
                    else
                    {
                         ticketsByOrderAndPrize.Add(key, new CompletedNoActionTicketsByOrderAndPrize(ticket, prize));
                    }
                }
            }
            var result = ticketsByOrderAndPrize.Values.OrderByDescending(x => x.FirstTicket.Year).
                ThenByDescending(x => x.FirstTicket.Month).
                ThenByDescending(x => x.FirstTicket.Day).
                ThenByDescending(x => x.FirstTicket.Hour).
                ThenByDescending(x => x.FirstTicket.Minute).ToList();
            return result;
        }

        internal List<CompletedKenoTicket> MyNoActionKenoTicketsOfDrawing(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");

            var queryMaker = new QueryMakerOfHistoricalKeno(player.Company);
            var result = queryMaker.NoActionTicketsOfPlayer(drawDate, customer.AccountNumber);
            return result.ToList();
        }

        internal CompletedLastPicksDraws LastPlayedDrawings()
        {
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks();
            var result = queryMaker.LastPlayedDrawingsOfPlayer(customer.AccountNumber);
            return result;
        }

        internal CompletedKenoDraws LastPlayedKenoDrawings()
        {
            var queryMaker = new QueryMakerOfHistoricalKeno();
            var result = queryMaker.LastPlayedDrawingsOfPlayer(customer.AccountNumber);
            return result;
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateAt(date, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByStateFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateFrom(date, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByStateIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateIn(drawDates, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrizeWithTotalSubtickets MyWinnerTicketsByStateBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);
            var winnersInfoByPrize = Games.Lotto.Reports.TransformToWinnerInfoByPrize(winners.ToArray());
            var result = winnersInfoByPrize.OrderByDescending(x => x.MessageInfo.Year).
                ThenByDescending(x => x.MessageInfo.Month).
                ThenByDescending(x => x.MessageInfo.Day).
                ThenByDescending(x => x.MessageInfo.Hour).
                ThenByDescending(x => x.MessageInfo.Minute);
            return new WinnerInfoByPrizeWithTotalSubtickets(result);
        }

        internal WinnerInfoByPrize GetWinnerTicketBy(string state, DateTime creationDate, DateTime drawDate, decimal prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            WinnerInfoByPrize winnerInfoByPrize;
            var winner = queryMaker.GetWinnerTicketBy(state, creationDate, drawDate, customer.AccountNumber);
            if (winner != null && winner.WasCreatedByPattern() == wasCreatedByPattern)
            {
                var result = Games.Lotto.Reports.TransformToWinnerInfoByPrize(new WinnerInfo[] { winner });
                winnerInfoByPrize = result.FirstOrDefault(x => x.Prize == prize);
                if (winnerInfoByPrize == null) winnerInfoByPrize = new WinnerInfoByPrizeEmpty();
            }
            else
            {
                winnerInfoByPrize = new WinnerInfoByPrizeEmpty();
            }
            return winnerInfoByPrize;
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByTimeAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByTimeAt(date, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeAt(date, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByTimeFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByTimeFrom(date, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeFrom(date, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByTimeIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByTimeIn(drawDates, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeIn(drawDates, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByTimeBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByTimeBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByTimeBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByStateAt(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByStateAt(date, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateAt(date, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByStateFrom(DateTime date, string gameType, bool withLR)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByStateFrom(date, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateFrom(date, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByStateIn(string dates, string gameType, bool withLR)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByStateIn(drawDates, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateIn(drawDates, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrizeWithTotalSubtickets MyLoserTicketsByStateBetween(DateTime startedDate, DateTime endedDate, string gameType, bool withLR)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var losers = queryMaker.LoserTicketsOfPlayerByStateBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);
            var winners = queryMaker.WinnerTicketsOfPlayerByStateBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes, withLR);

            var loserByPrize = Games.Lotto.Reports.TransformToLoserInfoByPrize(losers.ToArray());
            var loserByPrizeInWinners = TransformWinnerInfoToLoserInfoByPrize(winners.ToArray());
            var result = loserByPrize.Concat(loserByPrizeInWinners).
                OrderByDescending(x => x.LoserInfo.Year).
                ThenByDescending(x => x.LoserInfo.Month).
                ThenByDescending(x => x.LoserInfo.Day).
                ThenByDescending(x => x.LoserInfo.Hour).
                ThenByDescending(x => x.LoserInfo.Minute);
            return new LoserInfoByPrizeWithTotalSubtickets(result);
        }

        internal LoserInfoByPrize GetLoserTicketBy(string state, DateTime creationDate, DateTime drawDate, decimal prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            LoserInfoByPrize loserInfoByPrize;
            var loser = queryMaker.GetLoserTicketBy(state, creationDate, drawDate, customer.AccountNumber);
            if (loser != null)
            {
                if (loser.WasCreatedByPattern() == wasCreatedByPattern)
                {
                    var result = Games.Lotto.Reports.TransformToLoserInfoByPrize(new LoserInfo[] { loser });
                    loserInfoByPrize = result.SingleOrDefault(x => x.Prize == prize);
                    if (loserInfoByPrize != null) return loserInfoByPrize;
                }
            }
            var winner = queryMaker.GetWinnerTicketBy(state, creationDate, drawDate, customer.AccountNumber);
            if (winner != null && winner.WasCreatedByPattern() == wasCreatedByPattern)
            {
                var result = TransformWinnerInfoToLoserInfoByPrize(winner);
                loserInfoByPrize = result.SingleOrDefault(x => x.Prize == prize);
                if (loserInfoByPrize == null) loserInfoByPrize = new LoserInfoByPrizeEmpty();
                return loserInfoByPrize;
            }
            
            return new LoserInfoByPrizeEmpty();
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByTimeAt(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByTimeAt(date, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByTimeFrom(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByTimeFrom(date, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByTimeIn(string dates, string gameType)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByTimeIn(drawDates, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByTimeBetween(DateTime startedDate, DateTime endedDate, string gameType)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByTimeBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByStateAt(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByStateAt(date, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByStateFrom(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByStateFrom(date, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByStateIn(string dates, string gameType)
        {
            if (String.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            List<DateTime> drawDates = DatesFrom(dates);
            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByStateIn(drawDates, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrizeWithTotalSubtickets MyNoActionTicketsByStateBetween(DateTime startedDate, DateTime endedDate, string gameType)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            var ticketTypes = ValidTicketTypesFor(gameType);
            var noActions = queryMaker.NoActionTicketsOfPlayerByStateBetween(startedDate, endedDate, customer.AccountNumber, ticketTypes);
            var noActionsInfoByPrize = Games.Lotto.Reports.TransformToNoActionInfoByPrize(noActions.ToArray());
            var result = noActionsInfoByPrize.
                OrderByDescending(x => x.NoActionInfo.Year).
                ThenByDescending(x => x.NoActionInfo.Month).
                ThenByDescending(x => x.NoActionInfo.Day).
                ThenByDescending(x => x.NoActionInfo.Hour).
                ThenByDescending(x => x.NoActionInfo.Minute);
            return new NoActionInfoByPrizeWithTotalSubtickets(result);
        }

        internal NoActionInfoByPrize GetNoActionTicketBy(string state, DateTime creationDate, DateTime drawDate, decimal prize, bool wasCreatedByPattern)
        {
            if (String.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (creationDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (prize <= 0) throw new GameEngineException($"{nameof(prize)} must be greater than 0");

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(player.Company);
            NoActionInfoByPrize noActionInfoByPrize;
            var noAction = queryMaker.GetNoActionTicketBy(state, creationDate, drawDate, customer.AccountNumber);
            if (noAction != null && noAction.WasCreatedByPattern() == wasCreatedByPattern)
            {
                var result = Games.Lotto.Reports.TransformToNoActionInfoByPrize(new NoActionInfo[] { noAction });
                noActionInfoByPrize = result.SingleOrDefault(x => x.Prize == prize);
                if (noActionInfoByPrize == null) noActionInfoByPrize = new NoActionInfoByPrizeEmpty();
            }
            else
            {
                noActionInfoByPrize = new NoActionInfoByPrizeEmpty();
            }
            return noActionInfoByPrize;

        }
    }
}
