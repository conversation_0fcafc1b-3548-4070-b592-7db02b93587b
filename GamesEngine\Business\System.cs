﻿using GamesEngine.Business.Liquidity;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime.RestAPI;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business
{
    class System: Objeto
    {
        internal WholeEntities Entities { get; }

        internal WholeTransactionTypes TransactionTypes { get; }
        internal IEnumerable<ProcessorTransaction> VisibleTransactionTypes => TransactionTypes.VisibleMembers.Cast<ProcessorTransaction>().ToList();
        internal IEnumerable<ProcessorCoin> EnabledCoins => Coins.EnabledMembers.Cast<ProcessorCoin>().ToList();
        internal WholePaymentMethods PaymentMethods { get; }

        [Obsolete("Drivers: Do not use PaymentProcessor anymore, use DriverManagers instead")]
        internal WholePaymentProcessor PaymentProcessor => DriverManagers;
        internal WholePaymentProcessor DriverManagers { get; }

        internal WholeTenants Tenants { get; } 

        internal WholeCoins Coins { get; }

        internal Domains.Domains Domains { get; } 

        internal Stores Stores { get; }

        internal LiquidFlow LiquidFlow { get; }

        internal EndpointServiceMonitor ServiceMonitor { get; }

        Company company;
        public System(Company company)
        {
            this.company = company;
            Stores = company.Sales.Stores;
            Domains = new Domains.Domains(company);
            PaymentMethods = new WholePaymentMethods(company);
            DriverManagers = new WholePaymentProcessor(company);
            TransactionTypes = new WholeTransactionTypes(company);
            Entities = new WholeEntities(company);
            Tenants = new WholeTenants(company);
            Coins = new WholeCoins(company);
            LiquidFlow = LiquidFlow.Instance(company.Sales.AllDomains);
            ServiceMonitor = EndpointServiceMonitor.Instance();
        }


    }
}
