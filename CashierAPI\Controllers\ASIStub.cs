﻿using GamesEngine;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace ASIStub.Controllers
{
    public class ASIStub : AuthorizeController
	{
		private static int authorization = 2000;
		private decimal balance = 9000;
		[HttpPost("/v1/5dimesAPI/PostTransaction")]
		[AllowAnonymous]
		public int PostTransaction()
		{
			authorization++;
			return authorization;
		}

		[HttpPost("/v1/5dimesAPI/PostTransactionWReference")]
		[AllowAnonymous]
		public int PostTransactionWReference()
		{
			authorization++;
			return authorization;
		}
		
		[HttpPost("/v1/5dimesAPI/GetCustomerBalance")]
		[AllowAnonymous]
		public decimal GetBalance ()
		{
			return balance;
		}

		[HttpPost("/v1/5dimesAPI/SvcValidateCustomer")]
		[AllowAnonymous]
		public IActionResult ValidateCustomer()
		{
			return Ok($@"{{""Agent"" :""aa""}}");
		}

		[HttpPost("/v1/5dimesAPI/PostFreeFormTicket")]
		[AllowAnonymous]
		public int GetAuthorization()
		{
			authorization++;
			return authorization;
		}
	}
}
