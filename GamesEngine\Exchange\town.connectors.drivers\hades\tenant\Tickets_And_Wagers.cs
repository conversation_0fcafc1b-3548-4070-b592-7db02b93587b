﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class Tickets_And_Wagers : ASITenantDriver
	{
		private HttpClient _getTicketWagersClient;
		private string SystemId;
        private string SystemPassword;
        public string CcompanyBaseUrlServices { get; private set; }
		public Tickets_And_Wagers() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("ticketNumber");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			CcompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getTicketWagersClient == null)
			{
				_getTicketWagersClient = new HttpClient
				{
					BaseAddress = new Uri(CcompanyBaseUrlServices)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CcompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_getTicketWagersClient = new HttpClient
					{
						BaseAddress = new Uri(CcompanyBaseUrlServices)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			var TicketNumber = recordSet.Mappings["ticketNumber"];

			int ticketNumber = TicketNumber.AsInt;
			var result = await TicketWagersAsync(now, ticketNumber);
			return (T)Convert.ChangeType(result, typeof(T));

		}
		private async Task<PostFreeFormWagerCollectionWagers> TicketWagersAsync(DateTime now, int ticketNumber)
		{
			Debug.WriteLine($"Accounting service {nameof(GetTicketWagersAsync)} received {nameof(ticketNumber)}:{ticketNumber}");
			var result = await GetTicketWagersAsync(now, ticketNumber);
			return result;
		}
		private async Task<PostFreeFormWagerCollectionWagers> GetTicketWagersAsync(DateTime now, int ticketNumber)
		{
			var values = new GetTicketWagersBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				TicketNumber = ticketNumber.ToString()
			};
			const string url = "/v1/5dimesAPI/GetTicketWagers";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASIGetTicketWagers);

			if (ticketNumber <= 0)
			{
				NotifyError(nameof(GetTicketWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}", $"nRequest cannot be thrown for {nameof(ticketNumber)} {ticketNumber}");
				return CreateEmptyGetTicketWagers();
			}

			string responseString = "";
			int retryNumber = 0;
			const int MAX_RETRIES = 10;

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIGetTicketWagers.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var response = await _getTicketWagersClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();

					Loggers.GetIntance().AccountingServicesASIGetTicketWagers.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIGetTicketWagers.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
					InternalOnError(nameof(GetTicketWagersAsync), retryNumber, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);

					await Task.Delay(5000);

					if (retryNumber == MAX_RETRIES)
					{
						return CreateEmptyGetTicketWagers();
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetTicketWagersAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return CreateEmptyGetTicketWagers();
			}
			else
			{
				var objectResponse = Commons.FromJson<PostFreeFormWagerCollectionWagers>($"{{\"wagers\":{responseString}}}");
				return objectResponse;
			}
		}

    }
}
