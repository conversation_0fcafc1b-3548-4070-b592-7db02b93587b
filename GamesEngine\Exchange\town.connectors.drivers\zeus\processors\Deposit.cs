﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.zeus;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.zeus
{
    public class Deposit : DGSProcessorDriver
    {
        private RestClient _postDepositClient;

        public Deposit()
            : base(TransactionType.Deposit)
        {
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (_postDepositClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(AppToken))
                {
                    DGSTenantDriver.RequestAppToken(ServicesUrl, SystemId, Password);
                }

                _postDepositClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        DGSTenantDriver.RequestAppToken(ServicesUrl, SystemId, Password);
                    }

                    _postDepositClient = new RestClient(ServicesUrl);
                }
            }

            var authorizationNumber = AddAmount(now, recordSet);

            var result = new DepositTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        private int AddAmount(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (string.IsNullOrWhiteSpace(AppToken)) throw new Exception($"{nameof(AppToken)} is empty");

            var CustomerId = recordSet.Mappings["customerId"];
            var Amount = recordSet.Mappings["amount"];
            var Description = recordSet.Mappings["description"];

            string customerId = CustomerId.AsString;
            decimal amount = Amount.AsDecimal;
            string description = Description.AsString;

            if (string.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (amount <= 0) throw new Exception($"{nameof(amount)} {amount} is not valid to send request");

            var values = new CreditAndDebitTransactionBody()
            {
                idplayer = customerId,
                amount = amount,
                description = description,
                token = SUBSTITUTE_PLAYER_TOKEN
            };
            const string url = "/InsertCreditTransaction";
            var jsonString = Commons.ToJson(values);

            string responseString = "";
            string valuesWithHiddenFields = $@"idPlayer:{customerId}, description:{description}, amount:{amount}";
            var authorizationNumber = FAKE_DOCUMENT_NUMBER;
            try
            {
                Loggers.GetIntance().AccountingServicesDeposit.Debug($@"url:{url} data:{valuesWithHiddenFields}");

                var request = new RestRequest(url, Method.Post);
                request.AddHeader("Authorization", $"Bearer {AppToken}");
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var response = _postDepositClient.Execute(request);
                responseString = response.Content;
                Loggers.GetIntance().AccountingServicesDeposit.Debug($@"response:{responseString}");
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingServicesDeposit.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
                InternalOnError(nameof(AddAmount), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(AddAmount), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
            }
            else
            {
                var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
                if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                {
                    Loggers.GetIntance().AccountingServicesDeposit.Debug($"{nameof(AddAmount)}\nUrl:{url}\nResponse: {responseString}");
                }
                else
                {
                    try
                    {
                        var responseObj = Commons.FromJson<CreditAndDebitTransactionResponse>(responseString);
                        authorizationNumber = responseObj.transactionId;

                        if (authorizationNumber <= 0)
                        {
                            NotifyWarn(nameof(AddAmount), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(AddAmount)} has an invalid {nameof(authorizationNumber)} '{authorizationNumber}' for {customerId}");
                        }
                    }
                    catch (Exception e)
                    {
                        NotifyWarn(nameof(AddAmount), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                    }
                }
            }
            return authorizationNumber;
        }
        public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("referenceID");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
