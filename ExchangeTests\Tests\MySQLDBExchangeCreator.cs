﻿using ExchangeAPI.ExchangeEntities;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ExchangeTests.Tests
{
    public class MySQLDBExchangeCreator: DBExchangeCreator
    {
        
        public MySQLDBExchangeCreator(string strConnection)
        : base(
            new DbContextOptionsBuilder<freshContext>()
                .UseMySql(ServerVersion.AutoDetect(strConnection))
                .Options)
        {
        }
    }
}
