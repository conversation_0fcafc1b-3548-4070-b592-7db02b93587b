﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Games.Lotto
{
	class SenderToCashier
	{
		internal void WriteDebitDataForRegrade(KafkaMessagesBuffer withDrawalBuffer, Ticket ticket, string sequenceOfNumbers, int version, string who)
		{
			if (string.IsNullOrEmpty(who)) throw new ArgumentNullException(nameof(who));
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (ticket.Order.CurrencyCode.ToString() != ticket.Lottery.PicksLotteryGame.RewardCurrency) throw new GameEngineException("This is not a free ticket");
			if (ticket.TicketNumber == Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER) return;

			var storeId = ticket.SoldBy.Id;
            if (!Enum.TryParse(ticket.Lottery.PicksLotteryGame.StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {ticket.Lottery.PicksLotteryGame.StandardCurrency} does not exists");
            foreach (TicketWager wager in ticket.FindWagersWithThisSequence(sequenceOfNumbers))
			{
				decimal prize = wager.ToWin + ticket.BetAmount(); //Dont use wager.Risk, coz in balls selection has another amount;

				string reference = $"{wager.ReferenceNumber}-{version}";
				if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                //var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                var processorKey = string.Empty;
                WithdrawMessage info = new WithdrawMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: Coinage.Coin(ticket.Lottery.PicksLotteryGame.StandardCurrency),
					storeId: storeId,
					domain:ticket.DomainUrl,
					amount: prize,
					who: who,
					description: $"Lotto{fireballTag} Free Play Regrade for Ticket ID #{ticket.TicketNumber}-{wager.WagerNumber} {wager.BetDescription}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
					processorKey: processorKey
                );
				info.NotifyPlayer = true;
                withDrawalBuffer.Send(info);
			}
		}

		internal void WriteDebitDataForNoAction(KafkaMessagesBuffer withDrawalBuffer, Ticket ticket, int version, string who)
		{
			if (string.IsNullOrEmpty(who)) throw new ArgumentNullException(nameof(who));
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (ticket.Order.CurrencyCode.ToString() != ticket.Lottery.PicksLotteryGame.RewardCurrency && ticket.Order.CurrencyCode.ToString() != Currencies.CODES.KRW.ToString()) return;
			if (ticket.TicketNumber == Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER) return;

			var storeId = ticket.SoldBy.Id;
			var sequenceOfNumbers = !ticket.IsNoAction() ? ticket.Draw.PreviousSequenceOfNumber : ticket.NoActionDraw.PreviousSequenceOfNumber;
            if (!Enum.TryParse(ticket.Lottery.PicksLotteryGame.StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {ticket.Lottery.PicksLotteryGame.StandardCurrency} does not exists");
            foreach (TicketWager wager in ticket.FindWagersWithThisSequence(sequenceOfNumbers))
			{
				decimal prize = wager.ToWin + ticket.BetAmount(); //Dont use wager.Risk, coz in balls selection has another amount;

				string reference = $"{wager.ReferenceNumber}-{version}";
				if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                //var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                var processorKey = string.Empty;
                WithdrawMessage info = new WithdrawMessage(
					atAddress: ticket.Player.AccountNumber,
					currency: Coinage.Coin(ticket.Lottery.PicksLotteryGame.StandardCurrency),
					storeId: storeId,
					domain:ticket.DomainUrl,
					amount: prize,
					who: who,
					description: $"Lotto{fireballTag} Free Play No Action for Ticket ID #{ticket.TicketNumber}-{wager.WagerNumber} {wager.BetDescription}",
					reference: reference,
					agent: ticket.Player.Agent,
					processorId: WholePaymentProcessor.NoPaymentProcessor,
                    processorKey: processorKey
                );
                info.NotifyPlayer = true;
                withDrawalBuffer.Send(info);
			}
		}

		internal void WriteCreditData(KafkaMessagesBuffer depositBuffer, Ticket ticket, int version, string who)
		{
			if (string.IsNullOrEmpty(who)) throw new ArgumentNullException(nameof(who));
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (ticket.Order.CurrencyCode.ToString() != ticket.Lottery.PicksLotteryGame.RewardCurrency && ticket.Order.CurrencyCode.ToString() != Currencies.CODES.KRW.ToString()) return;
			if (ticket.TicketNumber == Ticket.DEFAULT_UNASSIGNED_TICKET_NUMBER) return;

			var storeId = ticket.SoldBy.Id;
            if (!Enum.TryParse(ticket.Lottery.PicksLotteryGame.StandardCurrency, out Currencies.CODES currency)) throw new GameEngineException($"Currency code {ticket.Lottery.PicksLotteryGame.StandardCurrency} does not exists");
            foreach (TicketWager wager in ticket.Wagers)
			{
				if (wager.IsWinner())
				{
					decimal prize = ticket.Payout; //Dont use wager.Risk, coz in balls selection has another amount;

					string reference = version == 0 ? wager.ReferenceNumber :
						$"{wager.ReferenceNumber}-{version}";
					if (reference.Length > Movement.MAX_REFERENCE_LENGTH) reference = reference.Substring(0, Movement.MAX_REFERENCE_LENGTH);
                    var fireballTag = ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
                    //var processorKey = ticket.Player.Agent == Agents.ARTEMIS ? WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Connectors.town.connectors.drivers.artemis.Grade)).ProcessorKey : string.Empty;
                    var processorKey = string.Empty;
                    DepositMessage info = new DepositMessage(
						atAddress: ticket.Player.AccountNumber,
						currency: Coinage.Coin(ticket.Lottery.PicksLotteryGame.StandardCurrency),
						storeId: storeId,
						domain:ticket.DomainUrl,
						amount: prize,
						who: who,
						description: $"Lotto{fireballTag} Free Play Win for Ticket ID #{ticket.TicketNumber}-{wager.WagerNumber} {wager.BetDescription}",
						reference: reference,
						agent: ticket.Player.Agent,
						processorId: WholePaymentProcessor.NoPaymentProcessor,
						processorKey: processorKey
                    );
					depositBuffer.Send(info);
				}
			}
		}
	}
}
