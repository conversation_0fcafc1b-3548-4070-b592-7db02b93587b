﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class CustomerInformation : ASITenantDriver
	{
		private HttpClient _getLottoCustomerClient;
		private string SystemId;
        private string SystemPassword;
		public CustomerInformation() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getLottoCustomerClient == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
				_getLottoCustomerClient = new HttpClient
				{
					BaseAddress = new Uri(companyBaseUrlServices)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_getLottoCustomerClient = new HttpClient
					{
						BaseAddress = new Uri(companyBaseUrlServices)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			var CustomerId = recordSet.Mappings["customerId"];

			var result = await GetLottoCustomerAsync(now, CustomerId.AsString);
			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
		}
		private async Task<bool> GetLottoCustomerAsync(DateTime now, string customerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

			var values = new LottoCustomer()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				CustomerId = customerId
			};

			var url = "/v1/5dimesAPI/GetLottoCustomer";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string responseString = "";
			int retryNumber = 0;
			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASIGetLottoCustomer);

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIGetLottoCustomer.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var response = await _getLottoCustomerClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();

					Loggers.GetIntance().AccountingServicesASIGetLottoCustomer.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIGetLottoCustomer.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					await Task.Delay(5000);
					if (retryNumber == MAX_RETRIES)
					{
						return false;
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetLottoCustomerAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return false;
			}
			else
			{
				var errorResponse = Commons.FromJson<JsonErrorResponse>(responseString);
				if (errorResponse != null)
				{
					NotifyWarn(nameof(GetLottoCustomerAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");
					return false;
				}
				else
				{
					var isSuccessful = Convert.ToBoolean(responseString);
					return isSuccessful;
				}
			}
		}
	}
}
