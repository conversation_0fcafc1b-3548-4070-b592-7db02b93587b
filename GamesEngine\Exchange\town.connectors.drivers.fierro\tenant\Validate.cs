﻿using System;
using System.Threading.Tasks;

namespace town.connectors.drivers.fiero
{
    internal class Validate : FieroTenantDriver
	{
		public Validate() : base(Tenant_Actions.Validate, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override void Prepare(DateTime now)
		{

			//CustomSettings.Prepare();
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			await Task.Yield();
			var result = true;

			return (T)Convert.ChangeType(result, typeof(T));
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = true;

			return (T)Convert.ChangeType(result, typeof(T));
		}
    }
}
