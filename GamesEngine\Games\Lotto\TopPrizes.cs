﻿using GamesEngine.Games.Lotto;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games
{
	[Puppet]
	internal class TopPrizes : Objeto
	{
		private TopsMonth topPrizes;

		internal TopPrizes(int topPrizesNumber)
		{
			if (topPrizesNumber <= 0) throw new GameEngineException("Number of top prizes must be upper than zero.");

			this.topPrizes = new TopsMonth(topPrizesNumber);
		}

		internal void InsertTop(int pickNumber, string winnerNumber, decimal prize, string draw, DateTime drawDate)
		{
			topPrizes.InsertTop(pickNumber, winnerNumber, prize, draw, drawDate);
		}

		internal void DeleteTop(int pickNumber, string previousWinnerNumber, decimal prize, string draw, DateTime drawDate)
		{
			topPrizes.DeleteTop(pickNumber, previousWinnerNumber, prize, draw, drawDate);
		}

		internal IEnumerable<PrizeElement> TopOfMonth()
		{
			return topPrizes.TopOfMonth();
		}

		internal IEnumerable<PrizeElement> TopOfDay()
		{
			return topPrizes.TopOfDay();
		}

		private class TopsMonth
		{
			private readonly int topPrizesNumber;
			private const int MONTH = 1;
			private readonly Dictionary<DateTime, TopsDay> elements;

			internal TopsMonth(int topPrizesNumber)
			{
				if (topPrizesNumber <= 0) throw new GameEngineException("Number of top prizes must be upper than zero.");
				
				this.topPrizesNumber = topPrizesNumber;
				this.elements = new Dictionary<DateTime, TopsDay>();
			}

			internal void InsertTop(int pickNumber, string winnerNumber, decimal prize, string draw, DateTime drawDate)
			{
				DateTime day = drawDate.Date;
				DeleteOlderTops(drawDate.Date.AddMonths(-MONTH));

				TopsDay recent;
				if(! elements.TryGetValue(day, out recent))
				{
					recent = new TopsDay(this.topPrizesNumber, day);
					elements.Add(day, recent);
				}

				recent.InsertTop(pickNumber, winnerNumber, prize, draw, day);
			}

			internal void DeleteTop(int pickNumber, string winnerNumber, decimal prize, string draw, DateTime drawDate)
			{
				DateTime day = drawDate.Date;

				TopsDay toDelete;
				if (elements.TryGetValue(day, out toDelete))
				{
					toDelete.DeleteTop(pickNumber, winnerNumber, prize, draw, day);
				}
			}

			private void DeleteOlderTops(DateTime upperDate)
			{
				var toDelete = elements.Keys.Where(x => x.Date <= upperDate).ToList();
				foreach (var delete in toDelete)
				{
					elements.Remove(delete);
				}
			}

			internal IEnumerable<PrizeElement> TopOfMonth()
			{
				decimal minTop = 0;
				List<PrizeElement> result = new List<PrizeElement>();
				foreach(TopsDay t in elements.Values)
				{
					var bestOfADay = t.BetterThan(minTop);
					foreach(var element in bestOfADay)
					{
						InsertTop(element, ref result);
					}
				}
				return result;
			}

			internal IEnumerable<PrizeElement> TopOfDay()
			{
				List <PrizeElement> result = new List<PrizeElement>();
				if (elements.Count == 0) return result;

				var recentDay = elements.Keys.Max();
				var topOfDay = elements[recentDay];
				result = topOfDay.TopOfDay().ToList();
				return result;
			}

			private void InsertTop(PrizeElement newTop, ref List<PrizeElement> result)
			{
				var lastElement = result.Count == 0 ? null : result.Last();

				if (lastElement == null || lastElement.Prize > newTop.Prize)
				{
					result.Add(newTop);
				}
				else
				{
					for (int i = 0; i < result.Count; i++)
					{
						var currElement = result[i];
						if (currElement.Prize <= newTop.Prize)
						{
							result.Insert(i, newTop);
							break;
						}
					}
				}

				while (result.Count > topPrizesNumber)
				{
					if (result.Last().Prize != result[topPrizesNumber - 1].Prize)
					{
						result.RemoveAt(result.Count - 1);
					}
					else
					{
						break;
					}
				}
			}
		}

		private class TopsDay
		{
			private readonly int topPrizesNumber;
			private readonly DateTime day;
			private readonly List<PrizeElement> elements;

			internal TopsDay(int topPrizesNumber, DateTime day)
			{
				if (topPrizesNumber <= 0) throw new GameEngineException("Number of top prizes must be upper than zero.");
				if (day.Date != day) throw new GameEngineException("It must be a date without hours, minutes and seconds");

				this.topPrizesNumber = topPrizesNumber;
				this.day = day;
				this.elements = new List<PrizeElement>();
			}

			internal void InsertTop(int pickNumber, string winnerNumber, decimal prize, string draw, DateTime day)
			{
				var lastElement = elements.Count == 0 ? null : elements.Last();
				var newTop = new PrizeElement(pickNumber, winnerNumber, prize, draw, day);

				if (lastElement == null || lastElement.Prize > prize)
				{
					elements.Add(newTop);
				}
				else
				{
					for (int i = 0; i < elements.Count; i++)
					{
						var currElement = elements[i];
						if (currElement.Prize <= prize)
						{
							elements.Insert(i, newTop);
							break;
						}
					}
				}

				while (elements.Count > topPrizesNumber)
				{
					if(elements.Last().Prize != elements[topPrizesNumber-1].Prize)
					{
						elements.RemoveAt(elements.Count - 1);
					}
					else
					{
						break;
					}
				}
			}

			internal void DeleteTop(int pickNumber, string winnerNumber, decimal prize, string draw, DateTime day)
			{
				int indexToDelete = elements.FindIndex(x => x.Day == day && x.Draw == draw);
				if(indexToDelete != -1)
					elements.RemoveAt(indexToDelete);
			}

			internal IEnumerable<PrizeElement> BetterThan(decimal prize)
			{
				var result = elements.Where(x => x.Prize >= prize);
				return result;
			}

			internal IEnumerable<PrizeElement> TopOfDay()
			{
				return elements;
			}
		}
	}
}
