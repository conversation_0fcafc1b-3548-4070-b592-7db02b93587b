﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Games.Tournaments;
using GamesEngine.Middleware.Providers;
using GamesEngine.Preferences.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	[Puppet]
	internal class Betboard : Objeto
	{
		private readonly Book book;
		private readonly Tournament tournament;
		private Leagues leagues;
		private readonly LinesCatalog catalog;
		private readonly Dictionary<int, Line> lines;
		private readonly BetRanges betRanges = new BetRanges();
		private readonly PresetBetAmounts presetBetAmounts = new PresetBetAmounts();
		private readonly LinesDefaultPreferences defaultPreferences;
		private readonly Players players;

		internal Betboard(Book book, Tournament tournament, Players players)
		{
			if (book == null) throw new ArgumentNullException(nameof(book));
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			if (players == null) throw new ArgumentNullException(nameof(players));

			this.book = book;
			this.tournament = tournament;
			this.catalog = tournament.LinesCatalog;
			this.lines = new Dictionary<int, Line>();
			this.defaultPreferences = new LinesDefaultPreferences();
			this.players = players;
		}

		internal Matchday Matchday(DateTime day)
		{
			if (day == default(DateTime)) throw new ArgumentNullException(nameof(day));
			if (day.Hour != 0 || day.Minute != 0 || day.Second != 0 || day.Millisecond != 0) throw new GameEngineException($"Parameter Day must be an exact day but {day.ToShortTimeString()} has hours");

			TournamentSchedule schedule = this.tournament.Schedule;
			return schedule.Matchday(day);
		}

		internal void AddLine(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Showcase.Betboard != this) throw new GameEngineException("This line does not belong to this betboard");
			if (line.Game.Sport != this.tournament.Sport) throw new GameEngineException($"Line {line.LineId} does not belong to the same sport");
			if (line.Game.Tournament != this.tournament) throw new GameEngineException($"Game associated to line {line.LineId} does not belong at the same Tournament");

			var originalLine = line.OriginalVersion;
			this.lines[originalLine.LineId] = line;
		}

		internal Book Book
		{
			get
			{
				return this.book;
			}
		}

		internal IEnumerable<Showcase> ExpiredShowcases(DateTime date)
		{
			var hoursKeepingAliveWagers = this.Company.Settings.HoursKeepingAliveWagers;
			var limitDate = date.AddHours(-hoursKeepingAliveWagers);
			var result = lines.Values.Where(line => line.IsInMemory && line.Showcase.Matchday.Day < limitDate).Select(x => x.Showcase).Distinct();
			
			return result;
		}

		internal void RemoveLine(Line line)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (line.Showcase.Betboard != this) throw new GameEngineException("This line does not belong to this betboard");

			var originalLine = line.OriginalVersion;
			this.lines.Remove(originalLine.LineId);
		}

		internal Line GetLine(int lineId)
		{
			Line result;
			if (!this.lines.TryGetValue(lineId, out result)) throw new GameEngineException("This line is unknown or it is not available anymore");
			return result;
		}

		internal Line SearchLineByAlias(int providerId, string aliasId)
        {
			if (providerId <= 0) throw new GameEngineException($"{nameof(providerId)} {providerId} must be greather than zero.");
			if (string.IsNullOrWhiteSpace(aliasId)) throw new ArgumentNullException(nameof(aliasId));

			var provider = ProvidersCollection.Instance().ById(providerId);
			var result = lines.Values.FirstOrDefault(line => line.Aliases.HasForeingAliasId(provider, aliasId));
			if (result == null) throw new ArgumentNullException(nameof(result));
			return result;
        }

		internal bool ExistLineByAlias(int providerId, string aliasId)
		{
			if (providerId <= 0) throw new GameEngineException($"{nameof(providerId)} {providerId} must be greather than zero.");
			if (string.IsNullOrWhiteSpace(aliasId)) throw new ArgumentNullException(nameof(aliasId));

			var provider = ProvidersCollection.Instance().ById(providerId);
			var result = lines.Values.FirstOrDefault(line => line.Aliases.HasForeingAliasId(provider, aliasId));
			return (result != null);
		}

		internal List<Line> AllLines()
		{
			// Quitar, nadie debe usar allLines
			var result = this.lines.Values.OrderBy(line => line.ShelveIndex).ThenBy(line => line.Index).ToList();
			return result;
		}

		internal bool ExistLine(int lineId)
		{
			var result = this.lines.ContainsKey(lineId);
			return result;
		}

		internal void AcceptFrom(Tournament tournament)
		{
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			League league = tournament.League;
			if (this.leagues == null) this.leagues = new Leagues();
			leagues.Add(league);
		}

		internal List<League> Leagues(Sport sport)
		{
			if (sport == null) throw new ArgumentNullException(nameof(sport));
			if (!leagues.Contains(sport)) throw new GameEngineException($"this sport is not accepted in the book");
			return leagues.BySport(sport);
		}

		internal void ForgetGame(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));
			if (game.Sport != this.tournament.Sport) throw new GameEngineException($"This betboard is not for {game.Sport.Name} Sport.");
			if (game.Tournament != this.tournament) throw new GameEngineException($"This betboard is not for {game.Tournament.Description} Tournament.");

			this.players.Forget(game);
		}

		private int uniqueLineId = 0;
		internal int NextLineId()
		{
			return uniqueLineId + 1;
		}

		internal void SetLastLineId(int lastLineId)
		{
			if (lastLineId <= uniqueLineId) throw new GameEngineException($"Last line id {lastLineId} can not be the last one because {uniqueLineId} was registered");
			uniqueLineId = lastLineId;
		}

		internal void IncludeDomainForActivations(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var showcases = lines.Values.Select(line => line.Showcase).Distinct();
			foreach (var showcase in showcases)
            {
				showcase.IncludeDomainForActivations(domain);
            }
		}

		internal Company Company
		{
			get
			{
				return this.tournament.Tournaments.Company;
			}
		}

		internal Tournament Tournament
		{
			get
			{
				return this.tournament;
			}
		}

		internal LinesCatalog Catalog
		{
			get
			{
				return this.catalog;
			}
		}

		internal BetRanges BetRanges
		{
			get
			{
				return betRanges;
			}
		}

		internal PresetBetAmounts PresetBetAmounts
		{
			get
			{
				return presetBetAmounts;
			}
		}

		internal LinesDefaultPreferences DefaultPreferences
		{
			get
			{
				return defaultPreferences;
			}
		}

	}
}
