﻿using GamesEngine.Gameboards.Lotto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal class TicketsWithCommonDomain
    {
        private List<Ticket> ticketsInDomain = new List<Ticket>();
        internal IEnumerable<Ticket> GetAll
        {
            get
            {
                return ticketsInDomain;
            }
        }
        private int drawingId;
        private int domainId;

        internal Lottery Lottery { get; private set; }

        internal DateTime DrawDate { get; private set; }

        internal TicketsWithCommonDomain(int drawingId, int domainId)
        {
            this.drawingId = drawingId;
            this.domainId = domainId;
        }

        internal void TryToAdd(Ticket ticket)
        {
            if (ticket.DomainId == domainId)
            {
                if (Lottery != null && Lottery != ticket.Lottery) throw new GameEngineException($"Collection have {nameof(Lottery)} {Lottery} but new ticket has a different {nameof(Lottery)}");
                Lottery = Lottery ?? ticket.Lottery;
                if (DrawDate != default(DateTime) && DrawDate != ticket.DrawDate) throw new GameEngineException($"Collection have {nameof(DrawDate)} {DrawDate} but new ticket has {nameof(DrawDate)} {ticket.DrawDate}");
                DrawDate = ticket.DrawDate;
                ticketsInDomain.Add(ticket);
            }
        }

        internal bool IsEmpty()
        {
            return ticketsInDomain.Count == 0;
        }
    }
}
