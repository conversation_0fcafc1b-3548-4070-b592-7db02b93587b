﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class PayoutDetail: ConsignmentTenantDriver
    {
        public PayoutDetail() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetPayoutDetailAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<PayoutsDetailResponse> GetPayoutDetailAsync(CustomSettings.RecordSet recordSet)
        {
            ResponsePayoutDetailEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Get_PayoutDetail";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetPayoutDetail.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetPayoutDetail.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponsePayoutDetailEnvelope));
                responseXml = (ResponsePayoutDetailEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetPayoutDetail.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetPayoutDetailAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.Get_PayoutDetailResponse.Get_PayoutDetailResult))
            {
                NotifyWarn(nameof(GetPayoutDetailAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                XmlSerializer serializer = new XmlSerializer(typeof(PayoutsDetailResponse));
                StringReader reader = new StringReader(responseXml.Body.Get_PayoutDetailResponse.Get_PayoutDetailResult);
                var result = (PayoutsDetailResponse)serializer.Deserialize(reader);
                return result;
            }

            return new PayoutsDetailResponse();
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var id = recordSet.Mappings["id"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Get_PayoutDetail xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<id>").Append(id).AppendLine("</id>");
            xmlBuilder.AppendLine("</Get_PayoutDetail>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Get_PayoutDetail xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<id>").Append(id).AppendLine("</id>");
            xmlBuilderWithHiddenFields.AppendLine("</Get_PayoutDetail>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponsePayoutDetailEnvelope
        {
            public ResponsePayoutDetailBody Body { get; set; }
        }

        public class ResponsePayoutDetailBody
        {
            [XmlElement(ElementName = "Get_PayoutDetailResponse", Namespace = "http://tempuri.org/")]
            public GetPayoutDetailResponse Get_PayoutDetailResponse { get; set; }
        }

        public class GetPayoutDetailResponse
        {
            public string Get_PayoutDetailResult { get; set; }
        }

        [XmlRoot("payout")]
        public class PayoutsDetailResponse
        {
            [XmlElement("Payout")]
            public List<PayoutResponse> Payouts { get; set; }
        }

        [XmlRoot("Payout")]
        public class PayoutResponse
        {
            [XmlAttribute("MerchId")]
            public string MerchId { get; set; }
            [XmlAttribute("Country")]
            public string Country { get; set; }
            [XmlAttribute("State")]
            public string State { get; set; }
            [XmlAttribute("City")]
            public string City { get; set; }
            [XmlAttribute("StatusId")]
            public string StatusId { get; set; }
            [XmlAttribute("StatusName")]
            public string StatusName { get; set; }
            [XmlAttribute("ControlNum")]
            public string ControlNum { get; set; }
            [XmlAttribute("ReceiversName")]
            public string ReceiversName { get; set; }
            [XmlAttribute("SendersName")]
            public string SendersName { get; set; }
            [XmlAttribute("SendersAddress")]
            public string SendersAddress { get; set; }
            [XmlAttribute("Amount")]
            public string Amount { get; set; }
            [XmlAttribute("fees")]
            public string fees { get; set; }
            [XmlAttribute("Notes")]
            public string Notes { get; set; }
            [XmlAttribute("CreatedDate")]
            public string CreatedDate { get; set; }
            [XmlAttribute("ConfirmedDate")]
            public string ConfirmedDate { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("id");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
