﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Settings;
using GamesEngine.Tools;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	internal abstract class TicketWager : Objeto
	{
		private readonly Ticket ticket;
		private readonly decimal amountToRisk;
		private readonly decimal amountToWin;
		private int wagerNumber;
		private int betNumber;
		private int consecutive;
		private string subticketAsString;
		private readonly SubTicket<IPick>[] subtickets;

		internal Ticket Ticket
		{
			get
			{
				return ticket;
			}
		}

		internal DateTime TicketCreationDate => ticket.CreationDate;

		internal DateTime DrawDate => ticket.DrawDate;

		internal int TicketNumber => ticket.TicketNumber;

		internal string AccountNumber => ticket.Player.AccountNumber;

		internal GameboardStatus GameboardStatus => ticket.Grading;

		internal decimal ExpectedProfit
		{
			get
			{
				var result = ticket.WasPurchasedForFree ? 0m : amountToRisk;
				return result;
			}
		}

		internal decimal Risk
		{
			get
			{
				return amountToRisk;
			}
		}

		internal decimal ToWin
		{
			get
			{
				return amountToWin;
			}
		}

		internal string RiskAsString
		{
			get
			{
				var strRisk = amountToRisk.ToString("N2", Integration.CultureInfoEnUS);
				return strRisk;
			}
		}

		internal string ToWinAsString
		{
			get
			{
				var strToWin = amountToWin.ToString("N2", Integration.CultureInfoEnUS);
				return strToWin;
			}
		}

		internal string BetDescription
		{
			get
			{
				return GenerateDescription();
			}
		}

		internal int WagerNumber
		{
			get
			{
				return wagerNumber;
			}
			set
			{
				wagerNumber = value;
			}
		}

		internal string ReferenceNumber
		{
			get
			{
				return $"{betNumber}-{consecutive}";
			}
		}

		internal string SubticketAsString
		{
			get
			{
				return subticketAsString;
			}
			set
			{
				subticketAsString = value;
			}
		}

		internal int BetNumber
		{
			get
			{
				return betNumber;
			}
			set
			{
				betNumber = value;
			}
		}

		internal int Consecutive
		{
			get
			{
				return consecutive;
			}
			set
			{
				consecutive = value;
			}
		}

		internal decimal AdjustedWinAmount
		{
			get
			{
				var result = CalculateAdjustedWinAmount();
				return result;
			}
		}

		internal decimal AdjustedLossAmount
		{
			get
			{
				var result = CalculateAdjustedLossAmount();
				return result;
			}
		}

		internal IEnumerable<SubTicket<IPick>> Subtickets
		{
			get
			{
				return subtickets;
			}
		}

        public TicketWager(Ticket ticket, decimal amountToRisk, decimal amountToWin, int consecutive, string subticketAsString, SubTicket<IPick>[] subtickets)
		{
			this.ticket = ticket;
			this.amountToRisk = amountToRisk;
			this.amountToWin = amountToWin;
			this.betNumber = ticket.BetNumber();
			this.consecutive = consecutive;
			this.subticketAsString = subticketAsString;
			this.subtickets = subtickets;
		}

		internal string FullWagerNumber()
		{
			if (ticket.Order.IsMultipleAuthorization) return ticket.TicketNumber.ToString();

			var sb = new StringBuilder();
			sb.Append(ticket.TicketNumber);
			sb.Append('-');
			sb.Append(wagerNumber);
			return sb.ToString();
		}

		internal void RemoveItselfFromTicket()
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (ticket.CountWagers == 1)
			{
				ticket.ChangeToRefunded();
			}				
			else
			{
				ticket.RemoveWager(this);
			}			
		}

		protected bool IsWinningMoreThanRisk()
		{
			var result = AdjustedWinAmount >= AdjustedLossAmount;
			return result;
		}

		protected string PickNameForDescription()
		{
			if (ticket is TicketPick3Boxed || ticket is TicketPick3Straight)
			{
				return "Pick 3";
			}
			else if (ticket is TicketPick4Boxed || ticket is TicketPick4Straight)
			{
				return "Pick 4";
			}
			else if (ticket is TicketPick2Boxed || ticket is TicketPick2Straight)
			{
				return "Pick 2";
			}
			else if (ticket is TicketPick5Boxed || ticket is TicketPick5Straight)
			{
				return "Pick 5";
			}
			else if (ticket is TicketPowerBallSingle || ticket is TicketPowerBallPowerPlay)
			{
				return "Powerball";
			}
			else
			{
				throw new GameEngineException("This ticket does not have its own wager implementation");
			}
		}

		internal string StatusAsLetter()
		{
			if (ticket.IsPending()) return "P";
			if (IsWinner()) return "W";
			return "L";
		}

		internal abstract bool IsWinner();
		internal bool HasThisSequenceNumber(string sequenceNumber)//TODO cris revisar con alvaro y kenneth.
		{
			var winnerNumber = sequenceNumber;
			int digit1 = winnerNumber[0] - '0';
			int digit2 = winnerNumber[1] - '0';
			int? digit3 = winnerNumber.Length > 2 ? (int?)(winnerNumber[2] - '0') : null;
			int? digit4 = winnerNumber.Length > 3 ? (int?)(winnerNumber[3] - '0') : null;
			int? digit5 = winnerNumber.Length > 4 ? (int?)(winnerNumber[4] - '0') : null;

			bool isWinnerWager = false;
			if (Ticket is TicketPick3Straight)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value));
			}
			else if (Ticket is TicketPick4Straight)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value));
			}
			else if (Ticket is TicketPick2Straight)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2));
			}
			else if (Ticket is TicketPick5Straight)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value));
			}
			else if (Ticket is TicketPick3Boxed)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value));
			}
			else if (Ticket is TicketPick4Boxed)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value));
			}
			else if (Ticket is TicketPick2Boxed)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2));
			}
			else if (Ticket is TicketPick5Boxed)
			{
				isWinnerWager = Subtickets.Where(x => x is SubTicket<IPick>).Any(x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value));
			}
			else
			{
				throw new GameEngineException($"Type {Ticket.GetType().Name} its no valid.");
			}
			return isWinnerWager;
		}

		protected abstract decimal CalculateAdjustedWinAmount();

		protected abstract decimal CalculateAdjustedLossAmount();

		protected abstract string GenerateDescription();

		internal FragmentPaymentMessage FragmentPaymentMessage(DateTime now)
		{
			if (ticket.Prizing == GameboardStatus.LOSER)
			{
				return GenerateLoserFragmentMessage(now);
			}
			else if (ticket.Prizing == GameboardStatus.WINNER)
			{
				var isWinner = IsWinner();
				return isWinner ? GenerateWinnerFragmentMessage(now) : GenerateLoserFragmentMessage(now);
			}
			else if (ticket.IsRegraded())
			{
				return GenerateLoserFragmentMessage(now);
			}
			else if (ticket.IsNoAction())
			{
				return GenerateNoActionFragmentMessage(now);
			}
			else
			{
				throw new GameEngineException("Unhandled ticket message");
			}
		}

		private FragmentPaymentMessage GenerateWinnerFragmentMessage(DateTime now)
		{
			return new FragmentPaymentMessage(
				Ticket.Order.Coin,
				Ticket.AccountNumber,
				Ticket.TicketNumber,
				WagerNumber,
				WagerStatus.W,
				now,
                AdjustedWinAmount,
				AdjustedLossAmount,
				(int)Ticket.Player.Agent,
				Ticket.DomainUrl
			);
		}

		private FragmentPaymentMessage GenerateLoserFragmentMessage(DateTime now)
		{
			return new FragmentPaymentMessage(
				Ticket.Order.Coin,
				Ticket.AccountNumber,
				Ticket.TicketNumber,
				WagerNumber,
				WagerStatus.L,
				now,
				AdjustedWinAmount,
				AdjustedLossAmount,
				(int)Ticket.Player.Agent,
                Ticket.DomainUrl
            );
        }

		private FragmentPaymentMessage GenerateNoActionFragmentMessage(DateTime now)
		{
			return new FragmentPaymentMessage(
				Ticket.Order.Coin,
				Ticket.AccountNumber,
				Ticket.TicketNumber,
				WagerNumber,
				WagerStatus.X,
				now,
				AdjustedWinAmount,
				AdjustedLossAmount,
				(int)Ticket.Player.Agent,
                Ticket.DomainUrl
            );
        }

		internal FragmentPaymentMessage GenerateDeletedFragmentMessage(DateTime now)
		{
			return new FragmentPaymentMessage(
				Ticket.Order.Coin,
				Ticket.AccountNumber,
				Ticket.TicketNumber,
				WagerNumber,
				WagerStatus.D,
				now,
				AdjustedWinAmount,
				AdjustedLossAmount,
				(int)Ticket.Player.Agent,
                Ticket.DomainUrl
            );
        }
	}

	internal sealed class WagerPickBoxed : TicketWager
	{
		public WagerPickBoxed(Ticket ticket, decimal amountToRisk, decimal amountToWin, int consecutive, string subticketAsString, SubTicket<IPick>[] subtickets) :
			base(ticket, amountToRisk, amountToWin, consecutive, subticketAsString, subtickets)
		{

		}

		internal override bool IsWinner()
		{
			if (!Ticket.IsWinner()) return false;

			var winnerNumber = Ticket.Draw.SequenceOfNumbers;
			var hasSequenceOfNumbers = Ticket.Draw.HasSequenceOfNumbers;

			if (!hasSequenceOfNumbers) return false;

			int digit1 = winnerNumber[0] - '0';
			int digit2 = winnerNumber[1] - '0';
			int? digit3 = winnerNumber.Length > 2 ? (int?)(winnerNumber[2] - '0') : null;
			int? digit4 = winnerNumber.Length > 3 ? (int?)(winnerNumber[3] - '0') : null;
			int? digit5 = winnerNumber.Length > 4 ? (int?)(winnerNumber[4] - '0') : null;

			bool isWinnerWager = false;
			if (Ticket is TicketPick3Boxed ticketPick3Boxed)
			{
				if (ticketPick3Boxed.BelongsToFireBallDraw)
				{
					int fireBallNumber = ticketPick3Boxed.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
                        Subtickets.Where(x => x is SubTicket<IPick>).Any(
                            x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value) ||
                            x.IsTheSameNumberWithDifferentOrder(fireBallNumber, digit2, digit3.Value) ||
                            x.IsTheSameNumberWithDifferentOrder(digit1, fireBallNumber, digit3.Value) ||
                            x.IsTheSameNumberWithDifferentOrder(digit1, digit2, fireBallNumber)
                        )
                    );
                }
				else
				{
					isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value)
						)
					);
				}
			}
			else if (Ticket is TicketPick4Boxed ticketPick4Boxed)
			{
				if (ticketPick4Boxed.BelongsToFireBallDraw)
                {
                    int fireBallNumber = ticketPick4Boxed.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value) ||
							x.IsTheSameNumberWithDifferentOrder(fireBallNumber, digit2, digit3.Value, digit4.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, fireBallNumber, digit3.Value, digit4.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, digit2, fireBallNumber, digit4.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, fireBallNumber)
						)
					);
                }
                else
                {
                    isWinnerWager = IsWinningMoreThanRisk() && (
                        Subtickets.Where(x => x is SubTicket<IPick>).Any(
                            x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value)
                        )
                    );
                }
            }
			else if (Ticket is TicketPick2Boxed ticketPick2Boxed)
			{
				if (ticketPick2Boxed.BelongsToFireBallDraw)
				{
                    int fireBallNumber = ticketPick2Boxed.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2) ||
							x.IsTheSameNumberWithDifferentOrder(fireBallNumber, digit2) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, fireBallNumber)
						)
					);
                }
                else
				{
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2)
						)
					);
                }
			}
			else if (Ticket is TicketPick5Boxed ticketPick5Boxed)
			{
				if (ticketPick5Boxed.BelongsToFireBallDraw)
				{
                    int fireBallNumber = ticketPick5Boxed.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value) ||
							x.IsTheSameNumberWithDifferentOrder(fireBallNumber, digit2, digit3.Value, digit4.Value, digit5.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, fireBallNumber, digit3.Value, digit4.Value, digit5.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, digit2, fireBallNumber, digit4.Value, digit5.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, fireBallNumber, digit5.Value) ||
							x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value, fireBallNumber)
						)
					);
                }
                else
				{
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value)
						)
					);
                }
			}
			return isWinnerWager;
		}

		protected override decimal CalculateAdjustedWinAmount()
		{
			var result = Ticket.Lottery.PicksLotteryGame.StandardCoin == Ticket.Order.Coin ? Ticket.Payout - Risk : Ticket.Payout;
            return result;
		}

		protected override decimal CalculateAdjustedLossAmount()
		{
			if (ToWin >= 0) return 0;
			return -ToWin;
		}

		protected override string GenerateDescription()
		{
			var betAmount = Ticket.BetAmount();
			var strBetAmount = betAmount.ToString("N2");
			var schedule = Ticket.Lottery.FindScheduleAt(DrawDate);
			var strDrawDate = DrawDate.ToString("MM/dd/yyyy");
			var prize = ToWin + betAmount;
			var strPrize = prize.ToString("N2");
			var standardUnicode = Currency.Unicode(Ticket.Lottery.PicksLotteryGame.StandardCurrency);
			var fireballTag = Ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
			var betDescription = $"Lotto{fireballTag} {schedule.GetDescription()} {PickNameForDescription()} {strDrawDate} ({SubticketAsString}) BOX {Currency.Unicode(Ticket.Order.CurrencyCode)}{strBetAmount} ticket for {standardUnicode}{strPrize} prize";
			return betDescription;
		}
	}

	internal class WagerPickStraight : TicketWager
	{
		public WagerPickStraight(Ticket ticket, decimal amountToRisk, decimal amountToWin, int consecutive, string subticketAsString, SubTicket<IPick>[] subtickets) :
			base(ticket, amountToRisk, amountToWin, consecutive, subticketAsString, subtickets)
		{

		}

		internal override bool IsWinner()
		{
			if (!Ticket.IsWinner()) return false;

			var winnerNumber = Ticket.Draw.SequenceOfNumbers;
			var hasSequenceOfNumbers = Ticket.Draw.HasSequenceOfNumbers;

			if (!hasSequenceOfNumbers) return false;

			int digit1 = winnerNumber[0] - '0';
			int digit2 = winnerNumber[1] - '0';
			int? digit3 = winnerNumber.Length > 2 ? (int?)(winnerNumber[2] - '0') : null;
			int? digit4 = winnerNumber.Length > 3 ? (int?)(winnerNumber[3] - '0') : null;
			int? digit5 = winnerNumber.Length > 4 ? (int?)(winnerNumber[4] - '0') : null;

			bool isWinnerWager = false;
			if (Ticket is TicketPick3Straight pick3Ticket)
			{
				if (pick3Ticket.BelongsToFireBallDraw)
				{
                    int fireBallNumber = pick3Ticket.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
                        Subtickets.Where(x => x is SubTicket<IPick>).Any(
                            x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(fireBallNumber, digit2, digit3.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, fireBallNumber, digit3.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, fireBallNumber)
                        )
                    );
                }
				else
				{
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value)
						)
					);
				}
			}
			else if (Ticket is TicketPick4Straight pick4Ticket)
			{
				if (pick4Ticket.BelongsToFireBallDraw)
				{
                    int fireBallNumber = pick4Ticket.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
                        Subtickets.Where(x => x is SubTicket<IPick>).Any(
                            x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(fireBallNumber, digit2, digit3.Value, digit4.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, fireBallNumber, digit3.Value, digit4.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, fireBallNumber, digit4.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, fireBallNumber)
                        )
                    );
                }
				else
				{
					isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value)
						)
					);
				}
			}
			else if (Ticket is TicketPick2Straight pick2Ticket)
			{
                if (pick2Ticket.BelongsToFireBallDraw)
				{
                    int fireBallNumber = pick2Ticket.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2) ||
							x.IsTheSameNumberWithTheSameOrder(fireBallNumber, digit2) ||
							x.IsTheSameNumberWithTheSameOrder(digit1, fireBallNumber)
						)
					);
				}
				else
				{
					isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2)
						)
					);
				}
			}
			else if (Ticket is TicketPick5Straight pick5Ticket)
			{
				if (pick5Ticket.BelongsToFireBallDraw)
				{
					int fireBallNumber = pick5Ticket.Draw.FireBallNumber;
                    isWinnerWager = IsWinningMoreThanRisk() && (
                        Subtickets.Where(x => x is SubTicket<IPick>).Any(
                            x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(fireBallNumber, digit2, digit3.Value, digit4.Value, digit5.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, fireBallNumber, digit3.Value, digit4.Value, digit5.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, fireBallNumber, digit4.Value, digit5.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, fireBallNumber, digit5.Value) ||
                            x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value, fireBallNumber)
                        )
                    );
                }
				else
				{
					isWinnerWager = IsWinningMoreThanRisk() && (
						Subtickets.Where(x => x is SubTicket<IPick>).Any(
							x => x.IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3.Value, digit4.Value, digit5.Value)
						)
					);
				}
			}
			return isWinnerWager;
		}

		protected override decimal CalculateAdjustedWinAmount()
		{
            var result = Ticket.Lottery.PicksLotteryGame.StandardCoin == Ticket.Order.Coin ? Ticket.Payout - Risk : Ticket.Payout;
            return result;
		}

		protected override decimal CalculateAdjustedLossAmount()
		{
			if (Ticket.NumbersFollowPattern())
			{
				var prize = ToWin + Ticket.BetAmount();
				var result = prize - Ticket.TicketAmount();
				Commons.ValidateAmount(result);
				if (result >= 0) return 0;
				return -result;
			}
			else
			{
				if (ToWin >= 0) return 0;
				return -ToWin;
			}
		}

		protected override string GenerateDescription()
		{
			var betAmount = Ticket.BetAmount();
			var strAmount = Ticket.NumbersFollowPattern() ? Ticket.TicketAmount().ToString("N2") : betAmount.ToString("N2");
			var schedule = Ticket.Lottery.FindScheduleAt(DrawDate);
			var strDrawDate = DrawDate.ToString("MM/dd/yyyy");
			var prize = ToWin + betAmount;
			var strPrize = prize.ToString("N2");
            var standardUnicode = Currency.Unicode(Ticket.Lottery.PicksLotteryGame.StandardCurrency);
            var fireballTag = Ticket.BelongsToFireBallDraw ? " FB" : string.Empty;
            var betDescription = $"Lotto{fireballTag} {schedule.GetDescription()} {PickNameForDescription()} {strDrawDate} {SubticketAsString} {Currency.Unicode(Ticket.Order.CurrencyCode)}{strAmount} ticket for {standardUnicode}{strPrize} prize";
			return betDescription;
		}
	}

	internal class WagerPowerball : TicketWager
	{
		public WagerPowerball(Ticket ticket, decimal amountToRisk, decimal amountToWin, int consecutive, string subticketAsString, SubTicket<IPick>[] subtickets) :
			base(ticket, amountToRisk, amountToWin, consecutive, subticketAsString, subtickets)
		{

		}

		internal override bool IsWinner()
		{
			var result = Ticket.IsWinner();
			return result;
		}

		protected override decimal CalculateAdjustedWinAmount()
		{
            return Ticket.Payout - Risk;
		}

		protected override decimal CalculateAdjustedLossAmount()
		{
			var result = Ticket.Grade() * Ticket.BetAmount() - Risk;
			Commons.ValidateAmount(result);
			if (result >= 0) return 0;
			return -result;
		}

		protected override string GenerateDescription()
		{
			var betAmount = Ticket.IdOfType() == TicketType.PBP ? Ticket.BetAmount() * TicketPowerBallPowerPlay.FACTOR_OF_A_POWER_PLAY_TICKET : Ticket.BetAmount();
			var strBetAmount = betAmount.ToString("N2");
			var schedule = Ticket.Lottery.FindScheduleAt(DrawDate);
			var strDrawDate = DrawDate.ToString("MM/dd/yyyy");
			var prize = ToWin + betAmount;
			var strPrize = prize.ToString("N2");
            var standardUnicode = Currency.Unicode(Ticket.Lottery.PicksLotteryGame.StandardCurrency);
            var betDescription = $"Lotto PB {schedule.GetDescription()} Powerball {strDrawDate} ({SubticketAsString}) {Currency.Unicode(Ticket.Order.CurrencyCode)}{strBetAmount} ticket for {standardUnicode}{strPrize} prize";
			return betDescription;
		}
	}
	internal class WagerKeno : TicketWager
	{
		public WagerKeno(Ticket ticket, decimal amountToRisk, decimal amountToWin, int consecutive, string subticketAsString, SubTicket<IPick>[] subtickets) :
			base(ticket, amountToRisk, amountToWin, consecutive, subticketAsString, subtickets)
		{

		}

		internal override bool IsWinner()
		{
			var result = Ticket.IsWinner();
			return result;
		}

		protected override decimal CalculateAdjustedWinAmount()
		{
            return Ticket.Payout - Risk;
		}

		protected override decimal CalculateAdjustedLossAmount()
		{
			var result = Ticket.Grade() * Ticket.BetAmount() - Risk;
			Commons.ValidateAmount(result);
			if (result >= 0) return 0;
			return -result;
		}

		protected override string GenerateDescription()
		{
			var betAmount = Ticket.BetAmount();
			var strBetAmount = betAmount.ToString("N2");
			var schedule = Ticket.Lottery.FindScheduleAt(DrawDate);
			var strDrawDate = DrawDate.ToString("MM/dd/yyyy");
			var prize = ToWin + betAmount;
			var strPrize = prize.ToString("N2");
			var betDescription = $"Keno {schedule.GetDescription()} {strDrawDate} ({SubticketAsString}) ${strBetAmount} ticket for ${strPrize} prize";
			return betDescription;
		}
	}
	internal class WagerWithError : Objeto
	{
		private readonly TicketWager wager;
		internal TicketWager Wager
		{
			get
			{
				return wager;
			}
		}

		private readonly string errorMessage;

		internal string ErrorMessage
		{
			get
			{
				return errorMessage;
			}
		}

		public WagerWithError(TicketWager wager, string errorMessage)
		{
			this.wager = wager;
			this.errorMessage = errorMessage;
		}
	}

	internal class AccountingPendingWager : Objeto
	{
		private readonly int ticketNumber;
		internal int TicketNumber
		{
			get
			{
				return ticketNumber;
			}
		}
		private readonly int initialWagerNumber;
		internal int InitialWagerNumber
		{
			get
			{
				return initialWagerNumber;
			}
		}
		private readonly int finalWagerNumber;
		internal int FinalWagerNumber
		{
			get
			{
				return finalWagerNumber;
			}
		}
		private readonly int orderNumber;
		internal int OrderNumber
		{
			get
			{
				return orderNumber;
			}
		}
		private readonly DateTime creationDate;
		internal DateTime CreationDate
		{
			get
			{
				return creationDate;
			}
		}
		internal AccountingPendingWager(int ticketNumber, int initialWagerNumber, int finalWagerNumber, int orderNumber, DateTime creationDate)
		{
			this.ticketNumber = ticketNumber;
			this.initialWagerNumber = initialWagerNumber;
			this.finalWagerNumber = finalWagerNumber;
			this.orderNumber = orderNumber;
			this.creationDate = creationDate;
		}
	}
}
