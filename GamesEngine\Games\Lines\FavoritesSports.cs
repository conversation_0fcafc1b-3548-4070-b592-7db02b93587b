﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
	internal class FavoritesSports : Objeto
	{
		private List<Sport> sports;
		internal FavoritesSports()
		{

		}

		internal void Mark(Sport sport)
		{
			if (sport == null) throw new ArgumentNullException(nameof(sport));

			if (sports == null)
			{
				sports = new List<Sport>();
				sports.Add(sport);
			}
			else if (!sports.Exists(x => x == sport))
			{
				sports.Add(sport);
			}
		}

		internal void Unmark(Sport sport)
		{
			if (sport == null) throw new ArgumentNullException(nameof(sport));

			if (sports != null)
			{
				sports.Remove(sport);
			}	
		}

		internal IEnumerable<Sport> Favorites()
		{
			if (this.sports == null) throw new GameEngineException("There not any favorites.");

			return this.sports;
		}

		internal bool HasFavorites()
		{
			if (this.sports == null) return false;

			return this.sports.Count() != 0;
		}

	}
}
