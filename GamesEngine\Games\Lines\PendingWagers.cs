﻿using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class PendingWagers : Objeto
    {
        readonly List<Wager> matchPerPlayers = new List<Wager>();

        internal IEnumerable<Wager> GetAll => matchPerPlayers;

        internal void Add(Wager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));

            matchPerPlayers.Add(wager);
            accounts.Add(wager.AccountNumber);

            TotalWagers ++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin() : 0;
            TotalPrize += toWin;
            if (wager.IsWinner() || wager.IsLoser()) TotalProfit += wager.Risk - toWin;
        }

        internal void Add(IEnumerable<Wager> pendingMatches)
        {
            if (!pendingMatches.Any()) throw new GameEngineException($"{nameof(pendingMatches)} cannot be empty");

            foreach (var match in pendingMatches)
            {
                Add(match);
            }
        }

        HashSet<string> accounts = new HashSet<string>();

        internal IEnumerable<string> Accounts
        {
            get
            {
                return accounts;
            }
        }

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
