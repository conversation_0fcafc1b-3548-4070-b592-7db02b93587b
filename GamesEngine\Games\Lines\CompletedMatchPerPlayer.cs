﻿using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedMatchPerPlayer : Objeto
    {
        internal string AccountNumber { get; }
        internal int DomainId { get; }
        internal int GameId => game.GameId;
        internal int TournamentId => game.TournamentId;
        internal string TeamAName => game.TeamAName;
        internal string TeamBName => game.TeamBName;
        internal string SportName => game.SportName;
        internal string LeagueName => game.LeagueName;
        internal DateTime StartDate => game.StartDate;
        readonly List<CompletedWager> wagers = new List<CompletedWager>();
        readonly CompletedMatchesPerPlayers completedMatchesPerPlayers;
        readonly CompletedScoreGame game;

        public CompletedMatchPerPlayer(CompletedMatchesPerPlayers completedMatchesPerPlayers, CompletedScoreGame game, string accountNumber, int domainId)
        {
            if (completedMatchesPerPlayers == null) throw new ArgumentNullException(nameof(completedMatchesPerPlayers));
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater than zero");

            this.completedMatchesPerPlayers = completedMatchesPerPlayers;
            this.game = game;
            AccountNumber = accountNumber;
            DomainId = domainId;
        }

        internal void Add(IEnumerable<CompletedWager> paramWagers)
        {
            if (!paramWagers.Any()) throw new GameEngineException($"{nameof(paramWagers)} cannot be empty");

            foreach (var wager in paramWagers)
            {
                Add(wager);
            }
        }

        internal void Add(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (wager.Game != game || wager.AccountNumber != AccountNumber || wager.DomainId != DomainId) throw new GameEngineException($"{nameof(Wager)} belong to different {nameof(AccountNumber)} '{AccountNumber}' and {nameof(DomainId)} '{DomainId}'");

            wagers.Add(wager);
            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
            completedMatchesPerPlayers.UpdateTotals(wager);
        }

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
