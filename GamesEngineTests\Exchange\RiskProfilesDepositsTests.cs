﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Exchange
{
    [TestClass]
    public class RiskProfilesDepositsTests
    {
        private Company company;
        private Domain domain1;
        private Domain domain2;
        private RiskProfilesDeposits depositProfiles;

        [TestInitialize]
        public void TestInitialize()
        {
            company = new Company();
            domain1 = company.Sales.CreateDomain(false, 1, "localhost", Agents.INSIDER);
            domain2 = company.Sales.CreateDomain(false, 2, "test.cr", Agents.INSIDER);
            depositProfiles = new RiskProfilesDeposits(company.Sales.AllDomains);
        }

        [TestMethod]
        public void DefaultProfile_IsCreatedAndHasCorrectRanges()
        {
            // Arrange
            var defaultProfile = (RiskProfileDeposit)depositProfiles.DefaultRiskProfile;

            // Assert
            Assert.IsNotNull(defaultProfile);
            Assert.AreEqual(RiskProfiles.DefaultRiskProfileName, defaultProfile.Name);
            Assert.AreEqual(4, defaultProfile.ConfirmationRanges.Count(), "Should be 4 ranges now.");

            Assert.AreEqual(1, defaultProfile.ConfirmationsForAmount(99.99m));
            Assert.AreEqual(2, defaultProfile.ConfirmationsForAmount(100m));
            Assert.AreEqual(2, defaultProfile.ConfirmationsForAmount(999.99m));
            Assert.AreEqual(4, defaultProfile.ConfirmationsForAmount(1000m));
            Assert.AreEqual(4, defaultProfile.ConfirmationsForAmount(4999.99m));
            Assert.AreEqual(6, defaultProfile.ConfirmationsForAmount(5000m));
            Assert.AreEqual(6, defaultProfile.ConfirmationsForAmount(10000m));
        }

        [TestMethod]
        public void GetConfirmationsForAmount_WorksAsExpected_WithNewRanges()
        {
            // Arrange
            var profile = (RiskProfileDeposit)depositProfiles.DefaultRiskProfile;

            // Assert
            Assert.AreEqual(1, profile.ConfirmationsForAmount(0m), "Tier 1: Lower bound");
            Assert.AreEqual(1, profile.ConfirmationsForAmount(50m), "Tier 1: Mid-point");
            Assert.AreEqual(1, profile.ConfirmationsForAmount(99.99m), "Tier 1: Upper bound");
            Assert.AreEqual(2, profile.ConfirmationsForAmount(100m), "Tier 2: Lower bound");
            Assert.AreEqual(2, profile.ConfirmationsForAmount(500m), "Tier 2: Mid-point");
            Assert.AreEqual(4, profile.ConfirmationsForAmount(1000m), "Tier 3: Lower bound");
            Assert.AreEqual(4, profile.ConfirmationsForAmount(2500m), "Tier 3: Mid-point");
            Assert.AreEqual(6, profile.ConfirmationsForAmount(5000m), "Tier 4: Lower bound");
            Assert.AreEqual(6, profile.ConfirmationsForAmount(25000m), "Tier 4: High value");
        }

        [TestMethod]
        public void CreateCustomProfile_ClonesFromDefaultAndIsIndependent()
        {
            // Act
            var customProfile = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(domain1, "High Security");

            // Assert: Initial state is a clone of the new default
            Assert.IsNotNull(customProfile);
            Assert.AreEqual(4, customProfile.ConfirmationRanges.Count());
            Assert.AreEqual(6, customProfile.ConfirmationsForAmount(5000m));

            // Arrange: Modify the custom profile
            var newRanges = new List<DepositConfirmationRange>
            {
                new DepositConfirmationRange(0, 50, 2),
                new DepositConfirmationRange(50, 500, 4)
            };
            customProfile.SetConfirmationRanges(newRanges);

            // Assert: Custom profile is changed
            Assert.AreEqual(2, customProfile.ConfirmationRanges.Count());

            // Assert: Default profile is NOT changed
            var defaultProfile = (RiskProfileDeposit)depositProfiles.DefaultRiskProfile;
            Assert.AreEqual(4, defaultProfile.ConfirmationRanges.Count());
        }

        [TestMethod]
        [ExpectedException(typeof(GameEngineException), "Deposit confirmation ranges cannot overlap.")]
        public void SetConfirmationRanges_ThrowsOnOverlap()
        {
            // Arrange
            var customProfile = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(domain1, "Invalid Profile");
            var overlappingRanges = new List<DepositConfirmationRange>
            {
                new DepositConfirmationRange(0, 100, 1),
                new DepositConfirmationRange(50, 200, 2)
            };

            // Act
            customProfile.SetConfirmationRanges(overlappingRanges);
        }

        [TestMethod]
        public void HasProfile_And_ExistsProfileName_WorkCorrectly()
        {
            Assert.IsFalse(depositProfiles.HasProfile(domain1));
            Assert.IsFalse(depositProfiles.ExistsProfileName("Level 1"));
            Assert.IsTrue(depositProfiles.ExistsProfileName("Preset Risk Profile"));
            depositProfiles.CreateRiskProfile(domain1, "Level 1");
            Assert.IsTrue(depositProfiles.HasProfile(domain1));
            Assert.IsTrue(depositProfiles.ExistsProfileName("Level 1"));
        }

        [TestMethod]
        public void NameProperty_CanBeChangedAndEnforcesUniqueness()
        {
            // Arrange
            var profile1 = depositProfiles.CreateRiskProfile(domain1, "Original Name");
            depositProfiles.CreateRiskProfile(domain2, "Existing Name");

            // Act
            profile1.Name = "New Unique Name";

            // Assert
            Assert.AreEqual("New Unique Name", profile1.Name);
            Assert.IsNotNull(depositProfiles.GetRiskProfile("New Unique Name"));
        }

        [TestMethod]
        [ExpectedException(typeof(GameEngineException), "Risk profile Existing Name already exists")]
        public void NameProperty_ThrowsOnDuplicateName()
        {
            // Arrange
            var profile1 = depositProfiles.CreateRiskProfile(domain1, "Original Name");
            depositProfiles.CreateRiskProfile(domain2, "Existing Name");

            // Act
            profile1.Name = "Existing Name";
        }

        [TestMethod]
        public void AssignDomain_MovesDomainBetweenProfiles()
        {
            var profile1 = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(domain1, "Profile 1");
            var profile2 = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(domain2, "Profile 2");
            depositProfiles.AssignDomain(domain1, profile2);
            Assert.IsFalse(profile1.ContainsDomain(domain1));
            Assert.IsTrue(profile2.ContainsDomain(domain1));
            Assert.AreEqual(profile2, depositProfiles.GetRiskProfile(domain1));
            Assert.AreEqual(0, profile1.Domains.Count());
            Assert.AreEqual(2, profile2.Domains.Count());
        }

        [TestMethod]
        public void CreateRiskProfile_FromClone_IsIndependent()
        {
            // Arrange
            var originalProfile = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(domain1, "Original Profile");
            var customRanges = new List<DepositConfirmationRange> { new DepositConfirmationRange(0, 1000, 5) };
            originalProfile.SetConfirmationRanges(customRanges);
            Assert.AreEqual(5, originalProfile.ConfirmationsForAmount(500m));
            Assert.AreEqual(1, originalProfile.Domains.Count());

            depositProfiles.AssignDomain(domain2, originalProfile);
            Assert.AreEqual(2, originalProfile.Domains.Count(), "Original profile should now manage two domains before the clone.");
            Assert.IsTrue(originalProfile.ContainsDomain(domain2));

            // Act: Clone the settings from 'originalProfile' and assign 'domain2' to the new profile.
            var clonedProfile = (RiskProfileDeposit)depositProfiles.CreateRiskProfile(originalProfile, domain2, "Cloned Profile");

            // Assert:
            Assert.AreEqual("Cloned Profile", clonedProfile.Name);
            Assert.IsTrue(clonedProfile.ContainsDomain(domain2), "Cloned profile should contain the specified domain.");
            Assert.AreEqual(1, clonedProfile.ConfirmationRanges.Count());
            Assert.AreEqual(5, clonedProfile.ConfirmationsForAmount(500m));

            Assert.IsFalse(originalProfile.ContainsDomain(domain2), "Original profile should no longer contain the cloned domain.");
            Assert.AreEqual(1, originalProfile.Domains.Count(), "Original profile should now only have one domain left.");
            Assert.IsTrue(originalProfile.ContainsDomain(domain1));

            var moreRanges = new List<DepositConfirmationRange> { new DepositConfirmationRange(0, 100, 10) };
            clonedProfile.SetConfirmationRanges(moreRanges);
            Assert.AreEqual(10, clonedProfile.ConfirmationsForAmount(50m));
            Assert.AreEqual(5, originalProfile.ConfirmationsForAmount(50m), "Original profile's settings should be unchanged.");
        }
    }
}