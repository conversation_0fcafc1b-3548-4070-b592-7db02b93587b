﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    public struct Pattern
    {
        private Digit digits;

        public Pattern (int from0To9)
        {
            digits = Digit.NONE;
            digits = ToDigit(from0To9);
        }

        public Pattern(char aPattern)
        {
            digits = Digit.NONE;
            digits = ToDigit(aPattern);
        }

        public Pattern (string aPattern)
        {
            if (String.IsNullOrEmpty(aPattern)) throw new ArgumentNullException(nameof(aPattern));
            digits = Digit.NONE;
            if (aPattern.Length == 1)
            {
                digits = ToDigit(aPattern[0]);
            }
            else
            {
                for (int i = 0; i < aPattern.Length; i++)
                {
                    char c = aPattern[i];
                    Mark(c - '0');
                }
            }
        }

        private Digit ToDigit (int number)
        {
            switch (number)
            {
                case 0:
                    return Digit.ZERO;
                case 1:
                    return Digit.ONE;
                case 2:
                    return Digit.TWO;
                case 3:
                    return Digit.THREE;
                case 4:
                    return Digit.FOUR;
                case 5:
                    return Digit.FIVE;
                case 6:
                    return Digit.SIX;
                case 7:
                    return Digit.SEVEN;
                case 8:
                    return Digit.EIGHT;
                case 9:
                    return Digit.NINE;
                default:
                    throw new GameEngineException($"Value {number} is not a valid digit. You must use values from 0..9");
            }
        }

        private Digit ToDigit(char pattern)
        {
            switch (pattern)
            {
                case '0':
                    return Digit.ZERO;
                case '1':
                    return Digit.ONE;
                case '2':
                    return Digit.TWO;
                case '3':
                    return Digit.THREE;
                case '4':
                    return Digit.FOUR;
                case '5':
                    return Digit.FIVE;
                case '6':
                    return Digit.SIX;
                case '7':
                    return Digit.SEVEN;
                case '8':
                    return Digit.EIGHT;
                case '9':
                    return Digit.NINE;
                case '*':
                    return Digit.ALL;
                default:
                    throw new GameEngineException($"Value {pattern} is not a valid digit. You must use values from '0'..'9' or '*'");
            }
        }

        private void Mark(int number)
        {
            Digit d = ToDigit(number);
            digits = digits | d;
        }

        internal bool IsMarked(int number)
        {
            Digit d = ToDigit(number);
            bool result = (digits & d) != 0;
            return result;
        }

        internal int Count
        {
            get
            {

                if (
                    digits == Digit.ZERO ||
                    digits == Digit.ONE ||
                    digits == Digit.TWO ||
                    digits == Digit.THREE ||
                    digits == Digit.FOUR ||
                    digits == Digit.FIVE ||
                    digits == Digit.SIX ||
                    digits == Digit.SEVEN ||
                    digits == Digit.EIGHT ||
                    digits == Digit.NINE
                )
                { 
                        return 1;
                }
                int result = 0;
                for (int i = 0; i <= 9; i++)
                {
                    if (IsMarked(i)) result++;
                }
                return result;
            }
        }

        internal int SingleDigit
        {
            get
            {
                switch (digits)
                {
                    case Digit.ZERO:  return 0;
                    case Digit.ONE:   return 1;
                    case Digit.TWO:   return 2;
                    case Digit.THREE: return 3;
                    case Digit.FOUR:  return 4;
                    case Digit.FIVE:  return 5;
                    case Digit.SIX:   return 6;
                    case Digit.SEVEN: return 7;
                    case Digit.EIGHT: return 8;
                    case Digit.NINE:  return 9;
                }
                throw new GameEngineException($"{nameof(SingleDigit)} can not be used for '{ToString()}'. It only can be used for a pattern that represents a single digit");
            }
        }

        internal int[] Digits()
        {
            switch (digits)
            {
                case Digit.ZERO:  return new int[] { 0 };
                case Digit.ONE:   return new int[] { 1 };
                case Digit.TWO:   return new int[] { 2 };
                case Digit.THREE: return new int[] { 3 };
                case Digit.FOUR:  return new int[] { 4 };
                case Digit.FIVE:  return new int[] { 5 };
                case Digit.SIX:   return new int[] { 6 };
                case Digit.SEVEN: return new int[] { 7 };
                case Digit.EIGHT: return new int[] { 8 };
                case Digit.NINE:  return new int[] { 9 };
                case Digit.ALL:   return new int[] { 1,2,3,4,5,6,7,8,9,0 };
            }
            List<int> result = new List<int>();
            for (int i = 0; i <= 9; i++)
            {
                if (IsMarked(i)) result.Add(i);
            }
            return result.ToArray();
        }

        public override string ToString()
        {
            if (digits == Digit.ALL)
                return "*";
            else
            {
                int count = this.Count;
                StringBuilder result = new StringBuilder();
                bool many = count > 1;
                bool needsComma = false;
                if (many) result.Append('{');
                for (int i = 0; i <= 9 && count > 0; i++)
                {
                    if (IsMarked(i))
                    {
                        if (needsComma) result.Append(','); else needsComma = true;
                        result.Append((char)(i + '0'));
                        count--;
                    }
                }
                if (many) result.Append('}');
                return result.ToString();
            }
        }

        public override bool Equals(object obj)
        {
            Pattern other = (Pattern)obj;
            return digits == other.digits;
        }

        public string Numbers()
        {
            switch (digits)
            {
                case Digit.ZERO:
                    return "0";
                case Digit.ONE:
                    return "1";
                case Digit.TWO:
                    return "2";
                case Digit.THREE:
                    return "3";
                case Digit.FOUR:
                    return "4";
                case Digit.FIVE:
                    return "5";
                case Digit.SIX:
                    return "6";
                case Digit.SEVEN:
                    return "7";
                case Digit.EIGHT:
                    return "8";
                case Digit.NINE:
                    return "9";
                case Digit.ALL:
                    return "*";
                default:
                    StringBuilder result = new StringBuilder();
                    for (int i = 0; i <= 9; i++)
                    {
                        if (IsMarked(i))
                        {
                            result.Append((char)(i + '0'));
                        }
                    }
                    return result.ToString();
            }
        }

        public string AsStringForAccounting()
        {
            switch (digits)
            {
                case Digit.ZERO:
                    return "0";
                case Digit.ONE:
                    return "1";
                case Digit.TWO:
                    return "2";
                case Digit.THREE:
                    return "3";
                case Digit.FOUR:
                    return "4";
                case Digit.FIVE:
                    return "5";
                case Digit.SIX:
                    return "6";
                case Digit.SEVEN:
                    return "7";
                case Digit.EIGHT:
                    return "8";
                case Digit.NINE:
                    return "9";
                case Digit.ALL:
                    return "*";
                default:
                    StringBuilder result = new StringBuilder();
                    bool needsComma = false;
                    result.Append('(');
                    for (int i = 0; i <= 9; i++)
                    {
                        if (IsMarked(i))
                        {
                            if (needsComma) result.Append(','); else needsComma = true;
                            result.Append((char)(i + '0'));
                        }
                    }
                    result.Append(')');
                    return result.ToString();
            }
        }

        [Flags]
        enum Digit
        {
            ZERO  = 0b00000000001,
            ONE   = 0b00000000010,
            TWO   = 0b00000000100,
            THREE = 0b00000001000,
            FOUR  = 0b00000010000,
            FIVE  = 0b00000100000,
            SIX   = 0b00001000000,
            SEVEN = 0b00010000000,
            EIGHT = 0b00100000000,
            NINE  = 0b01000000000,
            ALL   = 0b01111111111,
            NONE  = 0b00000000000
        }
    }
}
