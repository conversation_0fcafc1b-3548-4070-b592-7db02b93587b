﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>ExternalServices.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Properties\DataSources\**" />
    <EmbeddedResource Remove="Properties\DataSources\**" />
    <None Remove="Properties\DataSources\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="app.config" />
    <None Remove="packages.config" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="log4net" Version="3.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="2.1.1" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="16.4.43" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="4.4.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="4.4.*" />
  </ItemGroup>

  <ItemGroup>
    <WCFMetadata Include="Connected Services" />
  </ItemGroup>

  <ItemGroup>
    <None Update="ExternalServices.snk">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Connected Services\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Connectors\Connectors.csproj" />
    <ProjectReference Include="..\Puppeteer\Puppeteer.csproj" />
  </ItemGroup>

</Project>