﻿using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    abstract class CompletedLine : Objeto
    {
        internal abstract LineType LineType { get; }

        internal abstract string LineTypeAsString { get; }

        internal int LineId { get; }

        internal string Text { get; }

        internal CompletedScoreGame Game { get; }

        List<CompletedWager> wagers;

        public CompletedLine(CompletedScoreGame game, int lineId, string text)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

            Game = game;
            LineId = lineId;
            Text = text;
        }

        internal bool IsSpreadLine()
        {
            return this is CompletedSpreadLine;
        }

        internal bool IsMoneyLine()
        {
            return this is CompletedMoneyLine;
        }

        internal bool IsMoneyDrawLine()
        {
            return this is CompletedMoneyDrawLine;
        }

        internal bool IsTotalPointsLine()
        {
            return this is CompletedTotalPointsLine;
        }

        internal bool IsYesNoLine()
        {
            return this is CompletedYesNoLine;
        }

        internal bool IsOverUnderLine()
        {
            return this is CompletedOverUnderLine;
        }

        internal bool IsFixedLine()
        {
            return this is CompletedFixedLine;
        }

        internal void Add(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (wagers == null) wagers = new List<CompletedWager>();

            wagers.Add(wager);

            if (accounts == null) accounts = new HashSet<string>();
            accounts.Add(wager.AccountNumber);

            TotalTicketAmount += wager.Risk;
            if (wager.IsWinner()) TotalPrize += wager.ToWin;
            Game.UpdateTotals(wager);
        }

        HashSet<string> accounts;

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers => wagers == null ? 0 : wagers.Count;

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit() 
        { 
            return TotalTicketAmount - TotalPrize; 
        }

    }

    abstract class CompletedABLine : CompletedLine
    {
        internal const char TEAM_A_ANSWER = 'A';
        internal const char TEAM_B_ANSWER = 'B';

        internal int TeamAReward { get; }

        internal int TeamBReward { get; }

        internal char RealAnswer { get; }

        public CompletedABLine(CompletedScoreGame game, int lineId, string text, int teamAReward, int teamBReward, char realAnswer) : base(game, lineId, text)
        {
            TeamAReward = teamAReward;
            TeamBReward = teamBReward;
            RealAnswer = realAnswer;
        }

        internal abstract int GetReward(char answer);
    }

    class CompletedSpreadLine : CompletedABLine
    {
        internal override LineType LineType => LineType.SPREAD_LINE;
        internal override string LineTypeAsString => LineType.SPREAD_LINE.ToString();

        internal decimal Spread { get; }

        public CompletedSpreadLine(CompletedScoreGame game, int lineId, int teamAReward, int teamBReward, char realAnswer, decimal spread) :base(game, lineId, SpreadLine.TEXT, teamAReward, teamBReward, realAnswer)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            Line.ValidateReward(teamAReward, teamBReward);
            if (realAnswer != TEAM_A_ANSWER && realAnswer != TEAM_B_ANSWER) throw new GameEngineException($"{nameof(realAnswer)} '{realAnswer}' is not valid");
            if (spread < 0) throw new GameEngineException($"{nameof(spread)} {spread} must be greater or equal than 0");

            Spread = spread;
        }

        internal override int GetReward(char answer)
        {
            switch (answer)
            {
                case TEAM_A_ANSWER:
                    return TeamAReward;
                case TEAM_B_ANSWER:
                    return TeamBReward;
                default:
                    throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
            }
        }
    }

    class CompletedMoneyLine : CompletedABLine
    {
        internal override LineType LineType => LineType.MONEY_LINE;
        internal override string LineTypeAsString => LineType.MONEY_LINE.ToString();

        public CompletedMoneyLine(CompletedScoreGame game, int lineId, int teamAReward, int teamBReward, char realAnswer) : base(game, lineId, MoneyLine.TEXT, teamAReward, teamBReward, realAnswer)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            Line.ValidateReward(teamAReward, teamBReward);
            if (realAnswer != TEAM_A_ANSWER && realAnswer != TEAM_B_ANSWER) throw new GameEngineException($"{nameof(realAnswer)} '{realAnswer}' is not valid");
        }

        internal override int GetReward(char answer)
        {
            switch (answer)
            {
                case TEAM_A_ANSWER:
                    return TeamAReward;
                case TEAM_B_ANSWER:
                    return TeamBReward;
                default:
                    throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
            }
        }
    }

    class CompletedMoneyDrawLine : CompletedABLine
    {
        internal const char DRAW_ANSWER = 'D';

        internal override LineType LineType => LineType.MONEYDRAW_LINE;
        internal override string LineTypeAsString => LineType.MONEYDRAW_LINE.ToString();

        internal int TieReward { get; }

        public CompletedMoneyDrawLine(CompletedScoreGame game, int lineId, int teamAReward, int teamBReward, char realAnswer, int tieReward) : base(game, lineId, MoneyDrawLine.TEXT, teamAReward, teamBReward, realAnswer)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            Line.ValidateReward(teamAReward, teamBReward);
            if (realAnswer != TEAM_A_ANSWER && realAnswer != TEAM_B_ANSWER && realAnswer != DRAW_ANSWER) throw new GameEngineException($"{nameof(realAnswer)} '{realAnswer}' is not valid");
            Line.ValidateReward(tieReward);

            TieReward = tieReward;
        }

        internal override int GetReward(char answer)
        {
            switch (answer)
            {
                case TEAM_A_ANSWER:
                    return TeamAReward;
                case TEAM_B_ANSWER:
                    return TeamBReward;
                case DRAW_ANSWER:
                    return TieReward;
                default:
                    throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
            }
        }
    }

    class CompletedYesNoLine : CompletedLine
    {
        internal const string YES_ANSWER = "YES";
        internal const string NO_ANSWER = "NO";

        internal override LineType LineType => LineType.YES_NO_LINE;
        internal override string LineTypeAsString => LineType.YES_NO_LINE.ToString();

        internal int YesReward { get; }

        internal int NoReward { get; }

        internal YesNoAnswer RealAnswer { get; }

        public CompletedYesNoLine(CompletedScoreGame game, int lineId, string text, int yesReward, int noReward, YesNoAnswer realAnswer) : base(game, lineId, text)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
            YesNoLine.ValidateReward(yesReward, noReward);
            if (realAnswer == null) throw new ArgumentNullException(nameof(realAnswer));

            YesReward = yesReward;
            NoReward = noReward;
            RealAnswer = realAnswer;
        }

        internal int GetReward(YesNoAnswer answer)
        {
            if (answer == YesNoAnswer.YES)
            {
                return YesReward;
            }
            else if (answer == YesNoAnswer.NO)
            {
                return NoReward;
            }
            else
            {
                throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
            }
        }
    }

    class CompletedOverUnderLine : CompletedLine
    {
        internal const string OVER_ANSWER = "OVER";
        internal const string UNDER_ANSWER = "UNDER";

        internal override LineType LineType => LineType.OVER_UNDER_LINE;
        internal override string LineTypeAsString => LineType.OVER_UNDER_LINE.ToString();

        internal decimal Score { get; set; }

        internal int OverReward { get; set; }

        internal int UnderReward { get; set; }

        internal OverUnderAnswer RealAnswer { get; set; }

        public CompletedOverUnderLine(CompletedScoreGame game, int lineId, string text, int overReward, int underReward, decimal score, OverUnderAnswer realAnswer) : base(game, lineId, text)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
            YesNoLine.ValidateReward(overReward, underReward);
            if (realAnswer == null) throw new ArgumentNullException(nameof(realAnswer));
            if (score < 0) throw new GameEngineException($"{nameof(score)} {score} must be greater or equal than 0");

            OverReward = overReward;
            UnderReward = underReward;
            Score = score;
            RealAnswer = realAnswer;
        }

        internal int GetReward(OverUnderAnswer answer)
        {
            if (answer == OverUnderAnswer.OVER)
            {
                return OverReward;
            }
            else if (answer == OverUnderAnswer.UNDER)
            {
                return UnderReward;
            }
            else
            {
                throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
            }
        }
    }

    class CompletedTotalPointsLine : CompletedOverUnderLine
    {
        internal override LineType LineType => LineType.TOTAL_POINTS_LINE;
        internal override string LineTypeAsString => LineType.TOTAL_POINTS_LINE.ToString();

        public CompletedTotalPointsLine(CompletedScoreGame game, int lineId, int overReward, int underReward, decimal score, OverUnderAnswer realAnswer) : base(game, lineId, TotalPointsLine.TEXT, overReward, underReward, score, realAnswer)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            YesNoLine.ValidateReward(overReward, underReward);
            if (realAnswer == null) throw new ArgumentNullException(nameof(realAnswer));
            if (score < 0) throw new GameEngineException($"{nameof(score)} {score} must be greater or equal than 0");

            OverReward = overReward;
            UnderReward = underReward;
            Score = score;
            RealAnswer = realAnswer;
        }
    }

    class CompletedFixedLine : CompletedLine
    {
        internal override LineType LineType => LineType.FIXED_LINE;
        internal override string LineTypeAsString => LineType.FIXED_LINE.ToString();

        internal List<string> Options { get; } = new List<string>();

        internal List<int> Rewards { get; } = new List<int>();

        internal string RealAnswer { get; }

        public CompletedFixedLine(CompletedScoreGame game, int lineId, string text, string options, string rewards, string realAnswer) : base(game, lineId, text)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (lineId < 0) throw new GameEngineException($"{nameof(lineId)} {lineId} must be greater or equal than 0");
            if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
            if (string.IsNullOrWhiteSpace(options)) throw new ArgumentNullException(nameof(options));
            if (string.IsNullOrWhiteSpace(rewards)) throw new ArgumentNullException(nameof(rewards));
            if (string.IsNullOrWhiteSpace(realAnswer)) throw new ArgumentNullException(nameof(realAnswer));

            var optionList = options.Split(',');
            var rewardList = rewards.Split(',');
            if (optionList.Length != rewardList.Count()) throw new GameEngineException("Each option must have exact one corresponding reward");
            if (optionList.Length < 2) throw new GameEngineException("Fixed line must provide two or more options");

            Options.AddRange(optionList);
            foreach (var reward in rewardList)
            {
                Rewards.Add(Convert.ToInt32(reward));
            }
            
            RealAnswer = realAnswer;
        }

        internal int GetReward(string answer)
        {
            if (string.IsNullOrWhiteSpace(answer)) throw new ArgumentNullException(nameof(answer));

            for (var index = 0; index < Options.Count; index++)
            {
                if (Options[index] == answer)
                {
                    return Rewards[index];
                }
            }

            throw new GameEngineException($"{nameof(answer)} '{answer}' is not valid");
        }
    }
}
