﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Customers;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.consignment
{
    public class Payout : ConsignmentProcessorDriver
    {
        public Payout() : base(TransactionType.Withdrawal)
        {
            
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            WithdrawalTransaction result;
            var authorizationNumber = AddPayout(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new WithdrawalTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new WithdrawalTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            WithdrawalTransaction result;
            var authorizationNumber = await AddPayoutAsync(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new WithdrawalTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new WithdrawalTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<int> AddPayoutAsync(CustomSettings.RecordSet recordSet)
        {
            ResponsePayoutEnvelope responsePayout = null;
            string responseString = string.Empty;
            string valuesWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Add_payout";
            try
            {
                var sendersBirthdate = await GetBirthdateAsync(recordSet);
                DateTime birthdate = DateTime.MinValue;
                if (!string.IsNullOrWhiteSpace(sendersBirthdate) && ! DateTime.TryParseExact(sendersBirthdate, "M/d/yyyy", new CultureInfo("en-US"), DateTimeStyles.None, out birthdate)) throw new Exception("Invalid birthdate format");
                string xmlString = CreateSoapEnvelope(recordSet, out valuesWithHiddenFields, birthdate);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentWithdraw.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentWithdraw.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponsePayoutEnvelope));
                responsePayout = (ResponsePayoutEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentWithdraw.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(AddPayoutAsync), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");
            }

            if (responsePayout == null || string.IsNullOrWhiteSpace(responsePayout.Body.Add_payoutResponse.Add_payoutResult))
            {
                NotifyWarn(nameof(AddPayoutAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isSuccessful = responsePayout.Body.Add_payoutResponse.Add_payoutResult.StartsWith('1');
                if (isSuccessful)
                {
                    var tempResult = responsePayout.Body.Add_payoutResponse.Add_payoutResult.Remove(0, "1,Payout Inserted,".Length);
                    var isNumeric = int.TryParse(tempResult, out int authorizationNumber);
                    if (isNumeric) return authorizationNumber;
                    else NotifyWarn(nameof(AddPayoutAsync), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
                }
                else NotifyWarn(nameof(AddPayoutAsync), $"Url:{url}\nResponse: {responseString}", "Response is not successful");
            }

            return FAKE_DOCUMENT_NUMBER;
        }

        int AddPayout(CustomSettings.RecordSet recordSet)
        {
            ResponsePayoutEnvelope responsePayout = null;
            string responseString = string.Empty;
            string valuesWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Add_payout";
            try
            {
                var sendersBirthdate = GetBirthdateAsync(recordSet).Result;
                DateTime birthdate = DateTime.MinValue;
                if (!string.IsNullOrWhiteSpace(sendersBirthdate) && !DateTime.TryParseExact(sendersBirthdate, "M/d/yyyy", new CultureInfo("en-US"), DateTimeStyles.None, out birthdate)) throw new Exception("Invalid birthdate format");
                string xmlString = CreateSoapEnvelope(recordSet, out valuesWithHiddenFields, birthdate);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentWithdraw.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                var response = httpClient.Send(webRequest);
                using var reader = new StreamReader(response.Content.ReadAsStream());
                responseString = reader.ReadToEnd();
                Loggers.GetIntance().AccountingConsignmentWithdraw.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponsePayoutEnvelope));
                responsePayout = (ResponsePayoutEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentWithdraw.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(AddPayout), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");
            }

            if (responsePayout == null || string.IsNullOrWhiteSpace(responsePayout.Body.Add_payoutResponse.Add_payoutResult))
            {
                NotifyWarn(nameof(AddPayout), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isSuccessful = responsePayout.Body.Add_payoutResponse.Add_payoutResult.StartsWith('1');
                if (isSuccessful)
                {
                    var tempResult = responsePayout.Body.Add_payoutResponse.Add_payoutResult.Remove(0, "1,Payout Inserted,".Length);
                    var isNumeric = int.TryParse(tempResult, out int authorizationNumber);
                    if (isNumeric) return authorizationNumber;
                    else NotifyWarn(nameof(AddPayout), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
                }
                else NotifyWarn(nameof(AddPayout), $"Url:{url}\nResponse: {responseString}", "Response is not successful");
            }

            return FAKE_DOCUMENT_NUMBER;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields, DateTime sendersBirthdate)
        {
            var customerId = recordSet.Mappings["customerId"].AsString;
            var receiversName = recordSet.Mappings["receiversName"].AsString;
            var country = recordSet.Mappings["country"].AsString;
            var state = recordSet.Mappings["state"].AsString;
            var city = recordSet.Mappings["city"].AsString;
            var amount = recordSet.Mappings["amount"].AsDecimal;
            var sendersAddress = recordSet.Mappings["sendersAddress"].AsString;
            var readyForProcessing = recordSet.Mappings["readyForProcessing"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Add_payout xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilder.Append("<receiversName>").Append(receiversName).AppendLine("</receiversName>");
            xmlBuilder.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilder.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilder.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilder.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilder.Append("<sendersAddress>").Append(sendersAddress).AppendLine("</sendersAddress>");
            xmlBuilder.Append("<readyForProcessing>").Append(readyForProcessing).AppendLine("</readyForProcessing>");
            if (!sendersBirthdate.Equals(DateTime.MinValue)) xmlBuilder.Append("<sendersBirthdate>").Append(sendersBirthdate.ToString("d/M/yyyy")).AppendLine("</sendersBirthdate>");
            xmlBuilder.AppendLine("</Add_payout>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Add_payout xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilderWithHiddenFields.Append("<receiversName>").Append(receiversName).AppendLine("</receiversName>");
            xmlBuilderWithHiddenFields.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilderWithHiddenFields.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilderWithHiddenFields.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilderWithHiddenFields.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilderWithHiddenFields.Append("<sendersAddress>").Append(sendersAddress).AppendLine("</sendersAddress>");
            xmlBuilderWithHiddenFields.Append("<readyForProcessing>").Append(readyForProcessing).AppendLine("</readyForProcessing>");
            if (!sendersBirthdate.Equals(DateTime.MinValue)) xmlBuilderWithHiddenFields.Append("<sendersBirthdate>").Append(sendersBirthdate).AppendLine("</sendersBirthdate>");
            xmlBuilderWithHiddenFields.AppendLine("</Add_payout>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponsePayoutEnvelope
        {
            public ResponsePayoutBody Body { get; set; }
        }

        public class ResponsePayoutBody
        {
            [XmlElement(ElementName = "Add_payoutResponse", Namespace = "http://tempuri.org/")]
            public AddPayoutResponse Add_payoutResponse { get; set; }
        }

        public class AddPayoutResponse
        {
            public string Add_payoutResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("identificationNumber");
            CustomSettings.AddVariableParameter("customerId");
            CustomSettings.AddVariableParameter("receiversName");
            CustomSettings.AddVariableParameter("country");
            CustomSettings.AddVariableParameter("state");
            CustomSettings.AddVariableParameter("city");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("sendersAddress");
            CustomSettings.AddVariableParameter("readyForProcessing");
            CustomSettings.AddVariableParameter("KYC$DOB");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
            KYCUsername = CustomSettings.Get(now, "KYCUsername").AsSecret.ToString();
            KYCPassword = CustomSettings.Get(now, "KYCPassword").AsSecret.ToString();

            if (!File.Exists("consignment_payout_settings.json")) throw new Exception("No setting file 'consignment_payout_settings.json' found.");
            string json = File.ReadAllText("consignment_payout_settings.json");
            Settings = JsonConvert.DeserializeObject<ConsigmentPayoutSettings>(json);
            getClient = new RestClient(Settings.KYCServicesBaseUrl);
        }

        struct ConsigmentPayoutSettings 
        {
            public string KYCServicesBaseUrl { get; set; }
        }
        const int MAX_RETRIES = 3;
        string KYCUsername { get; set; }
        string KYCPassword { get; set; }
        ConsigmentPayoutSettings Settings { get; set; }
        RestClient getClient;
        async Task<string> GetBirthdateAsync(CustomSettings.RecordSet recordSet)
        {
            var identificationNumber = recordSet.Mappings["identificationNumber"].AsString;
            string url = $"/customers/fields";
            string responseString = "";

            try
            {
                var request = new RestRequest(url, Method.Get);
                request.AddParameter("identificationNumber", identificationNumber);
                request.AddParameter("username", KYCUsername);
                request.AddParameter("password", KYCPassword);
                var fieldsToSend = new List<string>();
                fieldsToSend.Add(Fields.ID_BIRTHDATE);
                
                request.AddParameter("fields", string.Join(',',fieldsToSend));
                var response = await getClient.ExecuteAsync(request);
                responseString = response.Content;
                if ((int)response.StatusCode != 200)
                {
                    Loggers.GetIntance().AccountingConsignmentWithdraw.Debug($"{nameof(GetBirthdateAsync)}\nUrl:{url}\nResponse: {responseString}");
                }
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentWithdraw.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);
            }
            

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(GetBirthdateAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
                return string.Empty;
            }
            else
            {
                var birthdate = string.Empty;
                try
                {
                    var responseObj = JsonConvert.DeserializeObject<Dictionary<string,string>>(responseString);
                    if (responseObj.Count > 1) throw new Exception("Only one field value is expected");
                    birthdate = responseObj[Fields.ID_BIRTHDATE];
                }
                catch (Exception e)
                {
                    NotifyWarn(nameof(GetBirthdateAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                }
                return birthdate;
            }
        }
    }
}
