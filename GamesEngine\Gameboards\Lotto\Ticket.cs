﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Time;
using GamesEngine.Tools;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Gameboards.Lotto
{
	public abstract class Ticket : Gameboard
	{
		public enum Selection { BALLS = 0, MultipleInputSingleAmount = 1, SingleInputMultipleAmount = 2, NONE = 9 }

		internal const int FAKE_TICKET_NUMBER = -1;
		internal const int DEFAULT_UNASSIGNED_TICKET_NUMBER = 0;

		protected private LotteryComplete draw;
		protected decimal payout = 0;
		internal decimal Payout
		{
			get
			{
				return payout;
			}
		}

		private Selection selectionMode;
		private DateTime drawDate;
		private List<TicketWager> wagers = new List<TicketWager>();
		private int lastWagerConsecutive = 1;
		private int betNumber;
		private decimal ticketCost;

		private bool isMarkedAsSent;
		private readonly Lottery lottery;

		internal Lottery Lottery
		{
			get
			{
				return this.lottery;
			}
		}

		internal int DomainId => Order.Domain.Id;
		internal string DomainUrl => Order.Domain.Url;
        internal int AgentId => Order.Domain.AgentId;
        internal int AffiliateId => Player.Customer.AffiliateId;

		internal Ticket(Player player, Lottery lottery, DateTime drawDate, Selection selectionMode, DateTime creationDate, decimal ticketCost, Prizes prizes) :
			base(player, lottery.Company.Gameboards, creationDate, prizes)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (lottery == null) throw new ArgumentNullException(nameof(lottery));

			this.selectionMode = selectionMode;
			this.lottery = lottery;
			this.drawDate = drawDate;
			this.ticketCost = ticketCost;
		}

		internal bool BelongsToFireBallDraw
		{
			get
			{
				if (lottery is LotteryForPicks lotteryPicks) return lotteryPicks.IsLotteryPickWithFireBall;
				return false;
			}
		}

		protected Prizes prizes;

		internal abstract TicketType IdOfType();

		internal abstract void LotteryDraw(LotteryDraw lotteryDraw);

		internal void RegradeAsLoser(LotteryDraw lotteryDraw)
		{
			if (base.IsUnprized()) throw new GameEngineException($"Ticket should not be in {GameboardStatus.UNPRIZED} status");
			if (lotteryDraw == null) throw new ArgumentNullException(nameof(lotteryDraw));

			payout = 0;
			base.ChangeToRegraded();
			Draw = lotteryDraw;
		}

		internal void RevertPayout(decimal payout)
		{
			if (payout < 0) throw new GameEngineException($"{nameof(payout)} must be greater than zero");
			this.payout = payout;
		}

		internal LotteryDraw Draw
		{
			get
			{
				if (this.IsPending()) throw new GameEngineException($"Grading status is not {GameboardStatus.GRADED} or {GameboardStatus.REGRADED}");
				if (draw == null) throw new GameEngineException($"Ticket is in {GameboardStatus.GRADED} or {GameboardStatus.REGRADED} status therefore {nameof(draw)} should be known but it is null");
				return (LotteryDraw)draw;
			}
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(Draw));
				draw = value;
			}
		}

		internal LotteryNoAction NoActionDraw
		{
			set
			{
				if (value == null) throw new ArgumentNullException(nameof(Draw));
				draw = value;
			}
			get
			{
				if (draw == null) throw new GameEngineException($"Ticket should be known but it is null");
				return (LotteryNoAction)this.draw;
			}
		}

		internal int DrawVersion
		{
			get
			{
				if (draw == null) throw new ArgumentNullException(nameof(Draw));
				return draw.Version;
			}
		}

		internal void SetDrawAsNull()
		{
			if (draw == null) throw new GameEngineException($"{nameof(draw)} is already null");
			draw = null;
		}

		internal abstract int Count { get; }

		internal int CountWagers
		{
			get
			{
				return wagers.Count();
			}
		}

		internal bool AnyWagerNumberIsZero()
		{
			var result = wagers.Any(wager => wager.WagerNumber == 0);
			return result;
		}

		internal List<TicketWager> WagersWithNumberZero()
		{
			var result = wagers.Where(wager => wager.WagerNumber == 0);
			return result.ToList();
		}

		internal bool HasDrawDateBetween(DateTime startDrawDay, DateTime endDrawDay)
		{
			if (startDrawDay.Hour != 0 || startDrawDay.Minute != 0 || startDrawDay.Second != 0 || startDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(startDrawDay)} '{startDrawDay}' must be an exact day");
			if (endDrawDay.Hour != 0 || endDrawDay.Minute != 0 || endDrawDay.Second != 0 || endDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(endDrawDay)} '{endDrawDay}' must be an exact day");

			var result = drawDate >= startDrawDay && drawDate <= endDrawDay;
			return result;
		}

		private int countAccountingWagers;
		internal int CountAccountingWagers
		{
			get
			{
				return countAccountingWagers;
			}
			set
			{
				countAccountingWagers = value;
			}
		}

		internal abstract IEnumerable<SubTicket<IPick>> SubTickets();
		internal abstract IEnumerable<SubTicket<IPick>> Permute();

		protected bool IsValidSequenceOfNumbers(string sequenceOfNumbers)
		{
			foreach (char c in sequenceOfNumbers)
			{
				if (!Char.IsDigit(c)) return false;
			}
			return true;
		}

		public override BigInteger GradeToUntie()
		{
			throw new NotImplementedException();
		}

		internal abstract decimal CalculatedPrize();

		public override decimal Grade()
		{
			if (base.IsUnprized()) throw new GameEngineException($"Ticket still is {GameboardStatus.UNPRIZED} status");
			if (base.IsWinner() && payout == 0) throw new GameEngineException("Ticket is a winner so it is supposed that payout should not be zero");
			if (base.IsLoser() && payout != 0) throw new GameEngineException("Ticket is not winner so it is supposed that payout should be zero");
			if (!IsGraded() && !IsRegraded()) throw new GameEngineException($"Grading status is not {GameboardStatus.GRADED} or {GameboardStatus.REGRADED}");
			return payout;
		}

		internal int CountOfWinners()
		{
			if (IsPending() || IsRegraded()) throw new GameEngineException("Ticket has not been graded yet");

			if (!base.IsWinner()) return 0;

			int count = 0;
			if (((LotteryDraw)draw).HasSequenceOfNumbers)
			{
				if (
					(selectionMode == Selection.MultipleInputSingleAmount || selectionMode == Selection.SingleInputMultipleAmount) &&
					wagers.Count > 1 &&
					(this is TicketPick3Boxed || this is TicketPick4Boxed || this is TicketPick2Boxed || this is TicketPick5Boxed)
				)
				{
					return 1;
				}

				if (
					(selectionMode == Selection.MultipleInputSingleAmount || selectionMode == Selection.SingleInputMultipleAmount) &&
					wagers.Count == 1 &&
					(this is TicketPowerBallSingle || this is TicketPowerBallPowerPlay)
				)
				{
					return 1;
				}

				if (
					(selectionMode == Selection.MultipleInputSingleAmount || selectionMode == Selection.SingleInputMultipleAmount) &&
					//TODO Keno wagers.Count == 1 &&
					(this is TicketKeno)
				)
				{
					return 1;
				}
				foreach (var wager in wagers)
				{
					if (wager.IsWinner())
					{
						count++;
					}
				}
			}
			return count;
		}

		protected bool IsValid(string number)
		{
			if (number.Length > 10) return false;
			bool[] included = new bool[10];
			int count = 0;
			for (int i = 0; i < number.Length; i++)
			{
				char c = number[i];
				if (c == '*' && count == 0)
				{
					return true;
				}
				else if (Char.IsDigit(c) && !included[c - '0'])
				{
					included[c - '0'] = true;
					count++;
				}
				else
				{
					return false;
				}
			}
			return true;
		}

		internal Selection SelectionMode
		{
			get
			{
				return selectionMode;
			}
		}

		internal string SelectionModeAsString
		{
			get
			{
				return selectionMode.ToString();
			}
		}

		internal abstract string AsString();

		internal virtual decimal BetAmount()
		{
			var result = TicketAmount() / Count;
			Commons.ValidateAmount(result);
			return result;
		}

		internal string BetAmountFormatted()
		{
			var result = Currency.Factory(Order.CurrencyCode, BetAmount()).ToDisplayFormat();
			return result;
		}

		internal decimal TicketAmount()
		{
			return ticketCost;
		}

		internal bool WasPurchasedForFree
		{
			get
			{
				var result = Order.CurrencyCode == Currencies.CODES.LR.ToString() || Order.CurrencyCode == Currencies.CODES.FP.ToString() || Order.CurrencyCode == Currencies.CODES.KRWFP.ToString();
				return result;
			}
		}

		internal int BetNumber()
		{
			return betNumber;
		}
		internal void AssignBetNumber(int number)
		{
			if (betNumber != 0) throw new GameEngineException("Bet number is already assigned");
			if (number <= 0) throw new GameEngineException($"{nameof(number)} {number} is not valid");

			betNumber = number;
		}

		internal decimal Profit()
		{
			if (IsPending() || IsRegraded()) throw new GameEngineException("Ticket has not been graded yet");
			if (base.IsUnprized()) throw new GameEngineException($"Ticket still is {GameboardStatus.UNPRIZED} status");
			if (base.IsWinner() && payout == 0) throw new GameEngineException("Ticket is a winner so it is supposed that payout should not be zero");
			if (base.IsLoser() && payout != 0) throw new GameEngineException("Ticket is not winner so it is supposed that payout should be zero");

			if (WasPurchasedForFree) return -WonAmount();
			var amount = TicketAmount() - WonAmount();
			Commons.ValidateAmount(amount);
			return amount;
		}

		internal decimal WonAmount()
		{
			if (IsPending() || IsRegraded()) throw new GameEngineException("Ticket has not been graded yet");

			decimal amount = CalculatedPrize();
			Commons.ValidateAmount(amount);
			return amount;
		}

		internal string PostedDateAsString()
		{
			var postedDate = base.CreationDate.ToString("M/d/yyyy h:mm tt");
			return postedDate;
		}

		internal string GameType()
		{
			return this.GetType().Name;
		}

		internal abstract string GameTypeForReports();

		internal DateTime DrawDate
		{
			get
			{
				return drawDate;
			}
		}

		internal void ChangeTicketHour(DateTime newDate)
		{
			if (!(IsPending() || IsRegraded())) throw new GameEngineException("Draw date cannot be changed because it was already played");
			this.drawDate = newDate;
		}

		internal Location.State LotteryState()
		{
			return Lottery.State;
		}

		internal Schedule Schedule()
		{
			return Lottery.FindScheduleAt(drawDate);
		}
		internal int DrawingId => BelongsToFireBallDraw ? ((WeeklySchedule)Schedule()).FireballId : Schedule().Id;
		internal int UniqueDrawingId => BelongsToFireBallDraw ? ((WeeklySchedule)Schedule()).FireballUniqueId : Schedule().UniqueId;
		internal string DrawingDescription => BelongsToFireBallDraw ? ((WeeklySchedule)Schedule()).FireballDescription() : Schedule().GetDescription();

		internal abstract IEnumerable<TicketByPrize> TicketsByPrize();

		private AuthorizationNumber authorization;
        internal AuthorizationNumber Authorization
		{
			get
			{
				if (authorization.Equals(default(AuthorizationNumber))) throw new GameEngineException("Authorization number has not been set");
                if (authorization.Number == DEFAULT_UNASSIGNED_TICKET_NUMBER) throw new GameEngineException("Ticket number has not been set");
                return authorization;
            }
			set
			{
				if (!authorization.Equals(default(AuthorizationNumber))) throw new GameEngineException("Authorization number is already set");
                authorization = value;
            }
        }
        internal string AuthorizationNumber => Authorization.ToString();
        internal int TicketNumber
		{
			get
			{
				if (authorization.Number == DEFAULT_UNASSIGNED_TICKET_NUMBER) throw new GameEngineException("Ticket number has not been set");
				return authorization.Number;
			}
			set
			{
                authorization = PurchaseOrders.AuthorizationNumber.FromLegacyNumber(value);
			}
		}

		internal bool SelectionWasInputs
		{
			get
			{
				return selectionMode == Selection.MultipleInputSingleAmount || selectionMode == Selection.SingleInputMultipleAmount;
			}
		}

		internal double RemainingTimeInSecondsToDrawDate(DateTime now)
		{
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (drawDate < now)
			{
				const double IN_ANY_MOMENT_FROM_NOW = 0;
				return IN_ANY_MOMENT_FROM_NOW;
			}
			else
			{
				var totalSeconds = drawDate.Subtract(now).TotalSeconds;
				return totalSeconds;
			}
		}

		internal bool IsMarkedAsSent
		{
			get
			{
				return isMarkedAsSent;
			}
			set
			{
				isMarkedAsSent = value;
			}
		}

		internal IEnumerable<TicketWager> Wagers
		{
			get
			{
				return wagers;
			}
		}

		internal IEnumerable<TicketWager> WinnerWagers()
		{
			var winners = new List<TicketWager>();
			foreach (var wager in wagers)
			{
				if (wager.IsWinner())
				{
					winners.Add(wager);
				}
			}
			return winners;
		}

		internal void RemoveThisWager(TicketWager wager)
		{
			this.wagers.Remove(wager);
		}

        internal TicketWager FindWager(int subTicketNumber)
        {
			if (subTicketNumber <= 0) throw new GameEngineException("Subticket number must be greater than zero");
			var result = this.wagers.FirstOrDefault(x => x.WagerNumber == subTicketNumber);
			return result; 
        }

        internal void AddWager(decimal risk, decimal toWin, string subticketAsString, params SubTicket<IPick>[] subtickets)
		{
			Commons.ValidateAmount(risk);
			Commons.ValidateAmount(toWin);

			TicketWager wager = null;
			if (this is TicketPick3Boxed || this is TicketPick4Boxed || this is TicketPick2Boxed || this is TicketPick5Boxed)
			{
				wager = new WagerPickBoxed(this, risk, toWin, lastWagerConsecutive, subticketAsString, subtickets);
			}
			else if (this is TicketPick3Straight || this is TicketPick4Straight || this is TicketPick2Straight || this is TicketPick5Straight)
			{
				wager = new WagerPickStraight(this, risk, toWin, lastWagerConsecutive, subticketAsString, subtickets);
			}
			else if (this is TicketPowerBallSingle || this is TicketPowerBallPowerPlay)
			{
				wager = new WagerPowerball(this, risk, toWin, lastWagerConsecutive, subticketAsString, subtickets);
			}
			else if (this is TicketKenoSingle || this is TicketKenoMultiplier || this is TicketKenoBulleye || this is TicketKenoMultiplierAndBulleye)
			{
				wager = new WagerKeno(this, risk, toWin, lastWagerConsecutive, subticketAsString, subtickets);
			}
			else
			{
				throw new GameEngineException("This ticket does not have its own wager implementation");
			}

			wager.WagerNumber = lastWagerConsecutive;
			wagers.Add(wager);
			lastWagerConsecutive++;
		}

		internal abstract bool WasCreatedByPattern();

		internal abstract bool NumbersFollowPattern();

		internal abstract void RemoveWager(TicketWager wager);

		internal void UpdateWagerNumber(int wagerConsecutive, int wagerNumber)
		{
			var wager = wagers.Single(x => x.Consecutive == wagerConsecutive);
			wager.WagerNumber = wagerNumber;
		}

		internal abstract void GenerateWagers();
		internal abstract void GenerateWagers(decimal betAmount);

		internal string SubticketsAndWagerNumbersAsString()
		{
			var strSubticketsAndWagerNumbers = new StringBuilder();
			foreach (var wager in wagers)
			{
				strSubticketsAndWagerNumbers.Append($"({wager.SubticketAsString},{wager.WagerNumber})");
			}
			return strSubticketsAndWagerNumbers.ToString();
		}

		internal abstract bool IsPowerball();

		internal IEnumerable<TicketWager> FindWagersWithThisSequence(string sequenceOfNumbers)
		{
			var winnerNumber = sequenceOfNumbers;
			List<TicketWager> result = new List<TicketWager>();
			foreach (TicketWager wager in wagers)
			{
				if (wager.HasThisSequenceNumber(sequenceOfNumbers))
				{
					result.Add(wager);
				}
			}
			return result;
		}

		internal abstract string WinnerNumbers();


	}

	internal class TicketsByOrderAndPrize : Objeto
	{
		internal List<Ticket> Tickets { get; private set; } = new List<Ticket>();
		internal int OrderNumber { get; private set; }
		internal decimal Prize { get; private set; }
		internal State State => Tickets.First().LotteryState();
		internal DateTime DrawDate => Tickets.First().DrawDate;
		internal DateTime CreationDate => Tickets.First().CreationDate;
		internal Ticket FirstTicket => Tickets.First();
		internal int Count => Tickets.Count;

		internal TicketsByOrderAndPrize(Ticket ticket, decimal prize)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (prize <= 0) throw new GameEngineException("Prize must be greater than zero");

			OrderNumber = ticket.Order.Number;
			Prize = prize;
			Tickets.Add(ticket);
		}

		internal void Add(Ticket ticket, decimal prize)
		{
			if (ticket == null) throw new ArgumentNullException(nameof(ticket));
			if (prize != Prize) throw new GameEngineException("Tickets must have the same prize");
			if (ticket.Order.Number != OrderNumber) throw new GameEngineException("Tickets must have the same order number");
			if (ticket.LotteryState() != State) throw new GameEngineException("Tickets must have the same state");
			if (ticket.DrawDate != DrawDate) throw new GameEngineException("Tickets must have the same draw date");

			Tickets.Add(ticket);
		}

		internal decimal TicketAmount()
		{
			var amount = FirstTicket.BetAmount() * Count;
			return amount;
		}

		internal string TicketAmountFormatted()
		{
			var result = Currency.Factory(FirstTicket.Order.CurrencyCode, TicketAmount()).ToDisplayFormat();
			return result;
		}

		internal decimal AmountToWin()
		{
			var amount = FirstTicket.BetAmount() * Prize;
			return amount;
		}

		internal string AmountToWinFormatted()
		{
			var result = Currency.Factory(FirstTicket.Order.CurrencyCode, AmountToWin()).ToDisplayFormat();
			return result;
		}

		internal IEnumerable<TicketWager> Wagers()
		{
			var wagers = new List<TicketWager>();
			foreach (var ticket in Tickets)
			{
				wagers.AddRange(ticket.Wagers);
			}
			return wagers;
		}
	}

	internal abstract class TicketByPrize : Objeto
	{
		private readonly Ticket ticket;
		private readonly List<SubTicket<IPick>> subtickets = new List<SubTicket<IPick>>();
		private readonly decimal prize;

		internal string Id
		{
			get
			{
				return ticket.IdOfType().ToString();
			}
		}

		internal Ticket Ticket { get { return ticket; } }
		internal IEnumerable<SubTicket<IPick>> Subtickets { get { return subtickets; } }

		internal int Count
		{
			get
			{
				return subtickets.Count;
			}
		}

		internal decimal Prize
		{
			get
			{
				return this.prize;
			}
		}

		protected TicketByPrize(Ticket ticket, int prizeCriteria)
		{
			this.ticket = ticket;
			this.prize = ticket.Prizes.Prize(ticket.IdOfType(), prizeCriteria);
		}

		protected TicketByPrize()
		{
		}

		internal virtual decimal TicketAmount()
		{
			var amount = ticket.BetAmount() * Count;
			return amount;
		}

		internal string TicketAmountFormatted()
		{
			var result = Currency.Factory(ticket.Order.CurrencyCode, TicketAmount()).ToDisplayFormat();
			return result;
		}

		internal decimal AmountToWin()
		{
			var amount = ticket.BetAmount() * this.prize;
			return amount;
		}

		internal string AmountToWinFormatted()
		{
			var result = Currency.Factory(ticket.Order.CurrencyCode, AmountToWin()).ToDisplayFormat();
			return result;
		}

		internal decimal WonAmount()
		{
			var amount = IsWinner() ? ticket.BetAmount() * this.prize * ticket.CountOfWinners() : 0m;
			return amount;
		}

		internal void Add(SubTicket<IPick> subTicket)
		{
			subtickets.Add(subTicket);
		}

		internal abstract string AsString();

		internal abstract bool HasNumber(int[] numbers);

		internal bool IsWinner()
		{
			if (ticket.IsLoser()) return false;
			var hasSequenceOfNumbers = ticket.Draw.HasSequenceOfNumbers;
			var winnerNumber = hasSequenceOfNumbers ? Array.ConvertAll(ticket.Draw.SequenceOfNumbers.ToCharArray(), c => (int)Char.GetNumericValue(c)) : new int[] { };
			var containsWinnerNumber = hasSequenceOfNumbers && HasNumber(winnerNumber);
			return containsWinnerNumber;
		}

		internal virtual IEnumerable<TicketWager> Wagers()
		{
			var wagers = new List<TicketWager>();
			if (ticket.Wagers.Count() == 1)
			{
				wagers.Add(ticket.Wagers.ElementAt(0));
				return wagers;
			}

			foreach (var wager in ticket.Wagers)
			{
				var existSubticket = subtickets.Exists(x => x.AsStringForAccounting() == wager.SubticketAsString);
				if (existSubticket)
				{
					wagers.Add(wager);
				}
			}
			return wagers;
		}

		internal bool HasSubtickets()
		{
			var result = subtickets != null && subtickets.Count > 0;
			return result;
		}
	}

	internal class TicketByPrizeEmpty : TicketByPrize
	{
		internal TicketByPrizeEmpty() : base()
		{

		}

		internal override string AsString()
		{
			return "";
		}

		internal override bool HasNumber(int[] numbers)
		{
			return false;
		}

		internal override IEnumerable<TicketWager> Wagers()
		{
			return Enumerable.Empty<TicketWager>();
		}
	}

	internal class TicketsByPrizeWithTotalSubtickets : Objeto
	{
		private readonly IEnumerable<TicketByPrize> ticketsByPrize;
		internal IEnumerable<TicketByPrize> TicketsByPrize
		{
			get
			{
				if (this.ticketsByPrize == null) return Enumerable.Empty<TicketByPrize>();
				return ticketsByPrize;
			}
		}

		public TicketsByPrizeWithTotalSubtickets(IEnumerable<TicketByPrize> ticketsByPrize)
		{
			this.ticketsByPrize = ticketsByPrize.ToList();
		}

		internal int TotalSubtickets()
		{
			var count = this.ticketsByPrize == null ? 0 : ticketsByPrize.Sum(x => x.Count);
			return count;
		}
	}

}
