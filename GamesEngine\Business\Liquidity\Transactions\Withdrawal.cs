﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Transactions
{
    public class Withdrawal
    {
        public int Id { get; private set; }

        public decimal Amount { get; private set; }

        public DateTime WithdrawalDate { get; private set; }

        public Withdrawal(int id, decimal amount, DateTime withdrawalDate)
        {
            if (id <= 0) throw new ArgumentException("Id must be greater than zero.", nameof(id));
            Id = id;
            Amount = amount;
            WithdrawalDate = withdrawalDate;
        }
    }
}
