﻿using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    class PendingWagersBuilder
    {
        IEnumerable<Wager> wagers = Enumerable.Empty<Wager>();

        public PendingWagersBuilder(IEnumerable<Tournament> tournaments, DateTime startDate, DateTime endDate)
        {
            if (!tournaments.Any()) throw new GameEngineException($"{nameof(tournaments)} cannot be empty");
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");

            foreach (var tournament in tournaments)
            {
                ConcatWagers(tournament, startDate, endDate);
            }
        }

        public PendingWagersBuilder(Tournament tournament, DateTime startDate, DateTime endDate)
        {
            if (tournament == null) throw new ArgumentNullException(nameof(tournament));
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");

            ConcatWagers(tournament, startDate, endDate);
        }

        void ConcatWagers(Tournament tournament, DateTime startDate, DateTime endDate)
        {
            foreach (var game in tournament.Games())
            {
                var scheduledDate = game.ScheduledDate.Date;
                if (scheduledDate >= startDate && scheduledDate <= endDate)
                {
                    var betBoard = tournament.Tournaments.Company.Betboard(tournament);
                    var matchDay = betBoard.Matchday(scheduledDate);
                    var showcase = matchDay.GetShowcase(game);
                    if (showcase.IsThereAnyPendingLine())
                    {
                        var tempWagers = showcase.WagersOfLastVersionLines();
                        wagers = wagers.Concat(tempWagers);
                    }
                }
            }
        }

        internal PendingWagersBuilder FilterByGame(int gameId)
        {
            wagers = wagers.Where(wager => wager.Line.Game.Number == gameId);
            return this;
        }

        internal PendingWagersBuilder FilterBy(LineType lineType)
        {
            wagers = wagers.Where(wager => wager.Line.LineTypeAsString == lineType.ToString());
            return this;
        }

        internal PendingWagersBuilder FilterBy(GameboardStatus status)
        {
            wagers = wagers.Where(wager => wager.Grading == status);
            return this;
        }

        internal PendingWagersBuilder FilterBy(string accountNumber)
        {
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            wagers = wagers.Where(wager => wager.AccountNumber == accountNumber);
            return this;
        }

        internal PendingWagersBuilder FilterBy(int authorizationId)
        {
            if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} must be greater than zero");

            wagers = wagers.Where(wager => wager.WasPurchased && wager.Order.AuthorizationId == authorizationId);
            return this;
        }

        internal PendingWagersBuilder FilterBy(IEnumerable<string> domainIds)
        {
            if (!domainIds.Any()) throw new GameEngineException($"{nameof(domainIds)} cannot be empty");

            wagers = wagers.Where(x => x.WasPurchased && domainIds.Contains(x.Order.Domain.Id.ToString()));
            return this;
        }

        internal PendingWagers Build()
        {
            var result = new PendingWagers();
            if (wagers.Any()) result.Add(wagers);
            return result;
        }
    }
}
