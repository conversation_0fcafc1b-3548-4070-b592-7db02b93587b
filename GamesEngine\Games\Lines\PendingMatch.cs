﻿using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class PendingMatch:Objeto
    {
        internal Game Game { get; }
        internal Domain Domain { get; }
        internal int GameId => Game.Number;
        internal int TournamentId => Game.Tournament.Id;
        internal string TeamAName => Game.TeamA.Name;
        internal string TeamBName => Game.TeamB.Name;
        internal string SportName => Game.Sport.Name;
        internal string LeagueName => Game.Tournament.League.Name;
        internal DateTime StartDate => Game.ScheduledDate;
        internal int DomainId => Domain.Id;
        internal string DomainUrl => Domain.Url;
        Dictionary<int, Wager> wagers = new Dictionary<int, Wager>();
        PendingMatches pendingMatches;

        public PendingMatch(PendingMatches pendingMatches, Game game, Domain domain)
        {
            if (pendingMatches == null) throw new ArgumentNullException(nameof(pendingMatches));
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            this.pendingMatches = pendingMatches;
            Game = game;
            Domain = domain;
        }

        internal void Add(IEnumerable<Wager> paramWagers)
        {
            if (paramWagers.Any(wager => wager.Line.Game != Game && wager.Order.Domain != Domain)) throw new GameEngineException($"Any {nameof(Wager)} belong to different {nameof(Game)} '{Game.Number}' and {nameof(Domain)} '{Domain.Url}'");
            
            foreach (var wager in paramWagers)
            {
                Add(wager);
            }
        }

        internal void Add(Wager wager)
        {
            if (!this.wagers.ContainsKey(wager.Id)) this.wagers.Add(wager.Id, wager);
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);

            TotalWagers ++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin() : 0;
            TotalPrize += toWin;
            if (wager.IsWinner() || wager.IsLoser()) TotalProfit += wager.Risk - toWin;
            pendingMatches.UpdateTotals(wager);
        }

        HashSet<string> accounts;

        internal IEnumerable<string> Accounts
        {
            get
            {
                return accounts;
            }
        }

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
