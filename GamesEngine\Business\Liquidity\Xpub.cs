﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity
{
    [Puppet]
    public class Xpub:Objeto
    {
        public string Value { get; }

        public Xpub(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("Invalid XPUB value.", nameof(value));

            Value = value;
        }

        public string GenerateAddress()
        {
            // Aquí se haría la derivación real de direcciones a partir del XPUB
            // Por ahora es un stub:
            return $"addr_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
        }
    }

}
