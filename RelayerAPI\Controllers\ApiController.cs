﻿using GamesEngine;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace RelayerAPI.Controllers
{
    [ApiController]
    public class ApiController : Controller
    {
        private readonly ILogger<ApiController> _logger;

        public ApiController(ILogger<ApiController> logger)
        {
            _logger = logger;
        }

        [HttpGet("api/proxyendpoints")]
        public async Task<IActionResult> ProxyEndpointsAsync()
        {
            var result = await RelayerAPI.Relayer.PerformQryAsync(HttpContext,
             $@"
            {{
                
                for( proxyEndpoints : company.System.ServiceMonitor.ProxyAccessEndpoints)
                {{
                    proxyEndpoint = proxyEndpoints;
                    print proxyEndpoint.Name name;
                    for( subscribers : proxyEndpoint.Subscribers)
                    {{
                        subscriber = subscribers;
                        print subscriber.Id id;
                        print subscriber.Organization organization;
                        print subscriber.Secret secret;
                        print subscriber.Description description;
                        print subscriber.Enabled enabled;
                        lastEntries = subscriber.Log.Entries();
                        for (logs:lastEntries)
                        {{
                          log = logs;
	                        print log.DateFormattedAsText date;
	                        print log.Who who;
	                        print log.Message message;
                        }}
                    }}
                }}
                
            }}");

            return result;

        }


        [HttpGet("api/{endpointName}/consumers")]
        public async Task<IActionResult> ConsumersAsync(string endpointName)
        {
            if (string.IsNullOrEmpty(endpointName)) return BadRequest($"EndpointName is required");

            var result = await RelayerAPI.Relayer.PerformChkThenQryAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endpointName}');
                Check(existEndpointName) Error 'The endpoint:{endpointName} does not exist.';
            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endpointName}');
                for( subscribers : proxyEndpoint.Subscribers)
                {{
                    subscriber = subscribers;
                    print subscriber.Organization organization;
                    print subscriber.Secret secret;
                    print subscriber.Description description;
                }}
                
            }}");

            return result;

        }

        [HttpPost("api/{endpointName}/consumers")]
        public async Task<IActionResult> AddConsumerAsync(string endpointName, [FromBody] EndpointSubscriber subscriber)
        {
            if (string.IsNullOrEmpty(endpointName)) return BadRequest($"EndpointName is required");
            if (subscriber == null) return BadRequest("Subscriber data is required");
            if (string.IsNullOrEmpty(subscriber.Organization)) return BadRequest("Organization is required");
            if (string.IsNullOrEmpty(subscriber.Description)) return BadRequest("Description is required");

            var who = "Juan";//Security.UserName(HttpContext); //Rubicon: Todo agregar security
            var scapedOrganization = Validator.StringEscape(subscriber.Organization);
            var scapedDescription = Validator.StringEscape(subscriber.Description);

            var result = await RelayerAPI.Relayer.PerformChkThenCmdAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endpointName}');
                Check(existEndpointName) Error 'The endpoint:{endpointName} does not exist.';
                proxyEndpoint = company.System.ServiceMonitor.Find('{endpointName}');
            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endpointName}');
                Eval('subscriberId = '+proxyEndpoint.NextSubscriberId()+';');
                Eval('secret = \''+proxyEndpoint.GenerateSecret()+'\';');
                subscriber = proxyEndpoint.Add(itIsThePresent, subscriberId, Now, '{scapedOrganization}', secret, '{scapedDescription}', '{who}');
                print subscriber.Id id;
            }}");

            return result;
        }

        [HttpPut("api/{endointName}/consumers/{subscriberId}/refreshsecret")]
        public async Task<IActionResult> RegenerateSecretAsync(string endointName, int subscriberId)
        {
            if (string.IsNullOrEmpty(endointName)) return BadRequest($"EndpointName is required");
            if (subscriberId <= 0) return BadRequest($"SubscriberId is required and must be greater than 0");

            var who = "Juan";//Security.UserName(HttpContext); //Rubicon: Todo agregar security

            var result = await RelayerAPI.Relayer.PerformChkThenCmdAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endointName}');
                Check(existEndpointName) Error 'The endpoint:{endointName} does not exist.';

                if (existEndpointName)
                {{
                    proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                    existSubscriber = proxyEndpoint.ExistSubscriber({subscriberId});
                    Check(existSubscriber) Error 'The subscriber:{subscriberId} does not exist.';
                }}

            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                subscriber = proxyEndpoint.FindSubscriber({subscriberId});
                Eval('secret = \''+proxyEndpoint.GenerateSecret()+'\';');
                subscriber.UpdateSecret(itIsThePresent, now, '{who}', secret);
            }}");

            return result;
        }

        [HttpPut("api/{endointName}/consumers/{subscriberId}/enabled")]
        public async Task<IActionResult> EnabledConsumerAsync(string endointName, int subscriberId, [FromBody] EnableConsumerRequest body)
        {
            if (string.IsNullOrEmpty(endointName)) return BadRequest($"EndpointName is required");
            if (subscriberId <= 0) return BadRequest($"SubscriberId is required and must be greater than 0");
            if (body == null) return BadRequest("Request data is required");
            if (string.IsNullOrEmpty(body.Reason)) return BadRequest("Reason is required");

            var who = "Juan";
            var scapedReason = Validator.StringEscape(body.Reason);

            var result = await RelayerAPI.Relayer.PerformChkThenCmdAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endointName}');
                Check(existEndpointName) Error 'The endpoint:{endointName} does not exist.';

                if (existEndpointName)
                {{
                    proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                    existSubscriber = proxyEndpoint.ExistSubscriber({subscriberId});
                    Check(existSubscriber) Error 'The subscriber:{subscriberId} does not exist.';
                }}

            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                subscriber = proxyEndpoint.FindSubscriber({subscriberId});
                subscriber.Enable(itIsThePresent, now, '{who}', '{scapedReason}');
            }}");

            return result;
        }

        [HttpPut("api/{endointName}/consumers/{subscriberId}/disabled")]
        public async Task<IActionResult> DisabledConsumerAsync(string endointName, int subscriberId, [FromBody] EnableConsumerRequest body)
        {
            if (string.IsNullOrEmpty(endointName)) return BadRequest($"EndpointName is required");
            if (subscriberId <= 0) return BadRequest($"SubscriberId is required and must be greater than 0");
            if (body == null) return BadRequest("Request data is required");
            if (string.IsNullOrEmpty(body.Reason)) return BadRequest("Reason is required");

            var who = "Juan";
            var scapedReason = Validator.StringEscape(body.Reason);

            var result = await RelayerAPI.Relayer.PerformChkThenCmdAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endointName}');
                Check(existEndpointName) Error 'The endpoint:{endointName} does not exist.';

                if (existEndpointName)
                {{
                    proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                    existSubscriber = proxyEndpoint.ExistSubscriber({subscriberId});
                    Check(existSubscriber) Error 'The subscriber:{subscriberId} does not exist.';
                }}

            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endointName}');
                subscriber = proxyEndpoint.FindSubscriber({subscriberId});
                subscriber.Disable(itIsThePresent, now, '{who}', '{scapedReason}');
            }}");

            return result;
        }

        [HttpDelete("api/{endpointName}/consumers/{subscriberId}")]
        public async Task<IActionResult> DeleteConsumerAsync(string endpointName, int subscriberId)
        {
            if (string.IsNullOrEmpty(endpointName)) return BadRequest($"EndpointName is required");
            if (subscriberId <= 0) return BadRequest($"SubscriberId is required and must be greater than 0");

            var result = await RelayerAPI.Relayer.PerformChkThenCmdAsync(HttpContext,
            @$"
                existEndpointName = company.System.ServiceMonitor.Exist('{endpointName}');
                Check(existEndpointName) Error 'The endpoint:{endpointName} does not exist.';

                if (existEndpointName)
                {{
                    proxyEndpoint = company.System.ServiceMonitor.Find('{endpointName}');
                    existSubscriber = proxyEndpoint.ExistSubscriber({subscriberId});
                    Check(existSubscriber) Error 'The subscriber:{subscriberId} does not exist.';
                }}

            ", $@"
            {{
                proxyEndpoint = company.System.ServiceMonitor.Find('{endpointName}');
                proxyEndpoint.RemoveEndpointSubscriber(itIsThePresent, {subscriberId});
            }}");

            return result;
        }

    }

    public class EndpointSubscriber
    {
        public string Organization { get; set; }
        public string Description { get; set; }
    }
    public class EnableConsumerRequest
    {
        public string Reason { get; set; }
    }

}
