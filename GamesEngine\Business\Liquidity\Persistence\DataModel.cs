using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class DataModel
    {
        public class Deposit
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public decimal Amount { get; set; }
            public DateTime Date { get; set; }
            public string DateAsText => Date.ToString("MM/dd/yyyy HH:mm:ss");
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string CreatedAsText => Created.ToString("MM/dd/yyyy HH:mm:ss");
            public string State { get; set; }
            public string ExternalId { get; set; }

            public Deposit() { }

            public Deposit(DraftDepositMessage msg)
            {
                Id = msg.DepositId;
                DocumentNumber = msg.TransactionId;
                Amount = msg.Amount;
                Date = msg.DraftDate;
                StoreId = 1; // <<<< NEEDS ACTUAL BUSINESS LOGIC
                AccountNumber = msg.Account;
                DomainId = msg.DomainId;
                Address = msg.Address;
                Created = DateTime.Now;
                State = "Completed"; // <<<< NEEDS ACTUAL BUSINESS LOGIC
                ExternalId = "EXT" + msg.TransactionId; // <<<< NEEDS ACTUAL BUSINESS LOGIC
            }

            public override string ToString()
            {
                return $"ID: {Id}, Domain: {DomainId}, Auth: {DocumentNumber}, Acct: {AccountNumber}, Store: {StoreId}, Date: {Date:MM/dd/yyyy HH:mm:ss}, Amount: {Amount}, Address: {Address}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class Tank
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
            public IEnumerable<int> DepositIds { get; set; }
            public string OriginType { get; set; }
            public long? OriginId { get; set; }

            public Tank() { }

            public Tank(CreatedTankMessage msg)
            {
                Id = msg.TankId;
                Description = msg.Description;
                Created = msg.CreatedAt;
                DepositIds = msg.DepositIds;
                OriginType = "Jar";
                OriginId = msg.JarVersion;
            }

            public Tank(TankMergedMessage msg)
            {
                Id = msg.TankId;
                Description = msg.Description;
                Created = msg.CreatedAt;
                DepositIds = msg.DepositIds;
                OriginType = "Tank";
                OriginId = msg.OriginTankId;
            }

            public override string ToString()
            {
                var originInfo = OriginId.HasValue ? $", Origin: {OriginType} {OriginId.Value}" : "";
                return $"ID: {Id}, Description: {Description}, Created: {Created:MM/dd/yyyy HH:mm:ss}, DepositIds: {string.Join(", ", DepositIds)}{originInfo}";
            }
        }

        public class TankWithDeposits
        {
            public Tank TankInfo { get; set; }
            public List<Deposit> Deposits { get; set; }
            public decimal TotalDepositsAmount { get; set; }
            public ulong DepositsCount { get; set; }

            public TankWithDeposits()
            {
                Deposits = new List<Deposit>();
            }
        }

        public class Tanker
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public Tanker() { }

            public Tanker(CreatedTankerMessage msg)
            {
                Id = msg.TankerId;
                Description = msg.Description;
                Created = msg.CreatedAt;
            }

            public override string ToString()
            {
                return $"ID: {Id}, Description: {Description}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class TankerWithDeposits
        {
            public Tanker TankerInfo { get; set; }
            public List<Deposit> Deposits { get; set; }
            public decimal TotalDepositsAmount { get; set; }
            public ulong DepositsCount { get; set; }

            public TankerWithDeposits()
            {
                Deposits = new List<Deposit>();
            }
        }

        public class Withdrawal
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public decimal Amount { get; set; }
            public DateTime Date { get; set; }
            public string DateAsText => Date.ToString("MM/dd/yyyy HH:mm:ss");
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string CreatedAsText => Created.ToString("MM/dd/yyyy HH:mm:ss");
            public string State { get; set; }
            public string ExternalId { get; set; }

            public Withdrawal()
            {
                State = "Awaiting"; // <<<< NEEDS ACTUAL BUSINESS LOGIC
                ExternalId = "EXT" + DocumentNumber; // <<<< NEEDS ACTUAL BUSINESS LOGIC
            }

            public override string ToString()
            {
                return $"ID: {Id}, Domain: {DomainId}, Auth: {DocumentNumber}, Acct: {AccountNumber}, Store: {StoreId}, Date: {Date:MM/dd/yyyy HH:mm:ss}, Amount: {Amount}, Address: {Address}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class Bottle
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public Bottle() { }

            public Bottle(CreatedBottleMessage msg)
            {
                Id = msg.Id;
                Description = msg.Name;
                Created = msg.CreatedAt;
            }
        }

        public class Dispenser
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public Dispenser() { }

            public Dispenser(CreatedDispenserMessage msg)
            {
                Id = msg.Id;
                Created = msg.StartDate;
            }
        }

        public class DispenserWithWithdrawals
        {
            public Dispenser DispenserInfo { get; set; } = new Dispenser();
            public List<Withdrawal> Withdrawals { get; set; } = new List<Withdrawal>();
            public decimal TotalWithdrawalsAmount { get; set; }
            public ulong WithdrawalsCount { get; set; }
        }

        public class JarWithDeposits
        {
            public List<Deposit> Deposits { get; set; }
            public decimal TotalAmount { get; set; }
            public ulong DepositCount { get; set; }

            public JarWithDeposits()
            {
                Deposits = new List<Deposit>();
            }
        }

        public class DailyDomainSummaryData
        {
            public DateTime TransactionDate { get; set; }
            public int DomainId { get; set; }
            public decimal TotalDepositsAmount { get; set; }
            public decimal TotalWithdrawalsAmount { get; set; }
        }

        public class JarWithOriginData
        {
            public long CurrentJarId { get; set; }
            public JarWithDeposits CurrentJarData { get; set; }
            public JarWithDeposits OriginJarData { get; set; }
            public long? OriginJarId { get; set; }
        }

        public class TankWithOriginData
        {
            public TankWithDeposits CurrentTankData { get; set; }
            public JarWithDeposits OriginJarData { get; set; }
            public TankWithDeposits OriginTankData { get; set; }
        }

        public class InvoicePayment
        {
            public string Kind { get; set; }
            public string InvoiceId { get; set; }
            public string DestinationAddress { get; set; }
            public string ExternalAtAddress { get; set; }
            public decimal PaidAmount { get; set; }
            public DateTime PaidAt { get; set; }

            public InvoicePayment() { }

            public InvoicePayment(InvoicePaidMessage msg)
            {
                Kind = msg.Kind;
                InvoiceId = msg.InvoiceId;
                DestinationAddress = msg.DestinationAddress;
                ExternalAtAddress = msg.ExternalAtAddress;
                PaidAmount = msg.PaidAmount;
                PaidAt = msg.PaidAt;
            }
        }

        public class SearchFilter
        {
            public string Account { get; set; }
            public DateTime? FromDate { get; set; }
            public DateTime? ToDate { get; set; }
            public string TransactionId { get; set; }
            public string State { get; set; }
            public int? InitialIndex { get; set; }
            public int? AmountOfRows { get; set; }
            public string ExternalId { get; set; }
            public string Address { get; set; }

            public override string ToString()
            {
                var sb = new StringBuilder();
                sb.AppendLine($"Account: {Account}");
                sb.AppendLine($"FromDate: {FromDate?.ToString("MM/dd/yyyy HH:mm:ss")}");
                sb.AppendLine($"ToDate: {ToDate?.ToString("MM/dd/yyyy HH:mm:ss")}");
                sb.AppendLine($"TransactionId: {TransactionId}");
                sb.AppendLine($"State: {State}");
                sb.AppendLine($"InitialIndex: {InitialIndex}");
                sb.AppendLine($"AmountOfRows: {AmountOfRows}");
                sb.AppendLine($"ExternalId: {ExternalId}");
                sb.AppendLine($"Address: {Address}");
                return sb.ToString();
            }
        }

        public class TransactionSearchResult
        {
            public string Date { get; set; }
            public string TransactionId { get; set; }
            public string ExternalId { get; set; }
            public string Type { get; set; }
            public decimal? Deposit { get; set; }
            public decimal? Withdrawal { get; set; }
            public string State { get; set; }
            public string Address { get; set; }
        }

        public class TransactionSearchContainer
        {
            public IEnumerable<TransactionSearchResult> Transactions { get; set; }
            public decimal TotalDeposits { get; set; }
            public decimal TotalWithdrawals { get; set; }
            public long TotalTransactions { get; set; }
        }
    }
}
