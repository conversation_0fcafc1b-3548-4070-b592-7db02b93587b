﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{       
    internal struct KenoDraw : IPick
    {
        private readonly KenoBall[] numbers;
        private MultiplierDraw multiplier;
        private BulleyeDraw bulleye;
        private KenoDraw(KenoBall pair1, KenoBall pair2, KenoBall pair3, KenoBall pair4, KenoBall pair5, KenoBall pair6, KenoBall pair7, KenoBall pair8, KenoBall pair9, KenoBall pair10, KenoBall pair11, KenoBall pair12, KenoBall pair13, KenoBall pair14, KenoBall pair15, KenoBall pair16, KenoBall pair17, KenoBall pair18, KenoBall pair19, KenoBall pair20, MultiplierDraw multiplier, BulleyeDraw bulleye)
        {
            if (pair2.LowerOrEqualsThan(pair1)  &&
                pair3.LowerOrEqualsThan(pair2) &&
                pair4.LowerOrEqualsThan(pair3) &&
                pair5.LowerOrEqualsThan(pair4) &&
                pair6.LowerOrEqualsThan(pair5) &&
                pair7.LowerOrEqualsThan(pair6) &&
                pair8.LowerOrEqualsThan(pair7) &&
                pair9.LowerOrEqualsThan(pair8) &&
                pair10.LowerOrEqualsThan(pair9) &&
                pair11.LowerOrEqualsThan(pair10) &&
                pair12.LowerOrEqualsThan(pair11) &&
                pair13.LowerOrEqualsThan(pair12) &&
                pair14.LowerOrEqualsThan(pair13) &&
                pair15.LowerOrEqualsThan(pair14) &&
                pair16.LowerOrEqualsThan(pair15) &&
                pair17.LowerOrEqualsThan(pair16) &&
                pair18.LowerOrEqualsThan(pair17) &&
                pair19.LowerOrEqualsThan(pair18) &&
                pair20.LowerOrEqualsThan(pair19) 
            )
            {
                throw new GameEngineException($"Every Keno number must be unique. {pair1},{pair2},{pair3},{pair4},{pair5},{pair6},{pair7},{pair8},{pair9},{pair10},{pair11},{pair12},{pair13},{pair14},{pair15},{pair16},{pair17},{pair18},{pair19},{pair20} has duplicated numbers");
            }
            numbers = new KenoBall[20];
            numbers[0] = pair1;
            numbers[1] = pair2;
            numbers[2] = pair3;
            numbers[3] = pair4;
            numbers[4] = pair5;
            numbers[5] = pair6;
            numbers[6] = pair7;
            numbers[7] = pair8;
            numbers[8] = pair9;
            numbers[9] = pair10;
            numbers[10] = pair11;
            numbers[11] = pair12;
            numbers[12] = pair13;
            numbers[13] = pair14;
            numbers[14] = pair15;
            numbers[15] = pair16;
            numbers[16] = pair17;
            numbers[17] = pair18;
            numbers[18] = pair19;
            numbers[19] = pair20;
            this.multiplier = multiplier;
            this.bulleye = bulleye;
        }
        internal KenoDraw(int number1, int number2, int number3, int number4, int number5, int number6, int number7, int number8, int number9, int number10, int number11, int number12, int number13, int number14, int number15, int number16, int number17, int number18, int number19, int number20, int multiplier, int bulleye) :
             this(new KenoBall(number1), new KenoBall(number2), new KenoBall(number3), new KenoBall(number4), new KenoBall(number5), new KenoBall(number6), new KenoBall(number7), new KenoBall(number8), new KenoBall(number9), new KenoBall(number10), new KenoBall(number11), new KenoBall(number12), new KenoBall(number13), new KenoBall(number14), new KenoBall(number15), new KenoBall(number16), new KenoBall(number17), new KenoBall(number18), new KenoBall(number19), new KenoBall(number20), new MultiplierDraw(multiplier), new BulleyeDraw(bulleye))
        {
        }

        Pattern IPick.this[int i] => throw new GameEngineException("Keno does not have single digits as there are in Picks lotteries.");

        IEnumerable<IPick> IPick.Permute() => throw new GameEngineException("Keno does not have permutations.");

        internal MultiplierDraw Multiplier
        {
            get
            {
                return multiplier;
            }
        }
        internal BulleyeDraw Bulleye
        {
            get
            {
                return bulleye;
            }
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            result.Add(this);
            return result;
        }

        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            result.Add(new SubTicket<IPick>(this));
            return result;
        }

        int IPick.Length => Length;
        public int Length
        {
            get
            {
                return numbers.Length;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return numbers.Length;
            }
        }

        string IPick.AsString() => AsString();
        internal string AsString()
        {
            return $"[{numbers[0]},{numbers[1]},{numbers[2]},{numbers[3]},{numbers[4]},{numbers[5]},{numbers[6]},{numbers[7]},{numbers[8]},{numbers[9]},{numbers[10]},{numbers[11]},{numbers[12]},{numbers[13]},{numbers[14]},{numbers[15]},{numbers[16]},{numbers[17]},{numbers[18]},{numbers[19]},{multiplier},{bulleye}]";
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            return $"[{numbers[0].AsStringForAccounting()}-{numbers[1].AsStringForAccounting()}-{numbers[2].AsStringForAccounting()}-{numbers[3].AsStringForAccounting()}-{numbers[4].AsStringForAccounting()}-{numbers[5].AsStringForAccounting()}-{numbers[6].AsStringForAccounting()}-{numbers[7].AsStringForAccounting()}-{numbers[8].AsStringForAccounting()}-{numbers[9].AsStringForAccounting()}-{numbers[10].AsStringForAccounting()}-{numbers[11].AsStringForAccounting()}-{numbers[12].AsStringForAccounting()}-{numbers[13].AsStringForAccounting()}-{numbers[14].AsStringForAccounting()}-{numbers[15].AsStringForAccounting()}-{numbers[16].AsStringForAccounting()}-{numbers[17].AsStringForAccounting()}-{numbers[18].AsStringForAccounting()}-{numbers[19].AsStringForAccounting()}-{multiplier.AsStringForAccounting()}-{bulleye.AsStringForAccounting()}]";
        }

        internal int Matches(KenoBall[] numbersToSearch)
        {
            int result = 0;
            foreach (KenoBall number in numbers)
            {
                if (numbersToSearch.Contains(number))
                {
                    result += 1;
                }
            }
            return result;
        }

        public long ToInt64()
        {
            return
              Convert.ToInt32(12) * *********0000000000 +
              this.numbers[5].ToInt32() * *********00000000 +
              this.numbers[6].ToInt32() * *********000000 +
              this.numbers[7].ToInt32() * *********0000 +
              this.numbers[8].ToInt32() * *********00 +
              this.numbers[9].ToInt32() * ********* +
              this.numbers[10].ToInt32() * 1000000 +
              this.numbers[11].ToInt32() * 10000 +
              Convert.ToInt32(this.multiplier) * 100 +
              Convert.ToInt32(this.bulleye);
        }
    }
}
