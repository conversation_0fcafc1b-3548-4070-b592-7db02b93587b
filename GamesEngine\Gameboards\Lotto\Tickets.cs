﻿using GamesEngine.Bets;
using GamesEngine.Games.Lotto;
using GamesEngine.Time;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Exchange.RiskProfilesLotteries;
using static GamesEngine.Games.Lotto.TicketsOfDraw;

namespace GamesEngine.Gameboards.Lotto
{
    internal class Tickets : Gameboards
    {
        private readonly Dictionary<DateTime, TicketsOfDraw> tickets = new Dictionary<DateTime, TicketsOfDraw>();
        private readonly Lottery lottery;

        internal Lottery Lottery
        {
            get
            {
                return lottery;
            }
        }

        internal Tickets()
        {

        }

        internal Tickets(Lottery lottery)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));
            this.lottery = lottery;
        }

        internal void Add(DateTime date, Ticket ticket)
        {
            Add(date, ticket, this.lottery);
        }

        internal void Add(DateTime date, Ticket ticket, Lottery lottery)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null)
            {
                set = new TicketsOfDraw(lottery, date);
                tickets.Add(date, set);
            }
            set.AddTicket(ticket);
        }
        
        internal new int NextSequence()
        {
            int result = -1;
            if (this.lottery != null)
			{
                result = this.lottery.Company.Gameboards.NextSequence();//TODO Una vez hecho el refactor de Tickets, solo deberia existir una sequence por compania
            }
            else
			{
                result = base.NextSequence();
			}
            return result;
        }

        internal TicketsOfDraw TicketsInMemory(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = tickets.GetValueOrDefault(drawDate);
            return result;
        }

        internal bool AnyTicketsInMemory(DateTime drawDate)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = tickets.ContainsKey(drawDate);
            return result;
        }

        internal DrawingsSummaryReport LotteryDraw(DateTime date, LotteryDraw winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName)
        {
            if (date == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (winner == null) throw new ArgumentNullException(nameof(winner));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            DrawingsSummaryReport reportResult = null;
            switch (lottery.IdOfLottery)
            {
                case IdOfLottery.P2:
                case IdOfLottery.P3:
                case IdOfLottery.P4:
                case IdOfLottery.P5:
                    if (ExistTicketsAt(date))
                    {
                        PicksDrawingsSummaryReport noFbpicksReport = LotteryDrawPickAndPowerBall(date, winner, itIsThePresent, now, previousSequenceOfNumbers, employeeName);
                        reportResult = noFbpicksReport;
                    }
                    break;
                case IdOfLottery.TZ:
                    //TRIZ DOES NOT HAVE TICKETS
                    return new EmptyDrawingsSummaryReport();
                case IdOfLottery.PB:
                    if (ExistTicketsAt(date))
                    {
                        PicksDrawingsSummaryReport powerBallDrawingsSummary = LotteryDrawPickAndPowerBall(date, winner, itIsThePresent, now, previousSequenceOfNumbers, employeeName);
                        reportResult = powerBallDrawingsSummary;
                    }
                    break;
                case IdOfLottery.KN:
                    if (ExistTicketsAt(date))
                    {
                        KenoDrawingsSummaryReport kenoDrawingsSummaryReport = LotteryDrawKeno(date, winner as LotteryDrawKeno, itIsThePresent, now, previousSequenceOfNumbers, employeeName);
                        reportResult = kenoDrawingsSummaryReport;
                    }
                    break;
                default:
                    throw new GameEngineException($"Cannot Draw for {nameof(IdOfLottery)} {lottery.IdOfLottery}, does not have any {nameof(DrawingsSummaryReport)} case.");
            }

            if (reportResult != null) CommitGradeOrRegradeAndSendToHistorical(date, itIsThePresent);
            
            return reportResult;
        }

        private PicksDrawingsSummaryReport LotteryDrawPickAndPowerBall(DateTime date, LotteryDraw winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName)
        {
            if (winner == null) throw new ArgumentNullException(nameof(winner));
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            var gradeStatusOfSet = set.GeneralGradeStatus();
            int currentVersion = winner.Version;

            if (gradeStatusOfSet.AllHaveTheSameGradeStatus && (gradeStatusOfSet.GameboardStatus == GameboardStatus.GRADED || gradeStatusOfSet.GameboardStatus == GameboardStatus.NOACTION))
			{
				set.GradeAGraded(winner, itIsThePresent, now, previousSequenceOfNumbers, employeeName, currentVersion);
		    }
            //TODO: Aqui habia que preguntar por algo y dejar el else para un GEE. Hay que hacerlo en CdeL
            else
            {
                set.LotteryDraw(winner, itIsThePresent, now, employeeName);
                DeleteExpiredTickets(date);
            }
		
            return new PicksDrawingsSummaryReport(set);
        }

        private KenoDrawingsSummaryReport LotteryDrawKeno(DateTime date, LotteryDrawKeno winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName)
        {
            if (winner == null) throw new ArgumentNullException(nameof(winner));
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            var gradeStatusOfSet = set.GeneralGradeStatus();

            if (gradeStatusOfSet.AllHaveTheSameGradeStatus && (gradeStatusOfSet.GameboardStatus == GameboardStatus.GRADED || gradeStatusOfSet.GameboardStatus == GameboardStatus.NOACTION))
            {
                set.GradeAGradedKeno(winner, itIsThePresent, now, employeeName);
            }
            else
            {
                set.LotteryDrawKeno(winner, itIsThePresent, now, employeeName);
                DeleteExpiredTickets(date);
            }

            return new KenoDrawingsSummaryReport(set);
        }

        internal List<PayFragmentsMessage> CreateMessagesToResendTickets(DateTime date, DateTime now)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");

            var gradeStatusOfSet = set.GeneralGradeStatus();
            if (!gradeStatusOfSet.AllHaveTheSameGradeStatus) throw new GameEngineException($"Some tickets have different {nameof(GameboardStatus)}");

            switch (gradeStatusOfSet.GameboardStatus)
            {
                case GameboardStatus.GRADED:
                    return set.CreateMessagesToResendGradedTickets(now);
                case GameboardStatus.REGRADED:
                    return set.CreateMessagesToResendRegradedTickets(now);
                case GameboardStatus.NOACTION:
                    return set.CreateMessagesToResendNoActionTickets(now);
                default:
                    throw new GameEngineException($"{nameof(TicketsOfDraw)} cannot be in {nameof(GameboardStatus)} {gradeStatusOfSet.GameboardStatus}");
            }
        }

        internal void ResendTicketsToHistorical(DateTime date, bool itIsThePresent)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");

            var gradeStatusOfSet = set.GeneralGradeStatus();
            if (!gradeStatusOfSet.AllHaveTheSameGradeStatus) throw new GameEngineException($"Some tickets have different {nameof(GameboardStatus)}");

            switch (gradeStatusOfSet.GameboardStatus)
            {
                case GameboardStatus.GRADED:
                    set.ResendGradeToHistorical(itIsThePresent);
                    break;
                case GameboardStatus.REGRADED:
                    set.ResendRegradeAsLosersToHistorical(itIsThePresent);
                    break;
                case GameboardStatus.NOACTION:
                    set.ResendNoActionToHistorical(itIsThePresent);
                    break;
                default:
                    throw new GameEngineException($"{nameof(TicketsOfDraw)} cannot be in {nameof(GameboardStatus)} {gradeStatusOfSet.GameboardStatus}");
            }
        }

        internal void RollbackFromGradeToPending(DateTime date, LotteryDraw winner, DateTime now, string previousSequenceOfNumbers)
		{
			TicketsOfDraw set = tickets.GetValueOrDefault(date);
			if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
			var gradeStatusOfSet = set.GeneralGradeStatus();
			if (gradeStatusOfSet.AllHaveTheSameGradeStatus && (gradeStatusOfSet.GameboardStatus == GameboardStatus.GRADED || gradeStatusOfSet.GameboardStatus == GameboardStatus.REGRADED))
			{
				set.RollbackFromGradeToPending(winner, now, previousSequenceOfNumbers);
			}
			else
			{
				set.RollbackFromGradeToPending();
			}
		}

		internal void CommitGradeOrRegradeAndSendToHistorical(DateTime date, bool itIsThePresent)
		{
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
			if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
			var gradeStatusOfSet = set.GeneralGradeStatus();
			if (gradeStatusOfSet.AllHaveTheSameGradeStatus && (gradeStatusOfSet.GameboardStatus == GameboardStatus.GRADED || gradeStatusOfSet.GameboardStatus == GameboardStatus.REGRADED))
			{
				set.CommitRegrade(itIsThePresent);
			}
			else
			{
				set.CommitGrade(itIsThePresent);
			}
		}

		internal void RollbackFromRegradeToPending(DateTime date, LotteryDraw winner, DateTime now, string previousSequenceOfNumbers)
		{
			TicketsOfDraw set = tickets.GetValueOrDefault(date);
			if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
			set.RollbackFromRegradeToPending(winner, now, previousSequenceOfNumbers);
		}

		internal void CommitRegradeAndSendToHistorical(DateTime date, bool itIsThePresent)
		{
			TicketsOfDraw set = tickets.GetValueOrDefault(date);
			if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
			set.CommitRegrade(itIsThePresent);
		}

		private void DeleteExpiredTickets(DateTime date)
        {
            var expiredDrawDates = ExpiredDrawDates(date);
            foreach (var expiredDate in expiredDrawDates)
            {
                TicketsOfDraw expiredTicketsOfDraw = tickets.GetValueOrDefault(expiredDate);
                var gradeStatusOfSet = expiredTicketsOfDraw.GeneralGradeStatus();
                if (gradeStatusOfSet.AllHaveTheSameGradeStatus)
                {
                    switch (gradeStatusOfSet.GameboardStatus)
                    {
                        case GameboardStatus.GRADED:
                            DeleteWinnersAndLosers(expiredDate, expiredTicketsOfDraw);
                            break;
                        case GameboardStatus.NOACTION:
                            DeleteNoAction(expiredDate, expiredTicketsOfDraw);
                            break;
                    }
                }
                RemoveDisposedTickets(expiredTicketsOfDraw);
                lottery.RemoveDisabledSchedules(expiredDate);
            }
            this.lottery.RemoveDisposedTickets();
        }

        private void RemoveDisposedTickets(TicketsOfDraw expiredTicketsOfDraw)
        {
            var playersToUpdate = expiredTicketsOfDraw.Tickets
                .Where(
                    x => x.IsReadyToBeDisposed()
                )
                .Select(ticket => ticket.Player)
                .Distinct();
            foreach (var player in playersToUpdate)
            {
                base.RemoveDisposedGameboards(player);
            }
        }

        internal PicksDrawingsSummaryReport Regrade(DateTime date, LotteryDraw winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName)
        {
            if (winner == null) throw new ArgumentNullException(nameof(winner));
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            var generalStatus = set.GeneralGradeStatus();
            int currentVersion = winner.Version;
            if (generalStatus.AllHaveTheSameGradeStatus && generalStatus.GameboardStatus == GameboardStatus.GRADED)
            {
                set.Regrade(winner, itIsThePresent, now, previousSequenceOfNumbers, employeeName, currentVersion);
            }
            return new PicksDrawingsSummaryReport(set);
        }

        internal PicksDrawingsSummaryReport RegradeKeno(DateTime date, LotteryDrawKeno winner, bool itIsThePresent, DateTime now, string previousSequenceOfNumbers, string employeeName)
        {
            if (winner == null) throw new ArgumentNullException(nameof(winner));
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            var generalStatus = set.GeneralGradeStatus();
            int currentVersion = winner.Version;
            if (generalStatus.AllHaveTheSameGradeStatus && generalStatus.GameboardStatus == GameboardStatus.GRADED)
            {
                set.RegradeKeno(winner, itIsThePresent, now, previousSequenceOfNumbers, employeeName, currentVersion);
            }
            return new PicksDrawingsSummaryReport(set);
        }

        internal NoActionReport SetNoAction(DateTime date, bool itIsThePresent, DateTime now, string employeeName, LotteryNoAction lotteryNoAction)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");

            var ticketsWithoutNoActionYet = set.TicketsWithoutNoAction();
            var noActionReport = new NoActionReport(ticketsWithoutNoActionYet);
            if (lottery is LotteryKeno)
                set.SetNoActionKeno(itIsThePresent, employeeName, now, lotteryNoAction);
            else
                set.SetNoAction(itIsThePresent, employeeName, now, lotteryNoAction);
            return noActionReport;
        }

        internal void SendToHistoricalLateGradedTickets(bool itIsThePresent, DateTime date, string employeeName)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            set.SendToHistoricalLateGradedTickets(itIsThePresent, employeeName);
        }

        internal void SendToHistoricalLateNoActionTickets(bool itIsThePresent, DateTime date, string employeeName)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            set.SendToHistoricalLateNoActionTickets(itIsThePresent, employeeName);
        }

        internal bool IsOnlyOneGradedTicketWithoutNumber(DateTime date)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            return set.IsOnlyOneGradedTicketWithoutNumber();
        }

        internal bool IsOnlyOneNoActionTicketWithoutNumber(DateTime date)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) throw new GameEngineException($"Draw on date {date} is unknown");
            return set.IsOnlyOneNoActionTicketWithoutNumber();
        }

        internal bool AnyFakeTicketNumberAt(DateTime date)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) return false;
            return set.AnyFakeTicketNumber();
        }

        private IEnumerable<DateTime> ExpiredDrawDates(DateTime date)
        {
            var hoursKeepingAliveTickets = lottery.PicksLotteryGame.Company.Settings.HoursKeepingAliveTickets;
            var limitDate = date.AddHours(-hoursKeepingAliveTickets);
            var dates =
                tickets.Where(
                    pair => {
                        DateTime drawDate = pair.Key;
                        TicketsOfDraw set = pair.Value;
                        return drawDate < limitDate && set.Count > 0 && !set.Lottery.IsPendingFor(drawDate);
                    }
                )
                .Select(
                    pair => pair.Key
                ).ToList();
            return dates;
        }

        internal void DeleteWinnersAndLosers(DateTime date, TicketsOfDraw set)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));
            bool thisDrawSoldSomeTickets = set != null;
            if (thisDrawSoldSomeTickets)
            {
                var domains = set.DomainsWhereBelongThisSet();
                foreach (var domain in domains)
                {
                    ((RiskProfileLotteries)lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(domain)).Risks.Risk.GetRiskPerLottery(lottery).RemoveAccumulatedToWinAt(date);
                }
                
                set.DeleteWinnersAndLosers();
                if (set.IsDeleted)
                {
                    tickets.Remove(date);
                }
            }
        }

        internal void DeleteNoAction(DateTime date, TicketsOfDraw set)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));
            bool thisDrawSoldSomeTickets = set != null;
            if (thisDrawSoldSomeTickets)
            {
                var domains = set.DomainsWhereBelongThisSet();
                foreach (var domain in domains)
                {
                    ((RiskProfileLotteries)lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(domain)).Risks.Risk.GetRiskPerLottery(lottery).RemoveAccumulatedToWinAt(date);
                }

                set.DeleteNoAction();
                if (set.IsDeleted)
                {
                    tickets.Remove(date);
                    lottery.RemoveDisabledSchedules(date);
                }
            }
        }
        internal bool ExistTicketsAt(int minute)
        {
            var exists = tickets.Keys.Any(d =>  d.Minute == minute);
            return exists;
        }

        internal bool ExistTicketsAt(HourMinute moment)
        {
            var exists = tickets.Keys.Any(d => d.Hour == moment.Hour && d.Minute == moment.Minute);
            return exists;
        }

        internal bool ExistTicketsAt(DateTime date)
        {
            var exists = tickets.Keys.Any(d => d == date);
            return exists;
        }

        internal bool ExistPendingTicketsAt(DateTime date)
        {
            var ticketOfDay = tickets.GetValueOrDefault(date);
            if (ticketOfDay == null) return false;

            var status = ticketOfDay.GeneralGradeStatus();
            var exists = status.AllHaveTheSameGradeStatus && status.GameboardStatus == GameboardStatus.PENDING;
            return exists;
        }

        internal void ChangePendingTicketsHour(int oldMinutes, int newMinutes) 
        {
            Dictionary<DateTime, TicketsOfDraw> ticketsToDelete = new Dictionary<DateTime, TicketsOfDraw>();

            foreach (DateTime date in tickets.Keys)
            {
                var ticketsPending = tickets[date];
                bool needsChange =  date.Minute == oldMinutes;
                needsChange = needsChange && !lottery.IsAlreadyGraded(date);
                needsChange = needsChange && !lottery.IsScheduleMarkedAsNoActionAt(date);
                needsChange = needsChange && !lottery.IsRegraded(date);
                if (needsChange)
                {
                    ticketsToDelete.Add(date, tickets[date]);
                }
            }
            foreach (DateTime date in ticketsToDelete.Keys)
            {
                tickets.Remove(date);
            }
            foreach (DateTime date in ticketsToDelete.Keys)
            {
                var newDate = new DateTime(date.Year, date.Month, date.Day, date.Hour, newMinutes, date.Second);
                var ticketsOfThisDraw = ticketsToDelete[date];
                tickets.Add(newDate, ticketsOfThisDraw);
                ticketsOfThisDraw.ChangeTicketsHour(newDate);
            }
        }
        internal void ChangePendingTicketsHour(HourMinute oldHour, HourMinute newHour)
        {
            if (oldHour == null) throw new ArgumentNullException(nameof(oldHour));
            if (newHour == null) throw new ArgumentNullException(nameof(newHour));
            if (oldHour == newHour) throw new GameEngineException("The hours cannot be the same");

            Dictionary<DateTime, TicketsOfDraw> ticketsToDelete = new Dictionary<DateTime, TicketsOfDraw>();

            foreach (DateTime date in tickets.Keys)
            {
                var ticketsPending = tickets[date];
                bool needsChange = date.Hour == oldHour.Hour && date.Minute == oldHour.Minute;
                needsChange = needsChange && !lottery.IsAlreadyGraded(date);
                needsChange = needsChange && !lottery.IsScheduleMarkedAsNoActionAt(date);
                needsChange = needsChange && !lottery.IsRegraded(date);
                if (needsChange)
                {
                    ticketsToDelete.Add(date, tickets[date]);
                }
            }
            foreach (DateTime date in ticketsToDelete.Keys)
            {
                tickets.Remove(date);
            }
            foreach (DateTime date in ticketsToDelete.Keys)
            {
                var newDate = new DateTime(date.Year, date.Month, date.Day, newHour.Hour, newHour.Minute, date.Second);
                var ticketsOfThisDraw = ticketsToDelete[date];
                tickets.Add(newDate, ticketsOfThisDraw);
                ticketsOfThisDraw.ChangeTicketsHour(newDate);
            }
        }

        internal bool HasPendingTicketsFor(Schedule schedule, DayOfWeek day)
        {
            foreach (DateTime date in tickets.Keys)
            {
                if (date.DayOfWeek == day)
                {
                    if (tickets.Keys.Contains(date))
                    {
                        var ticketsOfDraw = tickets[date];
                        if (ticketsOfDraw.Tickets.Any(x =>  x.DrawDate.Hour == schedule.Hour.Hour
                        && x.DrawDate.Minute == schedule.Hour.Minute && x.IsPending())) return true;
                    }
                }
            }
            return false;
        }

        internal IEnumerable<Ticket> TicketsOfDrawAt(DateTime date)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            if (set == null) return Enumerable.Empty<Ticket>();
            return set.Tickets;
        }

        internal IEnumerable<Ticket> TicketsOfDrawWithoutNoActionAt(DateTime date)
        {
            TicketsOfDraw set = tickets.GetValueOrDefault(date);
            var result = set.TicketsWithoutNoAction();
            return result;
        }

		internal IEnumerable<Ticket> TicketsAt(Schedule schedule)
        {
            if (schedule == null) throw new ArgumentNullException(nameof(schedule));

            var tickets = this.tickets.Where(x => schedule.Hour.Hour == x.Key.Hour && schedule.Hour.Minute == x.Key.Minute).
                SelectMany(x => x.Value.Tickets);
            return tickets;
        }

        internal IEnumerable<Ticket> AllTickets()
        {
            var tickets = this.tickets.Values.SelectMany(x => x.Tickets);
            return tickets;
        }

        internal IEnumerable<TicketWager> Wagers()
        {
            var wagers = this.tickets.Values.SelectMany(x => x.Tickets).SelectMany(x => x.Wagers);
            return wagers;
        }

        internal IEnumerable<Ticket> SearchTicketByNumber(int ticketNumber)
        {
            var ticketsToRemove = tickets.Values.SelectMany(x => x.SearchTicketByNumber(ticketNumber));
            return ticketsToRemove;
        }

        internal IEnumerable<Ticket> TicketsWithDrawDateBetween(DateTime startDrawDay, DateTime endDrawDay)
        {
            if (startDrawDay.Hour != 0 || startDrawDay.Minute != 0 || startDrawDay.Second != 0 || startDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(startDrawDay)} '{startDrawDay}' must be an exact day");
            if (endDrawDay.Hour != 0 || endDrawDay.Minute != 0 || endDrawDay.Second != 0 || endDrawDay.Millisecond != 0) throw new GameEngineException($"{nameof(endDrawDay)} '{endDrawDay}' must be an exact day");

            var result = tickets.Values.SelectMany(x => x.TicketsWithDrawDateBetween(startDrawDay, endDrawDay));
            return result;
        }

        internal IEnumerable<Ticket> FindActionableTickets(IEnumerable<string> ticketNumbers)
        {
            var result = tickets.Values.SelectMany(x => x.FindActionableTickets(ticketNumbers));
            return result;
        }

        internal IEnumerable<Ticket> FindTicketsMatchingWith(IEnumerable<int> ticketNumbers)
        {
            var result = tickets.Values.SelectMany(x => x.FindTicketsMatchingWith(ticketNumbers));
            return result;
        }

        internal Ticket FindTicketMatchingWith(int ticketNumber)
        {
            var result = tickets.Values.Select(x => x.FindTicketMatchingWith(ticketNumber)).FirstOrDefault(x => x != null);
            return result;
        }

        internal IEnumerable<Ticket> FindTicketsInRange(int theLowestBetId, int theHighestBetId)
        {
            var result = tickets.Values.SelectMany(x => x.FindTicketsInRange(theLowestBetId, theHighestBetId));
            return result;
        }

        internal TicketWager WagerToRemoveFor(int ticketNumber, int wagerNumber)
        {
            var result = tickets.Values.Select(x => x.WagerToRemoveFor(ticketNumber, wagerNumber)).FirstOrDefault(x => x != null);
            return result;
        }

        internal IEnumerable<PendingDraw> PendingDraws()
        {
            var pendingDraws = new List<PendingDraw>();
            foreach (var ticketPair in tickets)
            {
                var pendingOrRegradedTickets = ticketPair.Value.PendingOrRegradedTickets();
                if (pendingOrRegradedTickets.Count() > 0)
                {
                    pendingDraws.Add(new PendingDraw(lottery, ticketPair.Key, pendingOrRegradedTickets));
                }
            }
            return pendingDraws;
        }

        Dictionary<TicketsWithCommonDomainKey, TicketsWithCommonDomain> GetTicketsWithCommonDomain(string domainIds)
        {
            var areAllDomainsValid = domainIds == Reports.DOMAIN_ID_ALL;
            var ticketsInDomains = new Dictionary<TicketsWithCommonDomainKey, TicketsWithCommonDomain>();
            string[] domainIdsArray = null;
            if (!areAllDomainsValid)
            {
                domainIdsArray = domainIds.Split(',');
            }
            foreach (var ticketPair in tickets)
            {
                var pendingOrRegradedTickets = ticketPair.Value.PendingOrRegradedTickets();
                if (pendingOrRegradedTickets.Count() > 0)
                {
                    foreach (var ticket in pendingOrRegradedTickets)
                    {
                        if (areAllDomainsValid || (domainIdsArray != null && domainIdsArray.Contains(ticket.DomainId.ToString())))
                        {
                            TicketsWithCommonDomain ticketsWithCommonDomain;
                            var key = new TicketsWithCommonDomainKey(ticket.DrawingId, ticket.DomainId, ticket.DrawDate);
                            ticketsInDomains.TryGetValue(key, out ticketsWithCommonDomain);
                            if (ticketsWithCommonDomain == null)
                            {
                                ticketsWithCommonDomain = new TicketsWithCommonDomain(ticket.DrawingId, ticket.DomainId);
                                ticketsInDomains.Add(key, ticketsWithCommonDomain);
                            }
                            ticketsWithCommonDomain.TryToAdd(ticket);
                        }
                    }
                }
            }

            return ticketsInDomains;
        }

        internal IEnumerable<PendingDraw> PendingDrawsBy(string domainIds)
        {
            var ticketsInDomains = GetTicketsWithCommonDomain(domainIds);
            var pendingDraws = new List<PendingDraw>();
            foreach (var ticketsWithCommonDomain in ticketsInDomains.Values)
            {
                if (!ticketsWithCommonDomain.IsEmpty())
                {
                    var draw = new PendingDraw(ticketsWithCommonDomain.Lottery, ticketsWithCommonDomain.DrawDate, ticketsWithCommonDomain);
                    pendingDraws.Add(draw);
                }
            }

            return pendingDraws;
        }

        internal IEnumerable<PendingKenoDraw> PendingKenoDrawsBy(string domainIds)
        {
            var ticketsInDomains = GetTicketsWithCommonDomain(domainIds);
            var pendingDraws = new List<PendingKenoDraw>();
            foreach (var ticketsWithCommonDomain in ticketsInDomains.Values)
            {
                if (!ticketsWithCommonDomain.IsEmpty())
                {
                    var draw = new PendingKenoDraw(ticketsWithCommonDomain.Lottery, ticketsWithCommonDomain.DrawDate, ticketsWithCommonDomain);
                    pendingDraws.Add(draw);
                }
            }

            return pendingDraws;
        }

        struct TicketsWithCommonDomainKey
        {
            internal int DrawingId { get; }
            internal int DomainId { get; }
            internal DateTime DrawDate { get; }

            internal TicketsWithCommonDomainKey(int drawingId, int domainId, DateTime drawDate)
            {
                DrawingId = drawingId;
                DomainId = domainId;
                DrawDate = drawDate;
            }
        }
    }
}
