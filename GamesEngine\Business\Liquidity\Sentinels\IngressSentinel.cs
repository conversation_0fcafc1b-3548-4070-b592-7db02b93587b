﻿using GamesEngine.Business.Liquidity.Sentinels.Currency;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class IngressSentinel : Sentinel
    {
        private readonly ConcurrentDictionary<int, Deposit> wholeDeposits = new ConcurrentDictionary<int, Deposit>();
        private readonly ConcurrentDictionary<string, Deposit> confirmedInvoiceDeposits = new ConcurrentDictionary<string, Deposit>();
        private readonly LRUAccounts lruAccounts = new LRUAccounts();

        private readonly SentinelInboundTasks sentinelInboundTasks = new SentinelInboundTasks();

        internal IngressSentinel() { }

        internal IEnumerable<InboundTask> InboundTasks => sentinelInboundTasks.Tasks;

        internal IEnumerable<Deposit> Deposits => wholeDeposits.Values;

        internal IEnumerable<AccountAmount> LastAccounts => lruAccounts.LastAccounts;

        internal void AwaitForDeposit(Deposit unconfirmedDeposit, int totalConfirmations)
        {
            if (unconfirmedDeposit == null) throw new ArgumentNullException(nameof(unconfirmedDeposit), "Pending deposit cannot be null.");
            if (totalConfirmations < 0) throw new ArgumentOutOfRangeException(nameof(totalConfirmations), "Total confirmations cannot be negative.");

            try
            {
                wholeDeposits.TryAdd(unconfirmedDeposit.Id, unconfirmedDeposit);
                sentinelInboundTasks.AddConfirmationsWatcher(unconfirmedDeposit, totalConfirmations);
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error adding confirmation watcher for deposit {unconfirmedDeposit.Id}. It may not be being watched by the sentinel.", null);
            }
        }

        internal void StopAndDetachInboundTask(Deposit deposit)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit), "Deposit cannot be null.");
            try
            {
                InboundTask inboundTask = sentinelInboundTasks.FindInboundTask(deposit.InvoiceId);
                switch (inboundTask)
                {
                    case ConfirmationsInboundTask confirmationsInboundTask:
                        confirmationsInboundTask.Cancellation.Cancel();
                        confirmationsInboundTask.StartInboundTask();//WILL END IT IF IT WAS CANCELLED
                        break;
                    default:
                        throw new GameEngineException($"Unsupported inbound task type: {inboundTask.GetType().Name} for deposit {deposit.Id}.");
                }
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Sentinel.Error($"No inbound task found for deposit {deposit.Id}.", null);
            }
        }

        internal bool ExistInvoiceInDeposits(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");
            return wholeDeposits.Any(d => d.Value.InvoiceId == invoiceId);
        }

        internal bool ExistConfirmedInvoice(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");
            return confirmedInvoiceDeposits.ContainsKey(invoiceId);
        }

        internal void InvoiceReceivedPayment(bool itIsThePresent, DateTime now, string invoiceId, decimal invoiceDue, decimal totalPaid, decimal rate, string paymentId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (string.IsNullOrWhiteSpace(paymentId)) throw new ArgumentNullException(nameof(paymentId));
            if (totalPaid <= 0) throw new ArgumentOutOfRangeException(nameof(totalPaid));
            if (rate <= 0) throw new ArgumentOutOfRangeException(nameof(rate), "Rate must be greater than zero.");

            Deposit depositWithInvoice = null;
            foreach (var d in wholeDeposits)
            {
                if (d.Value.InvoiceId == invoiceId)
                {
                    depositWithInvoice = d.Value;
                    break;
                }
            }
            if (depositWithInvoice == null) throw new KeyNotFoundException($"Deposit with invoice ID {invoiceId} not found in the whole deposits.");
            if (!depositWithInvoice.IsPending) return;

            try
            {
                ConfirmationsInboundTask inboundTask = sentinelInboundTasks.FindInboundTask(invoiceId) as ConfirmationsInboundTask;
                if (inboundTask == null) throw new GameEngineException($"No inbound task found for invoice ID: {invoiceId}.");

                inboundTask.StartInboundTask(new Invoice(invoiceId, invoiceDue, totalPaid, rate, paymentId));

                if (itIsThePresent)
                {
                    if (Integration.UseKafka)
                    {
                        using var buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}");
                        var invoicePaidMessage = new InvoicePaidMessage(
                        depositWithInvoice.Kind,
                        invoiceId,
                        depositWithInvoice.Destination,
                        depositWithInvoice.ExternalAtAddress,
                        totalPaid,
                        now);
                        buffer.Send(invoicePaidMessage);
                    }
                    _ = DepositInprocessAsync(now, depositWithInvoice);
                }
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming invoice {invoiceId}. It may not be being watched by the sentinel.", null);
            }
        }


        private async Task DepositInprocessAsync(DateTime now, Deposit draftConfirmDeposit)
        {
            if (now == DateTime.MinValue) throw new ArgumentException("The provided date is not valid.", nameof(now));
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit), "Unconfirmed deposit cannot be null.");

            try
            {
                if (!TownSettings.TryToGetActorUrl("LIQUIDITY", out string hostnameIp)) throw new ArgumentException("Hostname or IP address is not configured.", nameof(hostnameIp));

                var client = new HttpClient();
                using var request = new HttpRequestMessage(HttpMethod.Post, $"{hostnameIp}/api/liquidity/{draftConfirmDeposit.Kind}/deposit/{draftConfirmDeposit.Id}/inprocess");

                HttpResponseMessage response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    Loggers.GetIntance().Sentinel.Debug($"Confirmed deposit {draftConfirmDeposit.Id} at {now} successfully notified.");
                }
                else
                {
                    Loggers.GetIntance().Sentinel.Debug($"Failed to notify confirmed deposit {draftConfirmDeposit.Id} at {now}. Status code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming deposit {draftConfirmDeposit.Id} at {now}: {ex.Message}", ex);
            }
        }

        internal static IngressSentinel SentinelByKind(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (!Enum.TryParse(kind, out Currencies.CODES currencyCode)) throw new ArgumentException($"Invalid currency code: {kind}", nameof(kind));

            IngressSentinel result = null;
            switch (currencyCode)
            {
                case Currencies.CODES.BTC:
                    result = new BTCIngressSentinel();
                    break;
                case Currencies.CODES.ETH:
                    result = new ETHIngressSentinel();
                    break;
                default:
                    throw new ArgumentException($"Unsupported currency code: {kind}", nameof(kind));
            }
            return result;
        }


        internal class AccountAmount
        {
            public string Account { get; private set; }
            public decimal Amount { get; private set; }
            public AccountAmount(string account, decimal amount)
            {
                if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account), "Account cannot be null or empty.");
                if (amount < 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount cannot be negative.");
                Account = account;
                Amount = amount;
            }
            public void AppendAmount(decimal amount)
            {
                if (amount < 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount cannot be negative.");
                Amount += amount;
            }
        }

        protected class LRUAccounts
        {
            internal static int MAX_ACCOUNTS = 20;

            private List<AccountAmount> lastAccounts { get; } = new List<AccountAmount>();

            internal LRUAccounts() { }

            internal IEnumerable<AccountAmount> LastAccounts => lastAccounts;

            internal int Count => lastAccounts.Count;

            internal void AddOrUpdate(string account, decimal amount)
            {
                if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account), "Account cannot be null or empty.");

                AccountAmount lruAccount = null;
                foreach (var acc in lastAccounts)
                {
                    if (acc.Account == account)
                    {
                        lruAccount = acc;
                        break;
                    }
                }

                if (lruAccount == null)
                {
                    lastAccounts.Insert(0, new AccountAmount(account, amount));
                    if (lastAccounts.Count == MAX_ACCOUNTS) lastAccounts.Remove(lastAccounts.Last());
                }
                else
                {
                    lruAccount.AppendAmount(amount);
                    lastAccounts.Remove(lruAccount);
                    lastAccounts.Insert(0, lruAccount);
                }
            }

            internal void Clear()
            {
                lastAccounts.Clear();
            }
        }
    }
}
