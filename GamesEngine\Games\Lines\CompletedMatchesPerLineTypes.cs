﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedMatchesPerLineTypes: Objeto, IEnumerable<CompletedMatchPerLineType>
    {
        readonly List<CompletedMatchPerLineType> matchPerPlayers = new List<CompletedMatchPerLineType>();

        internal IEnumerable<CompletedMatchPerLineType> GetAll => matchPerPlayers;

        internal bool Exists(int gameId, LineType lineType, int domainId)
        {
            if (gameId < 0) throw new GameEngineException($"{nameof(gameId)} must be greater than zero");
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater than zero");

            var result = matchPerPlayers.Exists(match => match.GameId == gameId && match.LineType == lineType && match.DomainId == domainId);
            return result;
        }

        internal CompletedMatchPerLineType Find(int gameId, LineType lineType, int domainId)
        {
            if (gameId < 0) throw new GameEngineException($"{nameof(gameId)} must be greater than zero");
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater than zero");

            var result = matchPerPlayers.Find(match => match.GameId == gameId && match.LineType == lineType && match.DomainId == domainId);
            if (result == null) throw new GameEngineException($"No match with {nameof(lineType)} '{lineType}' and {nameof(domainId)} '{domainId}'");
            return result;
        }

        internal void Add(CompletedMatchPerLineType match)
        {
            if (match == null) throw new ArgumentNullException(nameof(match));
            if (Exists(match.GameId, match.LineType, match.DomainId)) throw new GameEngineException($"Match was already added for this {nameof(match.LineType)} '{match.LineType}'");

            matchPerPlayers.Add(match);
        }

        internal void Add(IEnumerable<CompletedMatchPerLineType> pendingMatches)
        {
            if (!pendingMatches.Any()) throw new GameEngineException($"{nameof(pendingMatches)} cannot be empty");

            foreach (var match in pendingMatches)
            {
                Add(match);
            }
        }

        internal void UpdateTotals(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);

            if (wager.IsWinner()) TotalWinners++;
            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
        }

        HashSet<string> accounts = new HashSet<string>();

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWinners { get; private set; }

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }

        public IEnumerator<CompletedMatchPerLineType> GetEnumerator()
        {
            return matchPerPlayers.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return ((IEnumerable)matchPerPlayers).GetEnumerator();
        }
    }
}
