﻿using Connectors.town.connectors.commons;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.fiero.Grade;
using BalancesResponse = GamesEngine.Finance.BalancesResponse;
using LockBalanceResponse = GamesEngine.Finance.LockBalanceResponse;

namespace CashierAPI.Controllers
{
    public class ApiController : AuthorizeController
	{

		internal async Task<CurrenciesPrototype> AllCurrenciesAsync()
		{
            var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
            {{
                for (currencies:company.System.Coins)
                {{
                    currency = currencies;
					print currency.Iso4217Code currency;
                }}
            }}");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();

            var allCoins = JsonConvert.DeserializeObject<CurrenciesPrototype>(json);
			return allCoins;
        }
		

        const int DaysToBecomeUseless = 7;
        [HttpGet("api/customers/{atAddress}/balances")]
		[Authorize(Roles = "player,a1")]
		public async Task<IActionResult> CustomerBalancesAsync(string atAddress, [FromHeader(Name = "domain-url")] string domain)
		{
			if (string.IsNullOrEmpty(atAddress)) return NotFound("atAddress is required");
			if (string.IsNullOrEmpty(domain)) return NotFound("domain is required");

            Agents agent = Security.Agent(User);

            //Get All Currencies from actor
            var currencies = await AllCurrenciesAsync();
			List<string> currenciesList = currencies.currencies.Select(x => x.currency).ToList();

            BalancesResponse response = await PaymentChannels.CustomerBalancesAsync(domain, atAddress, (int)agent, currenciesList);
            return Ok(response);
		}

		[HttpGet("api/customers/{atAddress}/lottoBalances")]
		[Authorize(Roles = "player,a1")]
		public async Task<IActionResult> AvailableCustomerBalancesAsync(string atAddress, string standardCurrency, string rewardCurrency, [FromHeader(Name = "domain-url")] string domain)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest("atAddress is required");
			if (string.IsNullOrWhiteSpace(standardCurrency)) return BadRequest("standardCurrency is required");
			if (string.IsNullOrWhiteSpace(rewardCurrency)) return BadRequest("rewardCurrency is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest("domain is required");

            Agents agent = Security.Agent(User);
			BalancesResponse response = await PaymentChannels.CustomerBalancesAsync(domain, atAddress, (int)agent, new string[] { standardCurrency, rewardCurrency });

			return Ok(response);
		}

		[HttpGet("api/customers/{atAddress}/balances/FP")]
		[Authorize(Roles = "mfp1,a15")]
		public async Task<IActionResult> CustomerFreePlayBalanceAsync(string atAddress)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					exists = atAddress.CheckIfExistsAccount('FP');
					if (exists)
					{{
						balance = atAddress.GetBalance('FP');
						print balance.Available balance;
					}}
					else
					{{
						print 0.0 balance;
					}}
				}}
			");

			return result;
		}

		private const string ALL_CURRENCIES = "all";
		[HttpGet("api/customers/{atAddress}/balances/{strCurrencyCode}/accounts")]
		[Authorize(Roles = "player,a12")]
		public async Task<IActionResult> CustomerBalancesWithAccountsAsync(string atAddress, string strCurrencyCode, [FromHeader(Name = "domain-url")] string domain)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(strCurrencyCode)) return BadRequest($"{nameof(strCurrencyCode)} is required");
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            Agents agent = Security.Agent(User);

            //Coin[] currencyCodes;
            List<string> currencyCodes = new List<string>();
            if (strCurrencyCode == ALL_CURRENCIES)
			{
                foreach (var coin in Coinage.All)
                {
                    currencyCodes.Add(coin.Iso4217Code);
                }
            }
			else
			{
				var coin = Coinage.Coin(strCurrencyCode);
				currencyCodes.Add(coin.Iso4217Code);
            }


            BalancesResponse balancesResponse = await PaymentChannels.CustomerBalancesAsync(domain, atAddress, (int)agent, currencyCodes, true);
			return Ok(balancesResponse);
		}

		[HttpGet("api/processors/balances")]
		[Authorize(Roles = "player,a12")]
		public async Task<IActionResult> ProcessorsBalancesAsync([FromHeader(Name = "domain-url")] string domain)
		{
            if (String.IsNullOrWhiteSpace(HttpContext.Request.Headers["domain-url"])) return NotFound("Header 'domain-url' is not sent in request");

            var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.CurrencyIso4217Code currencyCode;
						account = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
						print account.Number accountNumber;
					}}
				}}
				");
			if (!(result is OkObjectResult)) throw new GameEngineException($@"Error:{((ObjectResult)result).Value}");
			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var processorsResponse = JsonConvert.DeserializeObject<ProcessorsResponse>(json);

            Agents agent = Security.Agent(User);
            IEnumerable<(string, string)> driversAccountAndCurrency = processorsResponse.Processors.Select(x => (x.AccountNumber, x.CurrencyCode));
            BalancesResponse balancesResponse = await PaymentChannels.CustomerBalancesAsync(domain, (int)agent, driversAccountAndCurrency);
            return Ok(balancesResponse);
		}

        async Task<int> NextAuthorizationNumberAsync()
        {
            return await Movements.StorageAsync.NextValueAuthorizationNumberAsync();
        }

        [HttpPost("api/customers/{atAddress}/balance/lock")]
		[Authorize(Roles = "player,c9,c13,dA2")]
		public async Task<IActionResult> LockAsync(string atAddress, [FromBody] LockBalanceData body)
		{
            if (body == null) return BadRequest("Body is required.");
            if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
            if (body.PurchaseTotal <= 0) return BadRequest($"{nameof(body.PurchaseTotal)} is required");
            if (body.StoreId <= 0) return BadRequest($"{nameof(body.StoreId)} is required");

            Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.PurchaseTotal);	

            try
            {
                var hasAccountNumber = !string.IsNullOrWhiteSpace(body.AccountNumber);
                string command = hasAccountNumber ? $@"
				{{
					print atAddress.HasEnoughFor('{body.AccountNumber}', Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
				}}" :
                $@"
				{{
					print atAddress.AnyAccountHasEnoughFor(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
				}}";
                var result = await CashierAPI.Cashier.PerformQryAsync(atAddress, HttpContext, command);
                if (!(result is OkObjectResult)) return BadRequest($"Failed to check if atAddress: {atAddress} has enough funds for purchaseTotal: {purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}");

                string jsonResult = (result as ObjectResult).Value.ToString();
                HasEnoughResponse response = JsonConvert.DeserializeObject<HasEnoughResponse>(jsonResult);

                string fragmentCommand = "";
                if (body.FragmentInformation != null && body.FragmentInformation.ItsConfigured)
                    fragmentCommand = $"authorization.CreateFragments(1, 1, {{'{body.Reference}'}}, {{'{body.Concept}'}}, {purchaseTotal.Value}, {body.FragmentInformation.Towin});";

                if (response.HasEnough)
                {
                    var authorization = await NextAuthorizationNumberAsync();
                    if (authorization != FAKE_TICKET_NUMBER && authorization > 0)
                    {
                        var normalizedUseless = body.Useless.ToString("MM/dd/yyyy HH:mm:ss");
                        command = hasAccountNumber ? $@"
						{{
							store = company.Sales.StoreById({body.StoreId});
							authorization = atAddress.CreateAuthorization(itIsThePresent,Now,Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}), {authorization}, store, '{body.Concept}', '{body.Reference}', '{body.AccountNumber}', {normalizedUseless}, {body.ProcessorId}, '{body.Who}');
							{fragmentCommand}
						}}" : $@"
						{{
							balance = atAddress.CreateBalanceIfNotExists('{purchaseTotal.CurrencyCode}');
							//Eval('available = '+balance.Available+';');
                            available = balance.Available;
							balance.SetInitialBalance(itIsThePresent, available);
							store = company.Sales.StoreById({body.StoreId});
							account = atAddress.SearchAccountWithBalanceGreaterThan(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}));
							authorization = atAddress.CreateAuthorization(itIsThePresent,Now,Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}), {authorization}, store, '{body.Concept}', '{body.Reference}', account, {normalizedUseless}, {body.ProcessorId}, '{body.Who}');
							{fragmentCommand}
						}}";
                        result = await CashierAPI.Cashier.PerformCmdAsync(atAddress, HttpContext, command);
                        if (!(result is OkObjectResult)) return result;

                        return Ok(authorization);
                    }
                    else
                    {
                        return BadRequest($"Failed to get a valid authorization number. Authorization number: {authorization}");
                    }
                }
                else
                {
                    return UnprocessableEntity($"Insufficient funds for atAddress: {atAddress}, purchaseTotal: {purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e, $"atAddress:{atAddress}", $"purchaseTotal:{purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}", $"storeSequence:{body.StoreId.ToString()}", $"concept:{body.Concept}", $"reference:{body.Reference}");
                return BadRequest($"Error processing the request: {e.Message}");
            }
        }

        class HasEnoughResponse
        {
            public bool HasEnough { get; set; }
        }

		private static int FAKE_TICKET_NUMBER = town.connectors.drivers.fiero.Authorization.FAKE_TICKET_NUMBER;

        [HttpPost("api/customers/{atAddress}/balance/externalMultiLock")]
		[Authorize(Roles = "player,dA2")]
		public async Task<IActionResult> ExternalMultiLockAsync(string atAddress, [FromBody] ExternalMultiLockBalanceData body)
		{
			if (body == null) return BadRequest("Body is required.");
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
            if (body.PurchaseTotal <= 0) return BadRequest($"{nameof(body.PurchaseTotal)} is required");
			if (body.StoreId <= 0) return BadRequest($"{nameof(body.StoreId)} is required");
			if (body.ToWinsByDrawAndNumber == null) return BadRequest($"{nameof(body.ToWinsByDrawAndNumber)} is required");
			if (!body.ToWinsByDrawAndNumber.Any()) return BadRequest($"{nameof(body.ToWinsByDrawAndNumber)} is required");

            Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.PurchaseTotal);
			
			string who = body.Who ?? "N/A";
            int authorization = FAKE_TICKET_NUMBER;
            try
            {
                var hasAccountNumber = !string.IsNullOrWhiteSpace(body.AccountNumber);
                string command = hasAccountNumber ? $@"
				{{
					print atAddress.HasEnoughFor('{body.AccountNumber}', Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
				}}
                " :
                $@"
				{{
					print atAddress.AnyAccountHasEnoughFor(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value})) hasEnough;
				}}";
                var result = await CashierAPI.Cashier.PerformQryAsync(atAddress, HttpContext, command);
                if (!(result is OkObjectResult)) return BadRequest($"Failed to check if atAddress: {atAddress} has enough funds for purchaseTotal: {purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}");

                string jsonResult = (result as ObjectResult).Value.ToString();
                HasEnoughResponse response = JsonConvert.DeserializeObject<HasEnoughResponse>(jsonResult);

                if (response.HasEnough)
                {
                    StringBuilder authorizationScript = new StringBuilder();
                    StringBuilder commandForAuthorizations = new StringBuilder();
                    StringBuilder risks = new StringBuilder();
                    bool allRisksAreEquals = true;
                    var lastIndex = body.ToWinsByDrawAndNumber.Count() - 1;
                    var firstRisk = body.ToWinsByDrawAndNumber.ElementAt(0).risk;

					if (body.UseLocalAuthorization)
					{
						foreach (var toWinByDrawAndNumber in body.ToWinsByDrawAndNumber)
						{
							int nextAuthorizationNumber = Movements.Storage.NextAuthorizationNumber();
							toWinByDrawAndNumber.ticketId = nextAuthorizationNumber.ToString();
						}
					}
                    AuthorizationsNumbers.BuildCommand(body.ToWinsByDrawAndNumber.Select(x => x.ticketId), ref commandForAuthorizations);
                    //AuthorizationsNumbers.BuildCommand(body.ToWinsByDrawAndNumber.Select(x => nextAuthorizationNumber.ToString()), ref commandForAuthorizations);

                    foreach (var toWinByDrawAndNumber in body.ToWinsByDrawAndNumber)
                    {
						if (!int.TryParse(toWinByDrawAndNumber.ticketId, out authorization))
						{
                            return BadRequest($"Invalid ticketId: {toWinByDrawAndNumber.ticketId} in ToWinsByDrawAndNumber. It must be a valid integer.");
                        }

                        if (firstRisk != toWinByDrawAndNumber.risk) allRisksAreEquals = false;

                        risks.Append(toWinByDrawAndNumber.risk);
                        if (toWinByDrawAndNumber != body.ToWinsByDrawAndNumber.ElementAt(lastIndex))
                        {
                            risks.Append("','");
                        }
                    }
                    if (authorization != FAKE_TICKET_NUMBER && authorization > 0)
                    {
                        var normalizedUseless = body.Useless.ToString("MM/dd/yyyy HH:mm:ss");
                        if (hasAccountNumber)
                        {
                            if (allRisksAreEquals)
                            {
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,Currency('").Append(purchaseTotal.CurrencyCode).Append("', ").Append(firstRisk).Append("), auths.Numbers, store, '").Append(body.Concept).Append("', '").Append(body.Reference).Append("', '").Append(body.AccountNumber).Append("', ").Append(normalizedUseless).Append(", ").Append(body.ProcessorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                            else
                            {
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,'").Append(purchaseTotal.CurrencyCode).Append("', {'").Append(risks).Append("'}, auths.Numbers, store, '").Append(body.Concept).Append("', '").Append(body.Reference).Append("', '").Append(body.AccountNumber).Append("', ").Append(normalizedUseless).Append(", ").Append(body.ProcessorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                        }
                        else
                        {
                            if (allRisksAreEquals)
                            {
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,Currency('").Append(purchaseTotal.CurrencyCode).Append("', ").Append(firstRisk).Append("), auths.Numbers, store, '").Append(body.Concept).Append("', '").Append(body.Reference).Append("', account, ").Append(normalizedUseless).Append(", ").Append(body.ProcessorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                            else
                            {
                                authorizationScript.Append("atAddress.CreateAuthorization(itIsThePresent,Now,'").Append(purchaseTotal.CurrencyCode).Append("', {'").Append(risks).Append("'}, auths.Numbers, store, '").Append(body.Concept).Append("', '").Append(body.Reference).Append("', account, ").Append(normalizedUseless).Append(", ").Append(body.ProcessorId).Append(", '").Append(who).Append("');").AppendLine();
                            }
                        }
                    }
                    else
                    {
                        return BadRequest($"Failed to get a valid authorization number. Authorization number: {authorization}");
                    }

                    command = hasAccountNumber ? $@"
						{{
							store = company.Sales.StoreById({body.StoreId});
							{commandForAuthorizations}
							{authorizationScript}
						}}" : $@"
						{{
							balance = atAddress.CreateBalanceIfNotExists('{purchaseTotal.CurrencyCode}');
							Eval('available = '+balance.Available+';');
							balance.SetInitialBalance(itIsThePresent, available);
							store = company.Sales.StoreById({body.StoreId});
							account = atAddress.SearchAccountWithBalanceGreaterThan(Currency('{purchaseTotal.CurrencyCode}',{purchaseTotal.Value}));
                            {commandForAuthorizations}
							{authorizationScript}
						}}";
                    result = await CashierAPI.Cashier.PerformCmdAsync(atAddress, HttpContext, command);
                    if (!(result is OkObjectResult)) return BadRequest($"Failed to lock balance for atAddress: {atAddress} with purchaseTotal: {purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}");

                    //AuthorizationNumber = nextAuthorizationNumber
                    int authorizationNumber = int.Parse(body.ToWinsByDrawAndNumber.First().ticketId);
                    return Ok(authorizationNumber);
                }
                else
                {
                    return UnprocessableEntity($"Insufficient funds for atAddress: {atAddress}, purchaseTotal: {purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e, $"atAddress:{atAddress}", $"purchaseTotal:{purchaseTotal.CurrencyCode.ToString()}{purchaseTotal.Value}", $"storeSequence:{body.StoreId.ToString()}", $"concept:{body.Concept}", $"reference:{body.Reference}");
                return BadRequest($"Error processing the request: {e.Message}");
            }
		}

        [HttpGet("api/customers/{atAddress}/{accountNumber}/existence")]
		[Authorize(Roles = "a30")]
		public async Task<IActionResult> ExistsAtAddressAsync(string atAddress, string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is invalid");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					existsBalance = atAddress.CheckIfExistsAccount('{accountNumber}');
					if (existsBalance)
					{{
						account = atAddress.SearchAccountByNumber('{accountNumber}');
						balance = account.Balance;
						print balance.Locked locked;
						print balance.Available balance;
						print balance.LockedAmount.ToDisplayFormat() lockedFormatted;
						print balance.AvailableAmount.ToDisplayFormat() balanceFormatted;
						print true foundAtAddress;
					}}
					else
					{{
						print false foundAtAddress;
					}}
				}}
			");

			return result;
		}

		[HttpGet("api/customers/{atAddress}/sources")]
		[Authorize(Roles = "a4")]
		public async Task<IActionResult> CustomerSourcesAsync(string atAddress)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					for(source : atAddress.ListSources())
					{{
						print source.Number id;
						print source.CurrencyAsText currency;
						print source.CurrencyTypeAsText type;
					}}
				}}
			");

			return result;
		}

		[HttpGet("api/sources")]
		[Authorize(Roles = "g15,devops,a5,FinancialReport")]
		public async Task<IActionResult> SourcesAsync()
		{
			return await CashierAPI.Cashier.PerformQryAsync("general", HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					
					for(source : balancesList.ValidCurrencies())
					{{
						print source.Number id;
						print source.Name name;
						print source.CurrencyAsText currency;
						print source.CurrencyTypeAsText type;
					}}
				}}
			");
		}

		[HttpGet("api/currencies")]
		public async Task<IActionResult> CurrenciesAsync()
		{
			var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
            {{
                for (currencies:company.System.EnabledCoins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currency;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                }}
            }}
            ");
			return result;
		}

		[HttpGet("api/movements/users")]
		[Authorize(Roles = "a17")]
		public async Task<IActionResult> usersAsync(int storeId)
		{
			var result = await CashierAPI.Cashier.PerformQryAsync("general", HttpContext, $@"
				{{
				    store = company.Sales.StoreById({storeId});
					for(users:store.Users())
					{{
						print users.Id id;
						print users.Name name;
					}}
				}}
			");

			return result;
		}

        [HttpPost("api/withdrawal")]
        [Authorize(Roles = "dA5,dA7")]
        public async Task<IActionResult> WithdrawalAsync([FromBody] TransactionBody transactionMessageBody)
        {
            if (transactionMessageBody == null) return BadRequest($"{nameof(transactionMessageBody)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.AtAddress)) return BadRequest($"{nameof(transactionMessageBody.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Currency)) return BadRequest($"{nameof(transactionMessageBody.Currency)} is required");
            if (transactionMessageBody.StoreId <= 0) return BadRequest($"{nameof(transactionMessageBody.StoreId)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Domain)) return BadRequest($"{nameof(transactionMessageBody.Domain)} is required");
            if (transactionMessageBody.Amount <= 0) return BadRequest($"{nameof(transactionMessageBody.Amount)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Description)) return BadRequest($"{nameof(transactionMessageBody.Description)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Reference)) return BadRequest($"{nameof(transactionMessageBody.Reference)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.AccountNumber)) return BadRequest($"{nameof(transactionMessageBody.AccountNumber)} is required");
            if (transactionMessageBody.ProcessorId <= 0) return BadRequest($"{nameof(transactionMessageBody.ProcessorId)} is required");
            if (transactionMessageBody.Agent < 0) return BadRequest($"{nameof(transactionMessageBody.Agent)} is required");

            var description = Validator.StringEscape(transactionMessageBody.Description);
            Currency amount = Currency.Factory(transactionMessageBody.Currency, transactionMessageBody.Amount);
            string command = "";
            int authorization = Movements.Storage.NextAuthorizationNumber();

            if (transactionMessageBody.HasSource())
            {
                if (string.IsNullOrWhiteSpace(transactionMessageBody.SourceName)) return BadRequest($"{nameof(transactionMessageBody.SourceName)} is required");
                command = $@"
				{{
					source{transactionMessageBody.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {transactionMessageBody.SourceNumber}, '{transactionMessageBody.Currency}', '{transactionMessageBody.SourceName}');
					balance = atAddress.CreateBalanceIfNotExists('{transactionMessageBody.Currency}');
					//Eval('available = '+balance.Available+';');
                    available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId});
					source{transactionMessageBody.SourceNumber}.Withdraw(itIsThePresent, Now, Currency('{transactionMessageBody.Currency}',{transactionMessageBody.Amount}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}','{transactionMessageBody.AccountNumber}', {transactionMessageBody.ProcessorId});
				}}";
            }
            else if (!string.IsNullOrEmpty(transactionMessageBody.AccountNumber))
            {
                command = $@"
				{{
					balance = atAddress.CreateAccountIfNotExists('{transactionMessageBody.Currency}', '{transactionMessageBody.AccountNumber}').Balance;
					//Eval('available = '+balance.Available+';');
                    available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId});
					balance.Withdraw(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}', {transactionMessageBody.ProcessorId});
				}}";
            }
            else
            {
                command = $@"
				{{
					balance = atAddress.CreateBalanceIfNotExists('{transactionMessageBody.Currency}');
					//Eval('available = '+balance.Available+';');
                    available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId});
					balance.Withdraw(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}', {transactionMessageBody.ProcessorId});
				}}";
            }

            var result = await CashierAPI.Cashier.PerformCmdAsync(transactionMessageBody.AtAddress, HttpContext, command);
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($"Withdrawal was not saved because command fails on cashier.");
            }

            return Ok(new TransactionResponse
            {
                Authorization = authorization
            });
        }

        [HttpPost("api/debitNote")]
		[Authorize(Roles = "dA5")]
        public async Task<IActionResult> WithdrawalDebitNoteAsync([FromBody] TransactionBody transactionMessageBody)
        {
            return await WithdrawalAsync(transactionMessageBody);
        }

        [HttpPost("api/deposit")]
		[Authorize(Roles = "dA6,dA8")]
		public async Task<IActionResult> DepositAsync([FromBody] TransactionBody transactionMessageBody)
        {
            if (transactionMessageBody == null) return BadRequest($"{nameof(transactionMessageBody)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.AtAddress)) return BadRequest($"{nameof(transactionMessageBody.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Currency)) return BadRequest($"{nameof(transactionMessageBody.Currency)} is required");
            if (transactionMessageBody.StoreId <= 0) return BadRequest($"{nameof(transactionMessageBody.StoreId)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Domain)) return BadRequest($"{nameof(transactionMessageBody.Domain)} is required");
            if (transactionMessageBody.Amount <= 0) return BadRequest($"{nameof(transactionMessageBody.Amount)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Description)) return BadRequest($"{nameof(transactionMessageBody.Description)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.Reference)) return BadRequest($"{nameof(transactionMessageBody.Reference)} is required");
            if (string.IsNullOrWhiteSpace(transactionMessageBody.AccountNumber)) return BadRequest($"{nameof(transactionMessageBody.AccountNumber)} is required");
            if (transactionMessageBody.ProcessorId <=0 ) return BadRequest($"{nameof(transactionMessageBody.ProcessorId)} is required");
            if (transactionMessageBody.Agent < 0) return BadRequest($"{nameof(transactionMessageBody.Agent)} is required");

            Integration.MarkFragmentsTableAsCreated(transactionMessageBody.AtAddress);
            Integration.MarkMovementsTableAsCreated(transactionMessageBody.Currency);

            Coin coin = Coinage.Coin(transactionMessageBody.Currency);
            Integration.MarkMovementsTableAsCreated(transactionMessageBody.AtAddress, coin);            

            var description = Validator.StringEscape(transactionMessageBody.Description);
            Currency amount = Currency.Factory(transactionMessageBody.Currency, transactionMessageBody.Amount);
            string command = "";
            int authorization = Movements.Storage.NextAuthorizationNumber();

            var normalizedUseless = transactionMessageBody.Now.AddDays(DaysToBecomeUseless).ToString("MM/dd/yyyy HH:mm:ss");
            StringBuilder lockCommand = new StringBuilder();
            if (transactionMessageBody.WithLock)
            {
                if (!string.IsNullOrEmpty(transactionMessageBody.AccountNumber) || transactionMessageBody.HasSource())
                {
                    lockCommand.AppendLine($"authorization = atAddress.CreateAuthorization(itIsThePresent,Now, Currency('{amount.CurrencyCode}', {amount.Value}), {authorization}, store, '', '', '{transactionMessageBody.AccountNumber}', {normalizedUseless}, {transactionMessageBody.ProcessorId});");
                }
                else
                {
                    lockCommand.AppendLine($"authorization = atAddress.CreateAuthorization(itIsThePresent,Now, Currency('{amount.CurrencyCode}', {amount.Value}), {authorization}, store, '', '', '{amount.CurrencyCode}', {normalizedUseless}, {transactionMessageBody.ProcessorId});");
                }
            }
            else
            {
                lockCommand.AppendLine($"authorization = atAddress.CreateFakeAuthorizationWithoutLock('{amount.CurrencyCode}', {authorization}, '{transactionMessageBody.AccountNumber}', {normalizedUseless});");
                lockCommand.AppendLine($"authorization.CreateFakeFragment('{transactionMessageBody.Reference}', '{description}', {amount.Value});");
                lockCommand.AppendLine($"atAddress.PrepareFakePayment(authorization, Now, store, {FragmentReason.Winner}).ApplyChanges(itIsThePresent, '{transactionMessageBody.Who}');");
            }

            if (transactionMessageBody.HasSource())
            {
                if (string.IsNullOrWhiteSpace(transactionMessageBody.SourceName)) return BadRequest($"{nameof(transactionMessageBody.SourceName)} is required");

                command = $@"
				{{
					source{transactionMessageBody.SourceNumber} = atAddress.GetOrCreateSource(itIsThePresent, now, {transactionMessageBody.SourceNumber}, '{transactionMessageBody.Currency}', '{transactionMessageBody.SourceName}');
	                balance = atAddress.CreateAccountIfNotExists('{transactionMessageBody.Currency}', '{transactionMessageBody.AccountNumber}').Balance;
					//Eval('available = '+balance.Available+';');//Rubicon: Eval bug with BTC decimal a la potencia de (x-10). afecta al follower
					available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId.ToString()});
					source{transactionMessageBody.SourceNumber}.Accredit(itIsThePresent, Now, Currency('{transactionMessageBody.Currency}',{transactionMessageBody.Amount}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}', '{transactionMessageBody.AccountNumber}', {transactionMessageBody.ProcessorId});
					{lockCommand}
				}}";
            }
            else if (!string.IsNullOrEmpty(transactionMessageBody.AccountNumber))
            {
                command = $@"
				{{
					balance = atAddress.CreateAccountIfNotExists('{transactionMessageBody.Currency}', '{transactionMessageBody.AccountNumber}').Balance;
					//Eval('available = '+balance.Available+';');//Rubicon: Eval bug with BTC decimal a la potencia de (x-10). afecta al follower
					available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId});
					balance.Accredit(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}', {transactionMessageBody.ProcessorId});
					{lockCommand}
				}}";
            }
            else
            {
                command = $@"
				{{
					balance = atAddress.CreateBalanceIfNotExists('{transactionMessageBody.Currency}');
					//Eval('available = '+balance.Available+';');//Rubicon: Eval bug with BTC decimal a la potencia de (x-10). afecta al follower
					available = balance.Available;
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({transactionMessageBody.StoreId});
					balance.Accredit(itIsThePresent, Now, Currency('{amount.CurrencyCode}',{amount.Value}), '{transactionMessageBody.Who}', '{authorization}', store, '{description}', '{transactionMessageBody.Reference}', {transactionMessageBody.ProcessorId});
					{lockCommand}
				}}";
            }

            var result = await CashierAPI.Cashier.PerformCmdAsync(transactionMessageBody.AtAddress, HttpContext, command);
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($"Deposit was not saved because command fails on cashier.");
            }

            return Ok(new TransactionResponse
            {
                Authorization = authorization
            });
        }

        [HttpPost("api/creditNote")]
		[Authorize(Roles = "dA6")]
		public async Task<IActionResult> DepositCreditNoteAsync([FromBody] TransactionBody transactionMessageBody)
        {
            return await DepositAsync(transactionMessageBody);
        }

        [DataContract(Name = "RefundBody")]
		public class RefundBody
		{
			[DataMember(Name = "storeId")]
			public int StoreId { get; set; }
			[DataMember(Name = "domainUrl")]
			public string DomainUrl { get; set; }
			[DataMember(Name = "who")]
			public string Who { get; set; }
			[DataMember(Name = "concept")]
			public string Concept { get; set; }
			[DataMember(Name = "payFragments")]
			public List<FragmentPaymentBody> FragmentPayments { get; set; }
            [DataMember(Name = "account")]
            public string Account { get; internal set; }
            [DataMember(Name = "processorId")]
            public int ProcessorId { get; internal set; }

            private List<Coin> currencies;
			public IEnumerable<Coin> Currencies()
			{
				if (currencies == null)
				{
					currencies = new List<Coin>();
					foreach (var fragmentPayment in FragmentPayments)
					{
						if (!currencies.Any(x => x.Iso4217Code == fragmentPayment.Currency))
						{
							currencies.Add(Coinage.Coin(fragmentPayment.Currency));
						}
					}
				}
				return currencies;
			}

			private List<FragmentPaymentBody> payFragmentsMessages;
			public List<FragmentPaymentBody> MessagesBy(Coin currencyCode)
			{
				if (payFragmentsMessages == null) payFragmentsMessages = new List<FragmentPaymentBody>();
				payFragmentsMessages.Clear();

				foreach (var fragmentPayment in FragmentPayments)
				{
					if (fragmentPayment.Currency == currencyCode.Iso4217Code)
					{
						payFragmentsMessages.Add(fragmentPayment);
					}
				}

				return payFragmentsMessages;
			}

			public string GetAtAddressForThisAuthorizationNumber(int authorizationNumber)
			{
				foreach (var fragmentPayment in FragmentPayments)
				{
					if (fragmentPayment.AuthorizationNumber == authorizationNumber)
					{
						return fragmentPayment.AtAddress;
					}
				}
				return null;
			}
		}

		//PayFragmentsMessage
		[DataContract(Name = "FragmentPaymentBody")]
		public sealed class FragmentPaymentBody
		{
			[DataMember(Name = "atAddress")]
			public string AtAddress { get; set; }
			[DataMember(Name = "authorizationNumber")]
			public int AuthorizationNumber { get; set; }
			[DataMember(Name = "wagerNumber")]
			public int WagerNumber { get; set; }
			[DataMember(Name = "wagerStatus")]
			public WagerStatus Outcome { get; set; }
			[DataMember(Name = "fragmentPaymentDate")]
			public DateTime FragmentPaymentDate { get; set; }
			[DataMember(Name = "adjustedWinAmount")]
			public decimal AdjustedWinAmount { get; set; }
			[DataMember(Name = "adjustedLossAmount")]
			public decimal AdjustedLossAmount { get; set; }
			[DataMember(Name = "agentId")]
			public int agentId { get; set; }
			[DataMember(Name = "currency")]
			public string Currency { get; set; }
			[DataMember(Name = "isValidTicketNumber")]
			public bool IsValidTicketNumber { get; set; }
		}

        [HttpPost("api/deposit/unlock")]
        [Authorize(Roles = "dA8")]
        public async Task<IActionResult> CancelDepositLockedAsync([FromBody] TransactionMovementBody body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
            if (string.IsNullOrWhiteSpace(body.Currency)) return BadRequest($"{nameof(body.Currency)} is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} is required");
            if (body.Store <= 0) return BadRequest($"{nameof(body.Store)} is required");
            if (string.IsNullOrWhiteSpace(body.Who)) return BadRequest($"{nameof(body.Who)} is required");
            if (string.IsNullOrWhiteSpace(body.DocumentNumber)) return BadRequest($"{nameof(body.DocumentNumber)} is required");
            if (string.IsNullOrWhiteSpace(body.Concept)) return BadRequest($"{nameof(body.Concept)} is required");
            if (string.IsNullOrWhiteSpace(body.Reference)) return BadRequest($"{nameof(body.Reference)} is required");
            if (body.ProcessorId <= 0) return BadRequest($"{nameof(body.ProcessorId)} is required");

            var result = await CashierAPI.Cashier.PerformCmdAsync(body.AtAddress, HttpContext, $@"
            {{
                store = company.Sales.StoreById({body.Store});
                accountWithBalance = atAddress.CreateAccountIfNotExists('{body.Currency}', '{body.AccountNumber}');
                accountWithBalance.UnLock(itIsThePresent, now, Currency('{body.Currency}',{body.Amount}), '{body.Who}', '{body.DocumentNumber}', store, '{body.Concept}', '{body.Reference}', {body.ProcessorId});
            }}");

            return result;
        }

        [HttpPost("api/deposit/unlockAndDebit")]
        [Authorize(Roles = "dA8")]
        public async Task<IActionResult> UnlockAndDebitDepositAsync([FromBody] TransactionMovementBody body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
            if (string.IsNullOrWhiteSpace(body.Currency)) return BadRequest($"{nameof(body.Currency)} is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} is required");
            if (body.Store <= 0) return BadRequest($"{nameof(body.Store)} is required");
            if (string.IsNullOrWhiteSpace(body.Who)) return BadRequest($"{nameof(body.Who)} is required");
            if (string.IsNullOrWhiteSpace(body.DocumentNumber)) return BadRequest($"{nameof(body.DocumentNumber)} is required");
            if (string.IsNullOrWhiteSpace(body.Concept)) return BadRequest($"{nameof(body.Concept)} is required");
            if (string.IsNullOrWhiteSpace(body.Reference)) return BadRequest($"{nameof(body.Reference)} is required");
            if (body.ProcessorId <= 0) return BadRequest($"{nameof(body.ProcessorId)} is required");

            if (body.SourceNumber < 0) return BadRequest($"{nameof(body.SourceNumber)} no negative allow");
            if (string.IsNullOrWhiteSpace(body.SourceName)) return BadRequest($"{nameof(body.SourceName)} is required");

            var result = await CashierAPI.Cashier.PerformCmdAsync(body.AtAddress, HttpContext, $@"
            {{
                store = company.Sales.StoreById({body.Store});
                accountWithBalance = atAddress.CreateAccountIfNotExists('{body.Currency}', '{body.AccountNumber}');
                accountWithBalance.UnLock(itIsThePresent, now, Currency('{body.Currency}',{body.Amount}), '{body.Who}', '{body.DocumentNumber}', store, '{body.Concept}', '{body.Reference}', {body.ProcessorId});

                source = atAddress.GetOrCreateSource(itIsThePresent, now, {body.SourceNumber}, '{body.Currency}', '{body.SourceName}');
                accountWithBalance.Withdraw(itIsThePresent, now, Currency('{body.Currency}',{body.Amount}), source, '{body.Who}', '{body.DocumentNumber}', store, '{body.Concept}', '{body.Reference}', {body.ProcessorId});
            }}");

            return result;
        }

        const int AverageScriptLengthForOneCommand = 1000;
		const int MAX_TAMANO_DE_UN_LEXEMA = 1024 * 64;
		[HttpPost("api/refund")]
		[Authorize(Roles = "dA4")]
        public async Task<IActionResult> RefundAsync([FromBody] RefundBody body)
		{
			if(body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.Account)) return BadRequest($"{nameof(body.Account)} is required");
            if (string.IsNullOrEmpty(body.Concept)) return BadRequest($"{nameof(body.Account)} is invalid");
            if (string.IsNullOrWhiteSpace(body.DomainUrl)) return BadRequest($"{nameof(body.Account)} is invalid");
            if (string.IsNullOrEmpty(body.Who)) return BadRequest($"{nameof(body.Account)} is invalid");
            if (body.FragmentPayments == null) return BadRequest($"{nameof(body.FragmentPayments)} is invalid");
            if (body.FragmentPayments.Count == 0) return BadRequest($"{nameof(body.FragmentPayments)} is invalid");
            if (body.StoreId <= 0) return BadRequest($"{nameof(body.StoreId)} is invalid");
            if (body.ProcessorId <= 0) return BadRequest($"{nameof(body.ProcessorId)} is invalid");

			foreach (var fragment in body.FragmentPayments)
			{
				if (string.IsNullOrWhiteSpace(fragment.AtAddress)) return BadRequest($"{nameof(fragment.AtAddress)} is invalid");
                if (fragment.AuthorizationNumber < 0) return BadRequest($"{nameof(fragment.AuthorizationNumber)} is invalid");
                if (fragment.WagerNumber < 0) return BadRequest($"{nameof(fragment.WagerNumber)} is invalid");
                if (fragment.FragmentPaymentDate == DateTime.MinValue) return BadRequest($"{nameof(fragment.FragmentPaymentDate)} is invalid");
                if (fragment.agentId < 0) return BadRequest($"{nameof(fragment.agentId)} is invalid");
                if (string.IsNullOrWhiteSpace(fragment.Currency)) return BadRequest($"{nameof(fragment.Currency)} is invalid");
			}

            RefundFreeFormWagersResponse finalResponse = new RefundFreeFormWagersResponse();
			foreach (var currencyCode in body.Currencies())
			{
				string processorAccountInfo = body.Account;

				var fragments = body.MessagesBy(currencyCode);
				var sortedWagers = fragments.OrderBy(x => body.GetAtAddressForThisAuthorizationNumber(x.AuthorizationNumber)).ThenBy(x => x.AuthorizationNumber).ToArray();
				int wagerIndex = 0;

				var buffer = MovementsBuffers.GetOrCreateBuffer(true);
				var storeId = body.StoreId;
				int processorId = body.ProcessorId;

				StringBuilder addressScript = new StringBuilder();
				StringBuilder changesPerAuthorization = new StringBuilder();
				StringBuilder wagersWinners = new StringBuilder();
				StringBuilder wagersLosers = new StringBuilder();
				StringBuilder refunds = new StringBuilder();
				StringBuilder wagersAdjustments = new StringBuilder();
				StringBuilder initialFragmentStatus = new StringBuilder();
				while (wagerIndex < sortedWagers.Length)
				{
					string currAtAddress = body.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].AuthorizationNumber);
					if (buffer.RequiresToChangeTopic(currAtAddress))
					{
						if (!buffer.IsEmpty()) buffer.Flush();
						buffer.UpdateTopicName(currAtAddress);
					}

					while (wagerIndex < sortedWagers.Length && body.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].AuthorizationNumber) == currAtAddress)
					{
						int currTicketNumber = sortedWagers[wagerIndex].AuthorizationNumber;

						wagersWinners.Append('{');
						wagersLosers.Append('{');
						refunds.Append('{');

						int emptyLengthWagers = wagersWinners.Length;

						while (wagerIndex < sortedWagers.Length && sortedWagers[wagerIndex].AuthorizationNumber == currTicketNumber)
						{
							var wager = sortedWagers[wagerIndex];
							wagerIndex++;

							switch (wager.Outcome)
							{
								case WagerStatus.L:
									wagersLosers.Append(wager.WagerNumber);
									wagersLosers.Append(',');
									break;
								case WagerStatus.W:
									wagersWinners.Append(wager.WagerNumber);
									wagersWinners.Append(',');
									break;
								case WagerStatus.X:
									refunds.Append(wager.WagerNumber);
									refunds.Append(',');
									break;
								case WagerStatus.D:
									refunds.Append(wager.WagerNumber);
									refunds.Append(',');
									break;
								default:
									string DailyFigureDate_YYYYMMDD = wager.FragmentPaymentDate.ToString("yyyyMMdd");
									ErrorsSender.Send(
										$@"Wager with unknown Outcome:{wager.Outcome}",
										$@"TicketNumber:{wager.WagerNumber} \n
												WagerNumber:{wager.WagerNumber} \n
												DailyFigureDate_YYYYMMDD:{DailyFigureDate_YYYYMMDD} \n
												IsValidTicketNumber:{wager.IsValidTicketNumber} \n
												AdjustedLossAmount:{wager.AdjustedLossAmount} \n
												AdjustedWinAmount:{wager.AdjustedWinAmount}");
									break;
							}

							decimal AdjustedWinAmount = wager.AdjustedWinAmount;
							decimal AdjustedLossAmount = wager.AdjustedLossAmount;
							bool hasAdjustments = AdjustedWinAmount != 0 || AdjustedLossAmount != 0;
							if (hasAdjustments)
							{
								wagersAdjustments.Append(".PrepareAdjustments(authorization, ").Append(wager.WagerNumber).Append(',').Append(AdjustedWinAmount).Append(',').Append(AdjustedLossAmount).Append(')');
							}
						}

						if (wagersWinners.Length > 1) wagersWinners.Length = wagersWinners.Length - 1;
						if (wagersLosers.Length > 1) wagersLosers.Length = wagersLosers.Length - 1;
						if (refunds.Length > 1) refunds.Length = refunds.Length - 1;

						wagersWinners.Append('}');
						wagersLosers.Append('}');
						refunds.Append('}');

						if (wagersLosers.Length > 2)
						{
							initialFragmentStatus.Append("Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersLosers).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersLosers).Append(", initialStatus);");
							changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersLosers).Append(", Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.Loser).Append(',').Append(processorId).Append(')');
						}
						if (wagersWinners.Length > 2)
						{
							initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersWinners).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersWinners).Append(", initialStatus);");
							changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersWinners).Append(",  Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.Winner).Append(',').Append(processorId).Append(')');
						}
						if (refunds.Length > 2)
						{
							initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(refunds).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(refunds).Append(", initialStatus);");
							changesPerAuthorization.Append($".PrepareRefunds(itIsThePresent, authorization,").Append(refunds).Append(", Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.NoAction).Append(',').Append(processorId).Append(')');
						}
						if (wagersAdjustments.Length > 1) changesPerAuthorization.Append(wagersAdjustments.ToString());

						addressScript.Append("authorization = atAddress.GetAuthorization(").Append(currTicketNumber).Append(");")
							.Append(initialFragmentStatus)
							.Append("{ store = company.Sales.StoreById(").Append(storeId).Append(");")
							.Append("ataddress").Append(changesPerAuthorization).Append(".ApplyChanges(").Append(buffer.Id).Append(", itIsThePresent, '").Append(body.Who).Append("'); }");

						changesPerAuthorization.Clear();
						wagersWinners.Clear();
						wagersLosers.Clear();
						refunds.Clear();
						wagersAdjustments.Clear();
						initialFragmentStatus.Clear();

						if (addressScript.Length + AverageScriptLengthForOneCommand > MAX_TAMANO_DE_UN_LEXEMA)
						{
							_ = CashierAPI.Cashier.PerformCmd(currAtAddress, addressScript.ToString());
							addressScript.Clear();
						}
					}
					if (addressScript.Length > 0)
					{
						_ = CashierAPI.Cashier.PerformCmd(currAtAddress, addressScript.ToString());
						addressScript.Clear();
					}

					//TODO cris preguntarle a alvaro como hacer el print ya que se necesitan las authorizaciones.
					RefundFreeFormWagersResponse mock = new RefundFreeFormWagersResponse();
					finalResponse.Merge(mock);
				}

				MovementsBuffers.Flush(buffer);
			}
			
			var result = new FragmentsWithProblemsResponse();
            if (finalResponse.Wagers != null && finalResponse.Wagers.Length > 0)
			{
				foreach (var wager in finalResponse.Wagers)
				{
					if (!wager.IsValidTicketNumber)
					{
						result.Add(wager.TicketNumber, wager.WagerNumber);
                    }
				}
			}

            string jsonString = JsonConvert.SerializeObject(result);
			return Ok(jsonString);
        }

        private static readonly int MaxFragmentsPerGroup = 100;
        private static string EscapeSingleQuotes(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var stringBuilder = new StringBuilder(input.Length);
            foreach (char c in input)
            {
                if (c == '\'')
                {
                    stringBuilder.Append("\\'");
                }
                else
                {
                    stringBuilder.Append(c);
                }
            }

            return stringBuilder.ToString();
        }

        [HttpPost("api/fragments")]
        [Authorize(Roles = "dA3")]
        public async Task<IActionResult> CreateFragmentsAsync([FromBody] FragmentAuthorizationBody body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (body.Fragments == null) return BadRequest($"{nameof(body.Fragments)} is required");
            if (!body.Fragments.Any()) return BadRequest($"{nameof(body.Fragments)} is required");

            int initialWagerId = 1;
            var wagers = Array.ConvertAll(body.Fragments, new Converter<Fragment, PostFreeFormWager>(EntitiesConverter.FragmentToWager));
            var buildResponse = new PostFreeFormWagerCollectionSuccessResponse()
            {
                Wagers = wagers
            };

            foreach (PostFreeFormWager wager in buildResponse.Wagers)
            {
				if (string.IsNullOrWhiteSpace(wager.WagerNumber)) return BadRequest($"{nameof(wager.WagerNumber)} is required");
                wager.WagerNumber = initialWagerId.ToString();
                initialWagerId++;
            }

            var wagerNumbers = buildResponse.Wagers.Select(x => int.Parse(x.WagerNumber));
            var wagerRisk = buildResponse.Wagers.Select(x => decimal.Parse(x.Risk)).ToList();
            var wagerTowin = buildResponse.Wagers.Select(x => decimal.Parse(x.ToWin)).ToList();

            decimal fragmentsRisk = wagerRisk[0];
            decimal fragmentsTowin = wagerTowin[0];
            var fragmentsReferences = buildResponse.Wagers.Select(x => x.ReferenceNumber);
            var fragmentsDescription = buildResponse.Wagers.Select(x => x.BetDescription);
            var fragmentsToWins = buildResponse.Wagers.Select(x => decimal.Parse(x.ToWin).ToString("F"));
            var fragmentsToRisks = buildResponse.Wagers.Select(x => decimal.Parse(x.Risk).ToString("F"));
            bool hasSameToWinAmounts = wagerTowin.Count(x => x == fragmentsTowin) == wagerTowin.Count;
            bool hasSameRiskAmounts = wagerRisk.Count(x => x == fragmentsRisk) == wagerRisk.Count;
            int theLowestFragmentNumber = wagerNumbers.Min();
            int theHighestFragmentNumber = wagerNumbers.Max();

            var commandBuilder = new StringBuilder();
            var isMultipleAuthorization = body.AuthorizationNumber == -1;
            var countFragments = fragmentsDescription.Count();
            int groupsOfRowsToInsert = countFragments > MaxFragmentsPerGroup ? ((countFragments - 1) / MaxFragmentsPerGroup) + 1 : 1;
            for (int index = 0; index < groupsOfRowsToInsert; index++)
            {
                var indexToStart = index * MaxFragmentsPerGroup;
                commandBuilder.AppendLine("{");
                if (isMultipleAuthorization)
                {
                    AuthorizationsNumbers.BuildCommand(buildResponse.Wagers.Select(w => w.TicketNumber).Skip(indexToStart).Take(MaxFragmentsPerGroup), ref commandBuilder);
                    commandBuilder.Append("authorizations = atAddress.GetAuthorizations(auths.Numbers);").AppendLine();
                    var amountOfFragmentsInGroup = Math.Min(buildResponse.Wagers.Length, MaxFragmentsPerGroup);
                    commandBuilder.Append("authorizations.CreateFragments();").AppendLine();
                }
                else
                {
                    commandBuilder.Append("authorization = atAddress.GetAuthorization(").Append(body.AuthorizationNumber).Append(");").AppendLine();
                    if (index == 0) commandBuilder.Append("authorization.CreateFragments(").Append(theLowestFragmentNumber).Append(", ").Append(theHighestFragmentNumber).Append(");").AppendLine();
                }

                var referencesInGroup = fragmentsReferences.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                var escapedDescriptionInGroup = fragmentsDescription.Skip(indexToStart).Take(MaxFragmentsPerGroup).Select(description => EscapeSingleQuotes(description));
                var maxFragmentNumber = escapedDescriptionInGroup.Count();
                if (hasSameToWinAmounts && hasSameRiskAmounts)
                {
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                }
                else if (!hasSameToWinAmounts && hasSameRiskAmounts)
                {
                    var toWinsInGroup = fragmentsToWins.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                }
                else if (hasSameToWinAmounts && !hasSameRiskAmounts)
                {
                    var risksInGroup = fragmentsToRisks.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                }
                else if (!hasSameToWinAmounts && !hasSameRiskAmounts)
                {
                    var toWinsInGroup = fragmentsToWins.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    var risksInGroup = fragmentsToRisks.Skip(indexToStart).Take(MaxFragmentsPerGroup);
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AddFragments(").Append(indexToStart).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AddFragments(1, ").Append(maxFragmentNumber).Append(", {'").Append(string.Join("','", referencesInGroup)).Append("'}, {'").Append(string.Join("','", escapedDescriptionInGroup)).Append("'}, {").Append(string.Join(",", risksInGroup)).Append("}, {").Append(string.Join(",", toWinsInGroup)).Append("});").AppendLine();
                    }
                }
                else
                {
                    throw new GameEngineException($"There is no valid option for risk and towin combination.");
                }
                commandBuilder.AppendLine("}");
                var commandResult = CashierAPI.Cashier.PerformCmd(body.AtAddress, commandBuilder.ToString());
                if (!(commandResult is OkObjectResult))
                {
                    ErrorsSender.Send($"command:{commandBuilder}.\nauthorization:{body.AuthorizationNumber}.\nWagersCount {buildResponse.Wagers.Count()}",
                        "Fragment creation fails.");

                    throw new GameEngineException($"failure in command:{commandBuilder}");
                }
                commandBuilder.Clear();
            }
            
            return Ok(buildResponse);
        }

        [HttpPost("api/multiple/fragments")]
		[Authorize(Roles = "dA3")]
        public async Task<IActionResult> CreateMultipleFragmentsAsync([FromBody] FragmentAuthorizationBody body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (body.Fragments == null) return BadRequest($"{nameof(body.Fragments)} is required");
            if (!body.Fragments.Any()) return BadRequest($"{nameof(body.Fragments)} is required");

            FragmentsChunks fragmentsChunksX = new FragmentsChunks();
			foreach (var frag in body.Fragments)
			{
				fragmentsChunksX.Add(frag);
			}

            var wagers = Array.ConvertAll(body.Fragments, new Converter<Fragment, PostFreeFormWager>(EntitiesConverter.FragmentToWager));
            var result = EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, 1);

			var numbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
			int theLowestFragmentNumber = numbers.Min();
			int theHighestFragmentNumber = numbers.Max();

			bool fistIteration = true;
            bool lastIteration = false;
            int iteration = 0;
            List<string> commands = new List<string>();
            var countOfChunks = fragmentsChunksX.AmountOfChunks();
            var isMultipleAuthorization = body.AuthorizationNumber == -1;
            foreach (Fragments fragments in fragmentsChunksX.List())
            {
                iteration++;
                lastIteration = iteration == countOfChunks;

                StringBuilder commandBuilder = new StringBuilder();
                commandBuilder.AppendLine("{");
                if (isMultipleAuthorization)
                {
                    AuthorizationsNumbers.BuildCommand(fragments.TicketNumbers, ref commandBuilder);
                    commandBuilder.Append("authorizations = atAddress.GetAuthorizations(auths.Numbers);").AppendLine();
                    commandBuilder.Append("authorizations.InitFragmentCreation();").AppendLine();
                }
                else
                {
                    commandBuilder.Append("authorization = atAddress.GetAuthorization(").Append(body.AuthorizationNumber).Append(");").AppendLine();
                    if (fistIteration)
                    {
                        fistIteration = false;
                        commandBuilder.Append("authorization.InitFragmentCreation(").Append(theLowestFragmentNumber).Append(", ").Append(theHighestFragmentNumber).Append(");").AppendLine();
                    }
                }

                decimal fragmentsRisk = fragments.FragmentsRisk;
                decimal fragmentsTowin = fragments.FragmentsTowin;
                List<string> fragmentsReferences = fragments.FragmentsReferences;
                var escapedFragmentsDescription = fragments.BetDescriptions.Select(description => EscapeSingleQuotes(description));
                List<string> fragmentsToWins = fragments.ToWins;
                List<string> fragmentsToRisks = fragments.Risks;
                bool hasSameToWinAmounts = fragments.HasSameToWinAmounts;
                bool hasSameRiskAmounts = fragments.HasSameRiskAmounts;

                if (hasSameToWinAmounts && hasSameRiskAmounts)
                {
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                }
                else if (!hasSameToWinAmounts && hasSameRiskAmounts)
                {
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, ").Append(fragmentsRisk).Append(", {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                }
                else if (hasSameToWinAmounts && !hasSameRiskAmounts)
                {
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, ").Append(fragmentsTowin).Append(");").AppendLine();
                    }
                }
                else if (!hasSameToWinAmounts && !hasSameRiskAmounts)
                {
                    if (isMultipleAuthorization)
                    {
                        commandBuilder.Append("authorizations.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                    else
                    {
                        commandBuilder.Append("authorization.AppendFragments(").Append("{'").Append(string.Join("','", fragmentsReferences)).Append("'}, {'").Append(string.Join("','", escapedFragmentsDescription)).Append("'}, {").Append(string.Join(",", fragmentsToRisks)).Append("}, {").Append(string.Join(",", fragmentsToWins)).Append("});").AppendLine();
                    }
                }
                else
                {
                    throw new GameEngineException($"There is no valid option for risk and towin combination.");
                }

                if (isMultipleAuthorization)
                {
                    commandBuilder.Append("authorizations.CommitFragmentCreation();").AppendLine();
                }
                else
                {
                    if (lastIteration) commandBuilder.Append("authorization.CommitFragmentCreation();").AppendLine();
                }

                commandBuilder.AppendLine("}");
                commands.Add(commandBuilder.ToString());
            }

            foreach (string command in commands)
            {
                var commandResult = CashierAPI.Cashier.PerformCmd(body.AtAddress, command);
                if (!(commandResult is OkObjectResult))
                {
                    ErrorsSender.Send($"authorization:{body.AuthorizationNumber}.\nCount {fragmentsChunksX.TotalAmountOfFragments()}.\n failed command:{command}.\n complete command:{string.Join(" | ", commands)}",
                        "Fragment creation fails.");

                    throw new GameEngineException($"failure in command:{command}");
                }
            }

            return Ok();
        }



        [HttpPost("api/fragment/multiple/depositThenLock")]
        [Authorize(Roles = "dA3")]
        public async Task<IActionResult> MultipleDepositAuthorizationAsync([FromBody] FragmentsCreationBodyDTO body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ProcessorKey)) return BadRequest($"{nameof(body.ProcessorKey)} is required");
            if (body.StoreId <= 0) return BadRequest($"{nameof(body.StoreId)} is required");
            if (body.Total <= 0) return BadRequest($"{nameof(body.Total)} is required");
            if (!body.Fragments.Any()) return BadRequest($"{nameof(body.Fragments)} is required");

            var result = CashierAPI.Cashier.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
				{{
                    processorAccount = guardian.Accounts().SearchByProcessor('{body.ProcessorKey}');
                    print processorAccount.Id processorAccountId;
                }}");
            if (!(result is OkObjectResult)) return BadRequest("Error: Processor account fails");

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

            StringBuilder commandForAuthorizations = new StringBuilder();
            AuthorizationsNumbers.BuildCommand(body.Fragments.Select(x => x.TicketNumber), ref commandForAuthorizations);

            Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.Total);
            var dateAsText = body.Date.ToString("MM/dd/yyyy HH:mm:ss");
            var normalizedUseless = body.Useless.ToString("MM/dd/yyyy HH:mm:ss");
            string commandForCreateAuthorization;
            if (body.AllRisksAreEquals)
            {
                var currencyByAuthorization = Currency.Factory(body.CurrencyCode, decimal.Parse(body.Fragments.First().Risk));
                commandForCreateAuthorization = $"balance.Accredit(itIsThePresent, {dateAsText}, Currency('{body.CurrencyCode}', {purchaseTotal.Value}), '{Users.ME}', auths.Numbers, store, '', '', {processorAccountInfo.processorAccountId});";
                commandForCreateAuthorization += $"atAddress.CreateAuthorization(itIsThePresent,{dateAsText}, Currency('{currencyByAuthorization.CurrencyCode}', {currencyByAuthorization.Value}), auths.Numbers, store, '', '', '{body.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});";
            }
            else
            {
                var risks = string.Join("','", body.Fragments.Select(f => f.Risk));
                commandForCreateAuthorization = $"balance.Accredit(itIsThePresent, {dateAsText}, {{'{risks}'}}, '{Users.ME}', auths.Numbers, store, '', '', {processorAccountInfo.processorAccountId});";
                commandForCreateAuthorization += $"atAddress.CreateAuthorization(itIsThePresent,{dateAsText}, '{body.CurrencyCode}', {{'{risks}'}}, auths.Numbers, store, '', '', '{body.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});";
            }

            string command = $@"
				{{
					balance = atAddress.CreateAccountIfNotExists('{body.CurrencyCode}', '{body.CurrencyCode}').Balance;
                    Eval('available = '+balance.Available+';');
					balance.SetInitialBalance(itIsThePresent, available);
					store = company.Sales.StoreById({body.StoreId});
                    {commandForAuthorizations}
					{commandForCreateAuthorization}
				}}";

            result = CashierAPI.Cashier.PerformCmd(body.AtAddress, command);
            if (!(result is OkObjectResult)) return BadRequest("Error in command");

            return result;
        }

        [HttpPost("api/single/depositThenLock")]
        [Authorize(Roles = "dA3")]
        public async Task<IActionResult> DepositSingleAuthorizationAsync([FromBody] FragmentsCreationBodyDTO body)
        {
            if (body == null) return BadRequest($"{nameof(body)} is required");
            if (string.IsNullOrWhiteSpace(body.AtAddress)) return BadRequest($"{nameof(body.AtAddress)} is required");
            if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ProcessorKey)) return BadRequest($"{nameof(body.ProcessorKey)} is required");
            if (body.StoreId <=0 ) return BadRequest($"{nameof(body.StoreId)} is required");


			var result = CashierAPI.Cashier.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
            {{
                processorAccount = guardian.Accounts().SearchByProcessor('{body.ProcessorKey}');
                print processorAccount.Id processorAccountId;
            }}");
			if (!(result is OkObjectResult)) return BadRequest("Error: Processor account fails");

            OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

			Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.Total);
			var normalizedUseless = body.Useless.ToString("MM/dd/yyyy HH:mm:ss");
			string command = $@"
            			{{
            				balance = atAddress.CreateAccountIfNotExists('{body.CurrencyCode}', '{body.CurrencyCode.ToString()}').Balance;
            				Eval('available = '+balance.Available+';');
            				balance.SetInitialBalance(itIsThePresent, available);
            				store = company.Sales.StoreById({body.StoreId.ToString()});
            				balance.Accredit(itIsThePresent, Now, Currency('{body.CurrencyCode}', {body.Total}), '{Users.ME}', '{body.AuthorizationNumber}', store, '', '', {processorAccountInfo.processorAccountId});
            				authorization = atAddress.CreateAuthorization(itIsThePresent,Now, Currency('{body.CurrencyCode}', {purchaseTotal.Value}), {body.AuthorizationNumber}, store, '', '', '{body.CurrencyCode}', {normalizedUseless}, {processorAccountInfo.processorAccountId});
							
							print authorization.Number authorizationNumber;
            			}}";

			result = CashierAPI.Cashier.PerformCmd(body.AtAddress, command);
            if (!(result is OkObjectResult)) return BadRequest("Error in command");

            return result;
        }

		[HttpPost("api/customers/{atAddress}/balance/{currencyCode}/available")]
		[Authorize(Roles = "dA1")]
		public async Task<IActionResult> AvailableBalanceAsync(string atAddress, string currencyCode)
		{
            if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
            if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

            var result = await CashierAPI.Cashier.PerformQryAsync(atAddress, HttpContext, $@"
			{{
				accumulatedBalances = atAddress.GetAccumulatedBalances({{'{currencyCode}'}});
				if (! accumulatedBalances.IsEmpty)
				{{
                    balances = accumulatedBalances.Balances;
					for (balance : accumulatedBalances.Balances)
					{{
						currentBalance = balance;
						currencyCode = currentBalance.CurrencyCodeAsText;
						print currencyCode code;
						print currentBalance.Locked locked;
						print currentBalance.Available balance;
						for (accounts : currentBalance.Accounts)
						{{
							account = accounts;
							print account.Number accountNumber;
							print currencyCode code;
							print account.Balance.Available available;
						}}
					}}
				}}
			}}
			");

            if (!(result is OkObjectResult))
            {
                ErrorsSender.Send($"atAddress:{atAddress} balance is failing.", $"Balance for atAddress:{atAddress} its not loading.");
                return new InternalServerErrorObjectResult(0);
            }

            string resultToString = ((OkObjectResult)result).Value.ToString();
            if (string.IsNullOrWhiteSpace(resultToString)) 
            {
                var resp = new BalancesResponse();
                resp.Add(new BalanceResponse
                {
                    AtAddress = atAddress,
                    CurrencyCodeAsText = currencyCode,
                    Balance = 0
                });
                return Ok(resp);
            }

            var temp = JsonConvert.DeserializeObject<BalancesResponse>(resultToString);
            return Ok(temp);
        }

        [HttpPost("api/pay")]
		[Authorize(Roles = "dA9")]
		public async Task<IActionResult> PayAsync([FromBody] PayBody body)
		{
            if (body == null) return BadRequest($"{nameof(body)} is required");
            //if (string.IsNullOrEmpty(body.Concept)) return BadRequest($"{nameof(body.Concept)} is invalid");
            if (string.IsNullOrEmpty(body.Who)) return BadRequest($"{nameof(body.Who)} is invalid");
            if (body.GradeFreeFormWagers == null) return BadRequest($"{nameof(body.GradeFreeFormWagers)} is invalid");
            if (body.GradeFreeFormWagers.Count == 0) return BadRequest($"{nameof(body.GradeFreeFormWagers)} is invalid");
            if (body.StoreId <= 0) return BadRequest($"{nameof(body.StoreId)} is invalid");
            if (string.IsNullOrEmpty(body.ProcessorKey)) return BadRequest($"{nameof(body.ProcessorKey)} is invalid");
            if (body.ProcessorId <= 0) return BadRequest($"{nameof(body.ProcessorId)} is invalid");


            var result = CashierAPI.Cashier.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
		    {{
                processorAccount = guardian.Accounts().SearchByProcessor('{body.ProcessorKey}');
                print processorAccount.Id processorAccountId;
            }}");
            if (!(result is OkObjectResult)) throw new Exception("Processor account not found");

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

            //var sortedWagers = body.GradeFreeFormWagers.OrderBy(x => payFragments.GetAtAddressForThisAuthorizationNumber(x.TicketNumber)).ThenBy(x => x.TicketNumber).ToArray();
            var sortedWagers = body.GradeFreeFormWagers.OrderBy(x => x.AtAddress).ThenBy(x => x.TicketNumber).ToArray();
            int wagerIndex = 0;

            GradeFreeFormWagersResponse finalResponse = new GradeFreeFormWagersResponse();
            var buffer = MovementsBuffers.GetOrCreateBuffer(true);
            var storeId = body.StoreId;
            var processorId = body.ProcessorId == 0 ? processorAccountInfo.processorAccountId : body.ProcessorId;

            StringBuilder addressScript = new StringBuilder();
            StringBuilder changesPerAuthorization = new StringBuilder();
            StringBuilder wagersWinners = new StringBuilder();
            StringBuilder wagersLosers = new StringBuilder();
            StringBuilder refunds = new StringBuilder();
            StringBuilder wagersAdjustments = new StringBuilder();
            StringBuilder initialFragmentStatus = new StringBuilder();
            while (wagerIndex < sortedWagers.Count())
            {
                //string currAtAddress = payFragments.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].TicketNumber);
                string currAtAddress = sortedWagers[wagerIndex].AtAddress;
                if (buffer.RequiresToChangeTopic(currAtAddress))
                {
                    if (!buffer.IsEmpty()) buffer.Flush();
                    buffer.UpdateTopicName(currAtAddress);
                }

                //while (wagerIndex < sortedWagers.Count() && payFragments.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].TicketNumber) == currAtAddress)
                while (wagerIndex < sortedWagers.Count() && sortedWagers[wagerIndex].AtAddress == currAtAddress)
                {
                    string currTicketNumber = sortedWagers[wagerIndex].TicketNumber;

                    wagersWinners.Append('{');
                    wagersLosers.Append('{');
                    refunds.Append('{');

                    int emptyLengthWagers = wagersWinners.Length;

                    while (wagerIndex < sortedWagers.Count() && sortedWagers[wagerIndex].TicketNumber == currTicketNumber)
                    {
                        var wager = sortedWagers[wagerIndex];
                        wagerIndex++;

                        WagerStatus outcome;
                        Enum.TryParse(wager.Outcome, out outcome);

                        switch (outcome)
                        {
                            case WagerStatus.L:
                                wagersLosers.Append(wager.WagerNumber);
                                wagersLosers.Append(',');
                                break;
                            case WagerStatus.W:
                                wagersWinners.Append(wager.WagerNumber);
                                wagersWinners.Append(',');
                                break;
                            case WagerStatus.X:
                                refunds.Append(wager.WagerNumber);
                                refunds.Append(',');
                                break;
                            case WagerStatus.D:
                                refunds.Append(wager.WagerNumber);
                                refunds.Append(',');
                                break;
                            default:
                                ErrorsSender.Send(
                                    $@"Wager with unknown Outcome:{wager.Outcome}",
                                    $@"TicketNumber:{wager.TicketNumber} \n
										    WagerNumber:{wager.WagerNumber} \n
										    DailyFigureDate_YYYYMMDD:{wager.DailyFigureDate_YYYYMMDD} \n
										    IsValidTicketNumber:{wager.IsValidTicketNumber} \n
										    AdjustedLossAmount:{wager.AdjustedLossAmount} \n
										    AdjustedWinAmount:{wager.AdjustedWinAmount}");
                                break;
                        }

                        decimal AdjustedWinAmount = (string.IsNullOrEmpty(wager.AdjustedWinAmount)) ? 0 : decimal.Parse(wager.AdjustedWinAmount);
                        decimal AdjustedLossAmount = (string.IsNullOrEmpty(wager.AdjustedLossAmount)) ? 0 : decimal.Parse(wager.AdjustedLossAmount);
                        bool hasAdjustments = AdjustedWinAmount != 0 || AdjustedLossAmount != 0;
                        if (hasAdjustments)
                        {
                            wagersAdjustments.Append(".PrepareAdjustments(authorization, ").Append(wager.WagerNumber).Append(',').Append(AdjustedWinAmount).Append(',').Append(AdjustedLossAmount).Append(')');
                        }
                    }

                    if (wagersWinners.Length > 1) wagersWinners.Length = wagersWinners.Length - 1;
                    if (wagersLosers.Length > 1) wagersLosers.Length = wagersLosers.Length - 1;
                    if (refunds.Length > 1) refunds.Length = refunds.Length - 1;

                    wagersWinners.Append('}');
                    wagersLosers.Append('}');
                    refunds.Append('}');

                    if (wagersLosers.Length > 2)
                    {
                        initialFragmentStatus.Append("Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersLosers).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersLosers).Append(", initialStatus);");
                        changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersLosers).Append(", Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.Loser).Append(',').Append(processorId).Append(')');
                    }
                    if (wagersWinners.Length > 2)
                    {
                        initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersWinners).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersWinners).Append(", initialStatus);");
                        changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersWinners).Append(",  Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.Winner).Append(',').Append(processorId).Append(')');
                    }
                    if (refunds.Length > 2)
                    {
                        initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(refunds).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(refunds).Append(", initialStatus);");
                        changesPerAuthorization.Append($".PrepareRefunds(itIsThePresent, authorization,").Append(refunds).Append(", Now, store, '").Append(body.Concept).Append("', ").Append(FragmentReason.NoAction).Append(',').Append(processorId).Append(')');
                    }
                    if (wagersAdjustments.Length > 1) changesPerAuthorization.Append(wagersAdjustments.ToString());

                    addressScript.Append("authorization = atAddress.GetAuthorization(").Append(currTicketNumber).Append(");")
                        .Append(initialFragmentStatus)
                        .Append("{ store = company.Sales.StoreById(").Append(storeId).Append(");")
                        .Append("ataddress").Append(changesPerAuthorization).Append(".ApplyChanges(").Append(buffer.Id).Append(", itIsThePresent, '").Append(body.Who).Append("'); }");

                    changesPerAuthorization.Clear();
                    wagersWinners.Clear();
                    wagersLosers.Clear();
                    refunds.Clear();
                    wagersAdjustments.Clear();
                    initialFragmentStatus.Clear();

                    if (addressScript.Length + AverageScriptLengthForOneCommand > MAX_TAMANO_DE_UN_LEXEMA)
                    {
                        CashierAPI.Cashier.PerformCmd(currAtAddress, addressScript.ToString());
                        addressScript.Clear();
                    }
                }
                if (addressScript.Length > 0)
                {
                    CashierAPI.Cashier.PerformCmd(currAtAddress, addressScript.ToString());
                    addressScript.Clear();
                }

                //TODO cris preguntarle a alvaro como hacer el print ya que se necesitan las authorizaciones.
                GradeFreeFormWagersResponse mock = new GradeFreeFormWagersResponse();
                finalResponse.Merge(mock);
            }

            MovementsBuffers.Flush(buffer);
            return Ok(finalResponse);
        }

        public void CreateConsumerForTopics()
		{
			for (int index = 0; index < 10; index++)
			{
				new MovementsConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForMovements}_{index}").StartListening();
			}
			if (!Integration.OnlyMovementsConsumersInitialized)
			{
				new FragmentCreationConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentsCreation).StartListening();
				new UpdatedFragmentsSendingToAgentConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_FRAGMENTS_CONSUMER_SUFFIX}").StartListening();
				new FragmentPaymentsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsForAll).StartListening();
				new FragmentPaymentsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsForWinners).StartListening();
				new MovementsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForMovements).StartListening();
				new DepositsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForDeposits).StartListening();
			new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.CASHIER_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();
#if DEBUG
				Task.Run(() =>
				{
					Thread.Sleep(20000); // This is avoid a race confition when mocks start up.
					new WithdrawalsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForWithdrawals).StartListening();
				});
#else
			new WithdrawalsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForWithdrawals).StartListening();
#endif
			}
		}

		bool ValidateMessageOwnership(string storeAlias)
		{
			return false;
		}
		RestAPIActorAsync GetActor()
		{
			return CashierAPI.Cashier.GetActor(RestAPISpawnerActor.GENERAL);
		}

		public class FragmentCreationConsumer : GamesEngine.Settings.Consumer
		{

			public FragmentCreationConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				FragmentsCreationBody fragmentMessage = new FragmentsCreationBody(msg);

				Integration.MarkFragmentsTableAsCreated(fragmentMessage.AtAddress);
				Integration.MarkMovementsTableAsCreated(fragmentMessage.CurrencyCode);
				Integration.MarkMovementsTableAsCreated(fragmentMessage.AtAddress, Coinage.Coin(fragmentMessage.CurrencyCode));

				bool wasPayedInDollars = fragmentMessage.CurrencyCode == Currencies.CODES.USD.ToString();
				var coin = Coinage.Coin(fragmentMessage.CurrencyCode);

                MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(fragmentMessage.AgentId, fragmentMessage.Domain);
                IEnumerable<PaymentProcessor> paymentProcessorList = drivers.SearchByX(TransactionType.CreateFragment, fragmentMessage.CurrencyCode);

                PaymentProcessor paymentProcessor = paymentProcessorList.FirstOrDefault();
                if (paymentProcessor == null) throw new Exception($"No payment processor for transactionType: {TransactionType.CreateFragment}, currencyCode: {fragmentMessage.CurrencyCode}");

				DateTime now = DateTime.Now;
                PostFreeFormWagerCollectionSuccessResponse result = null;

                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("atAddress", fragmentMessage.AtAddress);
                    recordSet.SetParameter("authorizationNumber", fragmentMessage.AuthorizationNumber);
                    recordSet.SetParameter("fragments", fragmentMessage.Fragments);
                    recordSet.SetParameter("storeId", fragmentMessage.StoreId);
                    recordSet.SetParameter("currencyCode", fragmentMessage.CurrencyCode);
                    recordSet.SetParameter("total", fragmentMessage.Total);
                    recordSet.SetParameter("date", fragmentMessage.Date);
                    recordSet.SetParameter("useless", fragmentMessage.Useless);
                    recordSet.SetParameter("allRisksAreEquals", fragmentMessage.AllRisksAreEquals);
                    recordSet.SetParameter("processorKey", paymentProcessor.ProcessorKey);
                    result = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(now, recordSet);
                }

				if (result == null) throw new Exception("result is null");

                var wagerNumbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
                int theLowestWagerNumber = wagerNumbers.Min();
                int theHighestWagerNumber = wagerNumbers.Max();

                FragmentCreationResponse fragmentLockResponse = new FragmentCreationResponse(
                    fragmentMessage.TheLowestBetId,
                    fragmentMessage.TheHighestBetId,
                    theLowestWagerNumber,
                    theHighestWagerNumber,
                    fragmentMessage.AuthorizationNumber,
                    fragmentMessage.OrderNumber
                );

                var partition = Integration.Kafka.GetPartition(fragmentMessage.Localization);
                Integration.Kafka.Send(true, Integration.Kafka.TopicForFragmentsCreationCallback(fragmentMessage.StoreId), fragmentLockResponse, partition);
            }
        }
            

		public class FragmentPaymentsConsumer : GamesEngine.Settings.Consumer
		{
			private readonly string topic;

			public FragmentPaymentsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

            public override void OnMessageBeforeCommit(string comp)
			{
				if (String.IsNullOrEmpty(comp)) throw new Exception(nameof(comp));
				string msg = FragmentPaymentCompressor.Expand(comp);

				FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
				GradeFreeFormWagersResponse finalResponse = new GradeFreeFormWagersResponse();
				
				string who = payFragments.Owner;
				int storeId = payFragments.StoreId;
				bool sometimeWereTicketsSentToAccounting = payFragments.SometimeWereTicketsSentToAccounting;
                TransactionType transactionType = sometimeWereTicketsSentToAccounting ? TransactionType.UpdatePayPrize : TransactionType.PayPrize;

                foreach (var currencyCode in payFragments.Currencies())
				{
					var fragments = payFragments.WithAtAddressMessageBy(currencyCode);
                    if (fragments.Count > 0)
                    {

                        HashSet<(int AgentId, string DomainUrl)> affiliateAndDomainAlreadySend = new HashSet<(int AgentId, string DomainUrl)>();
                        
                        int currentIndex = 0;
                        (int AgentId, string DomainUrl) currentAffiliateAndDomain = default;

                        bool salir = fragments.Count == 0;
                        if (!salir)
                        {
                            currentIndex = 0;
                            currentAffiliateAndDomain = (fragments[currentIndex].AgentId, fragments[currentIndex].DomainUrl);
                        }

                        while (!salir)
                        {
                            bool alreadySend = affiliateAndDomainAlreadySend.Contains(currentAffiliateAndDomain);
                            if (!alreadySend)
                            {
                                HashSet<PayFragmentsWithAtAddressMessage> fragmentsToBeSend = new HashSet<PayFragmentsWithAtAddressMessage>();

                                for (int fragmentIndex = currentIndex; fragmentIndex < fragments.Count; fragmentIndex++)
                                {
                                    var fragment = fragments[fragmentIndex];
                                    if (
                                        currentAffiliateAndDomain.AgentId == fragment.AgentId &&
                                        currentAffiliateAndDomain.DomainUrl == fragment.DomainUrl
                                    )
                                    {
                                        fragmentsToBeSend.Add(fragment);
                                    }
                                }

                                if (fragmentsToBeSend.Count > 0)
                                {
                                    int agentId = currentAffiliateAndDomain.AgentId;
                                    string domainUrl = currentAffiliateAndDomain.DomainUrl;
                                    MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domainUrl);

                                    var paymentProcessors = drivers.SearchByX(transactionType, currencyCode.Iso4217Code);
                                    if (paymentProcessors == null && paymentProcessors.Count() > 1) throw new Exception($"There are more than one payment processor for transactionType: {transactionType}, currencyCode: {currencyCode.Iso4217Code}");

                                    var paymentProcessor = paymentProcessors.FirstOrDefault();
                                    if (paymentProcessor == null) throw new Exception($"No payment processor for transactionType: {transactionType}, currencyCode: {currencyCode.Iso4217Code}");
                                    using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                                    {
                                        recordSet.SetParameter("who", who);
                                        recordSet.SetParameter("concept", payFragments.Concept);
                                        recordSet.SetParameter("storeId", storeId);
                                        recordSet.SetParameter("processorKey", paymentProcessor.ProcessorKey);
                                        recordSet.SetParameter("processorId", paymentProcessor.Id);
                                        recordSet.SetParameter("gradeFreeFormWagers", fragmentsToBeSend.ToList());
                                        var temp = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
                                        finalResponse.Merge(temp);
                                        affiliateAndDomainAlreadySend.Add(currentAffiliateAndDomain);
                                    }
                                }
                            }

                            currentIndex = currentIndex + 1;
                            if (currentIndex >= fragments.Count)
                            {
                                salir = true;
                            }
                            else
                            {
                                currentAffiliateAndDomain = (fragments[currentIndex].AgentId, fragments[currentIndex].DomainUrl);
                            }
                        }
                    }
                }

				if (finalResponse.Wagers != null && finalResponse.Wagers.Length > 0)
				{
					FragmentPaymentsWithProblemsResponse fragmentsWithProblems = new FragmentPaymentsWithProblemsResponse();
					foreach (var wager in finalResponse.Wagers)
					{
						if (!wager.IsValidTicketNumber)
						{
							fragmentsWithProblems.Add(wager.TicketNumber, wager.WagerNumber);
						}
					}

					if (fragmentsWithProblems.HasItems())
					{
                        var partition = Integration.Kafka.GetPartition(payFragments.Localization);
                        Integration.Kafka.Send(true, Integration.Kafka.TopicForFragmentPaymentsCallback(storeId), fragmentsWithProblems, partition);
					}
				}
			}
		}

		public class UpdatedFragmentsSendingToAgentConsumer : Consumer
		{
			public UpdatedFragmentsSendingToAgentConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

				var payFragments = new FragmentPaymentSendingToAgentMessages(msg);
                var fragments = payFragments.Fragments.ToList();
				PaymentChannels.UpdateWagers(fragments);
			}
		}

		private class MovementsConsumer : Consumer
		{
			public MovementsConsumer(string group, string topic) : base(group, topic)
			{
				PrepareDataTables();
			}

			MovementsDeserialitor movementsDeserialitor = new MovementsDeserialitor();
			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

				MovementsDeserialitor movementsDeserialitor = new MovementsDeserialitor();

				int whosId = Users.NO_USER;
				movementsDeserialitor.Separate(msg);
				if (movementsDeserialitor.Who != Users.LADYBET)
				{
					whosId = Movements.Storage.InsertUserIfNotExists(movementsDeserialitor.StoreId, movementsDeserialitor.Who);
				}

				Movements.Storage.SaveBalanceMovements(true, whosId, movementsDeserialitor.Movements, movementTableByAccount, movementTableByCurrency);
				movementsDeserialitor.Clear();
			}

			DataTable movementTableByAccount = new DataTable();
			DataTable movementTableByCurrency = new DataTable();
			void PrepareDataTables()
			{
				movementTableByAccount.Columns.Add("DAY", typeof(string));
				movementTableByAccount.Columns.Add("SOURCE", typeof(int));
				movementTableByAccount.Columns.Add("MOVEMENT", typeof(string));
				movementTableByAccount.Columns.Add("AMOUNT", typeof(decimal));
				movementTableByAccount.Columns.Add("NEWBALANCE", typeof(decimal));
				var dataColumn = movementTableByAccount.Columns.Add("WHO", typeof(int));
				dataColumn.AllowDBNull = true;
				movementTableByAccount.Columns.Add("DOCUMENTNUMBER", typeof(string));
				movementTableByAccount.Columns.Add("STORE", typeof(int));
				movementTableByAccount.Columns.Add("REFERENCE", typeof(string));
				movementTableByAccount.Columns.Add("CONCEPT", typeof(string));
				movementTableByAccount.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
				movementTableByAccount.Columns.Add("ACCOUNT_NUMBER", typeof(string));
				movementTableByAccount.Columns.Add("PROCESSORID", typeof(int));

				movementTableByCurrency.Columns.Add("DAY", typeof(string));
				movementTableByCurrency.Columns.Add("ATADDRESS", typeof(string));
				movementTableByCurrency.Columns.Add("SOURCE", typeof(int));
				movementTableByCurrency.Columns.Add("CURRENCY", typeof(string));
				movementTableByCurrency.Columns.Add("MOVEMENT", typeof(string));
				movementTableByCurrency.Columns.Add("AMOUNT", typeof(decimal));
				movementTableByCurrency.Columns.Add("NEWBALANCE", typeof(decimal));
				dataColumn = movementTableByCurrency.Columns.Add("WHO", typeof(int));
				dataColumn.AllowDBNull = true;
				movementTableByCurrency.Columns.Add("DOCUMENTNUMBER", typeof(string));
				movementTableByCurrency.Columns.Add("STORE", typeof(int));
				movementTableByCurrency.Columns.Add("REFERENCE", typeof(string));
				movementTableByCurrency.Columns.Add("CONCEPT", typeof(string));
				movementTableByCurrency.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
				movementTableByCurrency.Columns.Add("ACCOUNT_NUMBER", typeof(string));
				movementTableByCurrency.Columns.Add("PROCESSORID", typeof(int));
			}
		}

		public class DepositsConsumer : Consumer
		{
			private string topic;
			public DepositsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				string[] messages = KafkaMessages.Split(msg);
				foreach (string codedMessage in messages)
				{
					string command = "";
					int authorization = 0;

					try
					{
						DepositMessage message = new DepositMessage(codedMessage);
                        Integration.MarkFragmentsTableAsCreated(message.AtAddress);
						Integration.MarkMovementsTableAsCreated(message.Coin.Iso4217Code);
						Integration.MarkMovementsTableAsCreated(message.AtAddress, message.Coin);
						PaymentChannels.DepositX(true, DateTime.Now, CashierAPI.Cashier, message, out authorization);
	
					}
					catch (Exception e)
					{
						ErrorsSender.Send(e, $"message {msg}",  $"command:{command}", $"authorization:{authorization}");
					}	
				}
			}
		}

		public class WithdrawalsConsumer : Consumer
		{
			private string topic;
			public WithdrawalsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				string[] messages = KafkaMessages.Split(msg);
				foreach (string codedMessage in messages)
				{
					int authorization = 0;
					try
					{
						WithdrawMessage message = new WithdrawMessage(codedMessage);
						PaymentChannels.WithDrawX(true, DateTime.Now, CashierAPI.Cashier, message, out authorization);
					}
					catch (Exception e)
					{
						ErrorsSender.Send(e, $"message {msg}", $"authorization:{authorization}");
					}
				}
			}
		}

		[DataContract(Name = "ProcessorsResponse")]
		public class ProcessorsResponse
		{
			[DataMember(Name = "processors")]
			public List<ProcessorWithDistinctKey> Processors { get; set; }
		}

		[DataContract(Name = "ProcessorWithDistinctKey")]
		public class ProcessorWithDistinctKey
		{
			[DataMember(Name = "alias")]
			public string Alias { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
		}

    }
}
