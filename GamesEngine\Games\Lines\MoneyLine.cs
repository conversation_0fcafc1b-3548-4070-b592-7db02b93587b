﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class MoneyLine : ABLine
	{
		internal const string TEXT = "Money Line";

		internal MoneyLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, int teamAReward, int teamBReward, string who, DateTime now) : base(lineId, question, tier, game, game.Favorite, shelve, index, teamAReward, teamBReward, who, now)
		{
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException($"Game {game.ToString()} already started, it does not allow changes");
			if (game.IsGameOver()) throw new GameEngineException($"Game {game.ToString()} already started, it does not allow changes");
			if (string.IsNullOrWhiteSpace(question.Text)) throw new ArgumentNullException(nameof(question.Text));

			base.Text = ReplacePlaceholders(question.Text, game);
		}

		private MoneyLine(MoneyLine previousVersion, int teamAReward, int teamBReward, string who, DateTime now) : base(previousVersion, teamAReward, teamBReward, who, now)
		{
			if (!base.IsDraft && !base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		internal override decimal Grade(Wager wager)
		{
			if (Tier == Tier.TIER_ONE && !this.game.IsGameOver()) throw new GameEngineException($"This game {this.game.ToString()} can not be graded because it is not ended yet.");

			decimal prize = 0;
			if (base.RealAnswer.IsDraw())
			{
				if (!base.Game.Sport.AllowsTieResults()) throw new GameEngineException($"Sport {Game.Sport.Name} does not allow tie results");

				wager.ChangeToNoAction();
			}
			else
			{
				var chosenTeam = (ABAnswer)wager.ChosenAnswer;
				bool won = false;

				if (chosenTeam.ChosenTeam == base.TeamA)
				{
					won = base.RealAnswer.TeamAisWinner();
				}
				else if (chosenTeam.ChosenTeam == base.TeamB)
				{
					bool teamBIsWinner = !base.RealAnswer.TeamAisWinner();
					won = teamBIsWinner;
				}
				else
				{
					throw new GameEngineException($"Team {chosenTeam.ChosenTeam.Name} does not belong to game {this.game.ToString()}");
				}

				if (won)
				{
					prize = wager.ToWin();
					wager.GradeAsWinner();
				}
				else
				{
					wager.GradeAsLoser();
				}
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal MoneyLine NewVersion(int teamAReward, int teamBReward, string who, DateTime now)
		{
			MoneyLine result = new MoneyLine(this, teamAReward, teamBReward, who, now);
			return result;
		}

		internal override string LineTypeAsString => LineType.MONEY_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (!game.IsGameOver() && !Showcase.IsThereAnyPendingLine()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.MONEY_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(base.TeamAReward);
			message.AddProperty(base.TeamBReward);
			if (RealAnswer.TeamAisWinner())
			{
				message.AddProperty('A');
			}
			else
			{
				message.AddProperty('B');
			}
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedMoneyLineEvent(timestamp, this);
			return result;
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedMoneyLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}
	}
}
