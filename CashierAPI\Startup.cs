using CashierAPI.Controllers;
using Elastic.Apm.AspNetCore;
using GamesEngine;
using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngineMocks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using town.connectors.drivers;

namespace CashierAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            int minWorker, minIOC;
            ThreadPool.GetMinThreads(out minWorker, out minIOC);
            int maxWorker, maxIOC;
            ThreadPool.GetMaxThreads(out maxWorker, out maxIOC);

            Console.WriteLine($@" minWorker:{minWorker} minIOC:{minIOC} maxWorker:{maxWorker} maxIOC:{maxIOC}");

            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            /*WebHookConfiguration*/
            WebHookClient.Configure(Configuration);
            /*WebHookConfiguration*/

            /*TownSettings*/
            TownSettings.Configure(Configuration);
            /*TownSettings*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Cashier API", Version = "v1" });
            });
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            var biIntegration = Configuration.GetSection("BIIntegration");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);
            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());

            Integration.ConfigureAPM(Configuration);

            var messageOriginId = Configuration.GetValue<string>("MessageOriginId");
            if (string.IsNullOrEmpty(messageOriginId) && !int.TryParse(messageOriginId, out _)) throw new Exception("MessageOriginId its requeried in the appsettings.");
            MessageCatalog.AssingMessageOriginId(Convert.ToInt32(messageOriginId));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (Integration.UseAPM)
            {
                app.UseElasticApm(Configuration);
            }

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Cashier API V1");
                c.RoutePrefix = string.Empty;
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            var sectionDairy = Configuration.GetSection("DBDairy");
            Integration.MySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            Integration.SqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            Integration.DbSelected = sectionDairy.GetValue<string>("DBSelected");
            var CassandraHost = sectionDairy.GetValue<string>("CassandraHost");
            var KeySpace = sectionDairy.GetValue<string>("KeySpace");
            var UseCassandra = sectionDairy.GetValue<bool>("UseCassandra");
            var MonthsToArchivedMovements = sectionDairy.GetValue<int>("MonthsToArchivedMovements");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");
            int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);
            var isFollower = Configuration.GetValue<bool>("IsFollower");

            Settings.DbSelected = Integration.DbSelected;
            Settings.ScriptBeforeRecovering = scriptBeforeRecovering;
            Settings.MySQL = Integration.MySQL;
            Settings.SqlServer = Integration.SqlServer;
            Integration.MockToStart = numberOfTheMockConfigured;

            Settings.CassandraHost = CassandraHost;
            Settings.UseCassandra = UseCassandra;
            Settings.KeySpace = KeySpace;
            Settings.MonthsToArchivedMovements = MonthsToArchivedMovements;

            if (!isFollower)
            {
                CashierAPI.Initialize(new CashierMocks());

                Settings.ConfigureStorage();
            }

            /*Security*/
            app.UseAuthentication();
            /*Security*/

            app.UseMvc();

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);


            if(!isFollower)
            {
                DriverController.RestActor = CashierAPI.Cashier.GetActor(RestAPISpawnerActor.GENERAL);
                /*Consumer must start ones the actor already recover the state to avoid race conditions.*/
                if (Integration.UseKafka) new ApiController().CreateConsumerForTopics();

            var result = CashierAPI.Cashier.PerformQry(RestAPISpawnerActor.GENERAL, $@"
                {{
					accounts = guardian.Accounts();
                    print accounts.NextAccountId() accountId;
					for (processors:guardian.PaymentProcessorsWithoutAccounts())
					{{
						processor = processors;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;
					}}
                }}
                ");

            if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var processorsWithoutAccounts = JsonConvert.DeserializeObject<ProcessorsWithoutAccounts>(json);

            var withdrawalProcessorsWithoutAccounts = new List<ProcessorWithoutAccounts>();
            var commandsToCreateAccounts = new StringBuilder();
                if (processorsWithoutAccounts != null && processorsWithoutAccounts.Processors?.Count > 0)
                {
                    int accountId = processorsWithoutAccounts.AccountId;
                    int authorization = 100000;
                    foreach (var processorWithoutAccounts in processorsWithoutAccounts.Processors)
                    {
                        var processorAccount = processorWithoutAccounts.ProcessorAccount(accountId);
                        commandsToCreateAccounts.Append("accounts.Create(false, ").
                            Append(accountId).Append(", '").
                            Append(processorWithoutAccounts.ProcessorKey).Append("', '").
                            Append(processorAccount).Append("', '").
                            Append(processorWithoutAccounts.CurrencyCode).Append("');");
                        accountId++;

                        if (processorWithoutAccounts.TransactionType == $"{TransactionType.Withdrawal}" || processorWithoutAccounts.TransactionType == $"{TransactionType.Transfer}" || processorWithoutAccounts.TransactionType == $"{TransactionType.DebitNote}")
                        {
                            Integration.MarkFragmentsTableAsCreated(processorAccount);
                            Integration.MarkMovementsTableAsCreated(processorWithoutAccounts.CurrencyCode);
                            Integration.MarkMovementsTableAsCreated(processorAccount, Coinage.Coin(processorWithoutAccounts.CurrencyCode));
                            result = CashierAPI.Cashier.PerformCmd(processorAccount, $@"
				            balance = atAddress.CreateAccountIfNotExists('{processorWithoutAccounts.CurrencyCode}', '{processorAccount}').Balance;
				            store = company.Sales.StoreById(5);
				            balance.Accredit(itIsThePresent, Now, Currency('{processorWithoutAccounts.CurrencyCode}', 100000), 'ccajero', '{authorization}', store, 'Deposit 100000 {processorWithoutAccounts.CurrencyCode} to {processorAccount}', '{authorization}', {accountId - 1});
                        ");

                            if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
                            authorization--;
                        }
                    }
                    result = CashierAPI.Cashier.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
				        accounts = guardian.Accounts();
                        {commandsToCreateAccounts}
                    ");

                    if (!(result is OkObjectResult)) throw new GameEngineException($"{result}");
                }
            }
        }
    }
}
