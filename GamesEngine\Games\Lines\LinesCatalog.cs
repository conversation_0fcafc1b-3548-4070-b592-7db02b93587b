﻿using GamesEngine.Domains;
using GamesEngine.Middleware;
using GamesEngine.Resources;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	using Tournaments = GamesEngine.Games.Tournaments.Tournaments;
	[Puppet]
	internal class LinesCatalog : Objeto
	{
		private readonly Sport sport;
		private readonly List<Question> questions;
		private readonly DomainsActivations<Question> activations;
		private readonly Tournaments tournaments;
		private ImagesResource backgrounds;
		private readonly IndexedForeingAliasSet<Question> aliases;

		internal LinesCatalog(Tournaments tournaments, Sport sport)
		{
			if (tournaments == null) throw new ArgumentNullException(nameof(tournaments));
			if (sport == null) throw new ArgumentNullException(nameof(sport));

			this.tournaments = tournaments;
			this.sport = sport;
			this.questions = new List<Question>();
			this.activations = new DomainsActivations<Question>();

			aliases = new IndexedForeingAliasSet<Question>();
		}

		internal void LinkAliasToQuestion(int providerId, string idInTheProvider, Question question)
		{
			LinkAliasToQuestion(providerId, idInTheProvider, idInTheProvider, question);
		}

		internal void LinkAliasToQuestion(int providerId, string idInTheProvider, string nameInTheProvider, Question question)
		{
			if (string.IsNullOrEmpty(idInTheProvider)) throw new ArgumentException(nameof(idInTheProvider));
			if (question == null) throw new ArgumentException(nameof(question));

			aliases.Add(providerId, idInTheProvider, nameInTheProvider);

			ForeingAlias foreingAlias = aliases.Add(providerId, idInTheProvider, question);
			question.Aliases.Add(foreingAlias);
		}

		internal Question SearchQuestionByAliasId(int providerId, string id)
        {
			var result = aliases.TypesFor(providerId, id);
			return result;
        }

		internal SpreadQuestion CreateTierOneSpreadQuestion(int id, string shortcut)
		{
			if (questions.Exists(x => x.Tier == Tier.TIER_ONE && x is SpreadQuestion)) throw new GameEngineException($"Only can exist a Tier One template for SpreadLine.");

			return CreateSpreadQuestion(id, Tier.TIER_ONE, shortcut);
		}

		internal SpreadQuestion CreateTierTwoSpreadQuestion(int id, string shortcut)
		{
			return CreateSpreadQuestion(id, Tier.TIER_TWO, shortcut);
		}

		private SpreadQuestion CreateSpreadQuestion(int id, Tier tier, string shortcut)
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			SpreadQuestion spreadQuestion = new SpreadQuestion(id, tier, shortcut, this);
			AddAndActivateQuestion(spreadQuestion);
			return spreadQuestion;
		}

		internal MoneyQuestion CreateTierOneMoneyQuestion(int id, string shortcut)
		{
			if (questions.Exists(x => x.Tier == Tier.TIER_ONE && x is MoneyQuestion)) throw new GameEngineException($"Only can exist a Tier One template for MoneyLine.");

			return CreateMoneyQuestion(id, Tier.TIER_ONE, shortcut);
		}

		internal MoneyQuestion CreateTierTwoMoneyQuestion(int id, string shortcut)
		{
			return CreateMoneyQuestion(id, Tier.TIER_TWO, shortcut);
		}

		private MoneyQuestion CreateMoneyQuestion(int id, Tier tier, string shortcut)
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			MoneyQuestion moneyQuestion = new MoneyQuestion(id, tier, shortcut, this);
			AddAndActivateQuestion(moneyQuestion);
			return moneyQuestion;
		}

		internal MoneyDrawQuestion CreateTierOneMoneyDrawQuestion(int id, string shortcut) 
		{
			if (questions.Exists(x => x.Tier == Tier.TIER_ONE && x is MoneyDrawQuestion)) throw new GameEngineException($"Only can exist a Tier One template for MoneyDrawLine.");

			return CreateMoneyDrawQuestion(id, Tier.TIER_ONE, shortcut);
		}

		internal MoneyDrawQuestion CreateTierTwoMoneyDrawQuestion(int id, string shortcut)
		{
			return CreateMoneyDrawQuestion(id, Tier.TIER_TWO, shortcut);
		}

		private MoneyDrawQuestion CreateMoneyDrawQuestion(int id, Tier tier, string shortcut)
		{
			if (!this.sport.AllowsTieResults()) throw new GameEngineException($"Sport {this.sport.Name} does not allow Money Draw Lines");

			bool tieResultsAreAllowed = (this.sport == Sport.SOCCER && Sport.SOCCER.AllowsTieResults())
				|| (this.sport == Sport.HOCKEY && Sport.HOCKEY.AllowsTieResults());
			if (!tieResultsAreAllowed) throw new GameEngineException($"Money Draw Line is only allowed for {this.sport.Name} sport");
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			MoneyDrawQuestion moneyDrawQuestion = new MoneyDrawQuestion(id, tier, shortcut, this);
			AddAndActivateQuestion(moneyDrawQuestion);
			return moneyDrawQuestion;
		}

		internal TotalPointsQuestion CreateTierOneTotalPointsQuestion(int id, string shortcut) 
		{
			if (questions.Exists(x => x.Tier == Tier.TIER_ONE && x is TotalPointsQuestion)) throw new GameEngineException($"Only can exist a Tier One template for TotalPointsLine.");

			return CreateTotalPointsQuestion(id, Tier.TIER_ONE, shortcut);
		}

		internal TotalPointsQuestion CreateTierTwoTotalPointsQuestion(int id, string shortcut) 
		{
			return CreateTotalPointsQuestion(id, Tier.TIER_TWO, shortcut);
		}

		private TotalPointsQuestion CreateTotalPointsQuestion(int id, Tier tier, string shortcut)
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (tier != Tier.TIER_ONE && tier != Tier.TIER_TWO) throw new GameEngineException("Questions only can be created for Tier one or Tier two");
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			TotalPointsQuestion totalPointsQuestion = new TotalPointsQuestion(id, tier, shortcut, this);
			AddAndActivateQuestion(totalPointsQuestion);
			return totalPointsQuestion;
		}

		private void AddAndActivateQuestion(Question question)
		{
			this.questions.Add(question);
			this.activations.AddActivatableElement(question);
			IncludeAllDomainsFor(question);
		}

		internal YesNoQuestion CreatePropYesNoQuestion(int id, string keyword, string text)
		{
			return CreateYesNoQuestion(id, Tier.PROP, keyword, text);
		}

		internal YesNoQuestion CreateTierTwoYesNoQuestion(int id, string keyword, string text)
		{
			return CreateYesNoQuestion(id, Tier.TIER_TWO, keyword, text);
		}

		internal YesNoQuestion CreateTierOneYesNoQuestion(int id, string keyword, string text)
		{
			return CreateYesNoQuestion(id, Tier.TIER_ONE, keyword, text);
		}

		private YesNoQuestion CreateYesNoQuestion(int id, Tier tier, string shortcut, string text) 
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			YesNoQuestion yesNoQuestion = new YesNoQuestion(id, tier, shortcut, text, this);
			AddAndActivateQuestion(yesNoQuestion);
			return yesNoQuestion;
		}

		private void IncludeAllDomainsFor(Question question)
        {
            foreach (var domain in this.tournaments.Company.Sales.CurrentStore.VisiblesDomains)
            {
				activations.IncludeDomainForElement(question, domain);
            }
        }

		internal OverUnderQuestion CreatePropOverUnderQuestion(int id, string keyword, string text)
		{
			return CreateOverUnderQuestion(id, Tier.PROP, keyword, text);
		}

		internal OverUnderQuestion CreateTierTwoOverUnderQuestion(int id, string keyword, string text)
		{
			return CreateOverUnderQuestion(id, Tier.TIER_TWO, keyword, text);
		}

		internal OverUnderQuestion CreateTierOneOverUnderQuestion(int id, string keyword, string text)
		{
			return CreateOverUnderQuestion(id, Tier.TIER_ONE, keyword, text);
		}

		private OverUnderQuestion CreateOverUnderQuestion(int id, Tier tier, string shortcut, string text)
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");


			OverUnderQuestion overUnderQuestion = new OverUnderQuestion(id, tier, shortcut, text, this);
			AddAndActivateQuestion(overUnderQuestion);
			return overUnderQuestion;
		}

		internal FixedQuestion CreatePropFixedQuestion(int id, string keyword, string text)
		{
			return CreateFixedQuestion(id, Tier.PROP, keyword, text);
		}
		internal FixedQuestion CreateTierTwoFixedQuestion(int id, string keyword, string text)
		{
			return CreateFixedQuestion(id, Tier.TIER_TWO, keyword, text);
		}

		internal FixedQuestion CreateTierOneFixedQuestion(int id, string keyword, string text)
		{
			return CreateFixedQuestion(id, Tier.TIER_ONE, keyword, text);
		}

		private FixedQuestion CreateFixedQuestion(int id, Tier tier, string shortcut, string text)
		{
			if (this.questions.Any(x => x.Id == id)) throw new GameEngineException($"There is already another question with id {id}");
			if (tier == null) throw new ArgumentNullException(nameof(tier));
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));
			if (questions.Exists(x => String.Equals(x.Shortcut, shortcut, StringComparison.OrdinalIgnoreCase))) throw new GameEngineException($"This shortcut {shortcut} is already used, a shortcut must be unique.");

			FixedQuestion fixedQuestion = new FixedQuestion(id, tier, shortcut, text, this);
			AddAndActivateQuestion(fixedQuestion);;
			return fixedQuestion;
		}



		internal Sport Sport
		{
			get
			{
				return this.sport;
			}
		}

		internal Question this[int questionId]
		{
			get
			{
				Question result = this.questions.Find(x => x.Id == questionId);
				if (result == null) throw new GameEngineException($"Question {questionId} does not exist in the catalog");
				return result;
			}
		}

		internal Question FindQuestionById(int id)
		{
			var result = this.questions.Find(x => x.Id == id);
			if (result == null) throw new GameEngineException($"There is not any question with id {id}");

			return result;
		}

		internal bool ExistsQuestion(string shortcut)
		{
			return ExistQuestionBesidesIds(shortcut);
		}

		internal bool ExistsQuestion(string shortcut, int besidesOfId)
		{
			if (!ExistsQuestion(besidesOfId)) throw new GameEngineException($"Id {besidesOfId} does not exist");

			return ExistQuestionBesidesIds(shortcut, besidesOfId);
		}

		private bool ExistQuestionBesidesIds(string shortcut, params int[] besidesOf)
		{
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));

			var shortcutNormalized = shortcut.ToLower().Trim();
			var result = questions.Exists(x => !Array.Exists(besidesOf, id => id == x.Id) && String.Equals(x.Shortcut, shortcutNormalized, StringComparison.OrdinalIgnoreCase));
			return result;
		}

		internal bool ExistsQuestion(int id)
		{
			var result = this.questions.Exists(x => x.Id == id);
			return result;
		}

		const string ALL_SHORTCUTS = "all";
		internal IEnumerable<Question> QuestionsMatchingWith(string shortcut)
		{
			if (string.IsNullOrWhiteSpace(shortcut)) throw new ArgumentNullException(nameof(shortcut));

			var shortcutNormalized = shortcut.ToLower().Trim();
			var allQuestions = ALL_SHORTCUTS == shortcutNormalized;
			if (allQuestions) 
			{ 
				return questions.ToList();
			}

			var result = questions.Where(x => String.Equals(x.Shortcut, shortcutNormalized, StringComparison.OrdinalIgnoreCase)).ToList();
			return result;
		}

		internal IEnumerable<string> LineTypes
		{
			get
			{
				var types = new List<string>();
				foreach (LineType lineType in (LineType[])Enum.GetValues(typeof(LineType)))
				{
					if (lineType != LineType.MONEYDRAW_LINE)
					{
						types.Add(lineType.ToString());
					}
					else if (this.sport == Sport.SOCCER && Sport.SOCCER.AllowsTieResults())
					{
						types.Add(lineType.ToString());
					}
					else if (this.sport == Sport.HOCKEY && Sport.HOCKEY.AllowsTieResults())
					{
						types.Add(lineType.ToString());
					}
				}
				return types;
			}
		}

		internal void IncludeDomainForActivations(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			activations.IncludeNewDomainForAll(domain, false);
		}

		internal bool IsEnabled(Question question, Domain domain)
		{
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var result = activations.IsEnabled(question, domain);
			return result;
		}

		internal void EnableQuestion(Question question, Domain domain, string employeeName, DateTime now)
		{
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			activations.EnableDomain(question, domain, employeeName, now);
		}

		internal void DisableQuestion(Question question, Domain domain, string employeeName, DateTime now)
		{
			if (question == null) throw new ArgumentNullException(nameof(question));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			activations.DisableDomain(question, domain, employeeName, now);
		}

		internal DomainsActivation DomainsActivationFrom(Question question)
		{
			if (question == null) throw new ArgumentNullException(nameof(question));

			var result = activations.DomainsActivationFrom(question);
			return result;
		}

		internal int NextQuestionId()
		{
			return this.tournaments.NextQuestionId();
		}

		internal void SetLastQuestionId(int lastQuestionId)
		{
			this.tournaments.SetLastQuestionId(lastQuestionId);
		}

		internal ImageResource GetOrCreateBackground(string url)
        {
			if (string.IsNullOrEmpty(url)) throw new ArgumentNullException(nameof(url));

			if (this.backgrounds == null) this.backgrounds = new ImagesResource();
			return this.backgrounds.GetOrCreateImage(url);
        }

		internal Tournaments Tournaments
		{
			get
			{
				return this.tournaments;
			}
		}

		internal IEnumerable<Question> Questions
		{
			get
            {
				return questions;
			}
		}
	}
}
