﻿using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.exception;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using static town.connectors.Variables;

[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace town.connectors
{
    public sealed class CustomSettingsPerType
    {
        private Dictionary<string, CustomSettings> customSettingsPerType = new Dictionary<string, CustomSettings>();

        public void Add(Type type, CustomSettings dgscs)
        {
            customSettingsPerType.Add(type.FullName, dgscs);
        }

        internal CustomSettings Get(Type possibleType)
        {
            return customSettingsPerType[possibleType.FullName];
        }
    }

    public sealed class CustomSettings
    {
        private Dictionary<Variable, CustomSetting> fixedCustomSettings = new Dictionary<Variable, CustomSetting>();
        private Variables variables;

        private List<CustomSetting> _tempBeforePrepare;
        private CustomParameterCollection customParameterCollection;

        internal CustomParameterCollection CustomParameters
        {
            get
            {
                return customParameterCollection;
            }
        }
		public IEnumerable<CustomSetting> Settings 
        { 
            get 
            { 
                return _tempBeforePrepare; 
            }  
        }
        public bool AnyCustomSetting => _tempBeforePrepare != null && _tempBeforePrepare.Count != 0;
        public CustomSettings(Variables variables) 
        {
            this.variables = variables;
            customParameterCollection = new CustomParameterCollection(this);
        }

        public CustomSettings(Variables variables, int capacity)
        {
            this.variables = variables;
            customParameterCollection = new CustomParameterCollection(this, capacity);
        }

        public void Prepare()
        {
            if (_tempBeforePrepare == null) throw new ConnectorException("No parameter has been added.");

            customParameterCollection.Configure(_tempBeforePrepare.ToArray());
            _tempBeforePrepare.Clear();
        }

        public CustomSetting Get(DateTime now, string key)
        {
            bool changesApplied;
            return Get(now, key, out changesApplied);
        }

        public CustomSetting Get(DateTime now, string key, out bool changesApplied)
        {
            changesApplied = false;
            Variable variable;
            if (!variables.TryGetValue(key, out variable)) throw new ConnectorException($"{nameof(Variable)} {key} does not exists.");
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            var custom = fixedCustomSettings[variable];
            changesApplied = custom.ApplyChangeUntil(now);

            custom.Now = now;

            return custom;
        }

        private CustomSetting Get(string key)
        {
            if (!variables.TryGetValue(key, out Variable variable)) throw new ConnectorException($"{nameof(Variable)} {key} does not exists.");
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            if (!fixedCustomSettings.TryGetValue(variable, out CustomSetting custom)) throw new ConnectorException($"{nameof(CustomSetting)} {key} does not exists.");
            return custom;
        }

        public bool Exist(string key)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            var result = variables.TryGetValue(key, out _);
            return result;
        }

        public RecordSet ThreadUnsafeGetRecordSet()
        {
            RecordSet result = customParameterCollection.NextAvalaible();
            return result;
        }

        public CustomSetting AddVariableParameter(string key)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            if (_tempBeforePrepare == null) _tempBeforePrepare = new List<CustomSetting>();

            CustomSetting valueAlreadyStored = new VariableCustomSetting(this, _tempBeforePrepare.Count(), key, null);
            _tempBeforePrepare.Add(valueAlreadyStored);

            return valueAlreadyStored;
        }
        public CustomSetting AddFixedParameter(DateTime now, string key, object value)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            Variable variable;
            if (!variables.TryGetValue(key, out variable)) variable= variables.Create(key, value.GetType());

            if (variable.Type != value.GetType()) throw new ConnectorException($"{value.GetType()} is not valid");

            if (_tempBeforePrepare == null) _tempBeforePrepare = new List<CustomSetting>();

            CustomSetting valueAlreadyStored = null;
            if (fixedCustomSettings.TryGetValue(variable, out valueAlreadyStored))
            {
                valueAlreadyStored.Val = value;
            }
            else
            {
                valueAlreadyStored = new FixedCustomSetting(this, ordinalPosition: _tempBeforePrepare.Count(), key, value);
                fixedCustomSettings.Add(variable, valueAlreadyStored);
            }
            valueAlreadyStored.Now = now;

            _tempBeforePrepare.Add(valueAlreadyStored);

            CustomParameters.ClearPoolOfRecordSet();
            CustomParameters.AddCustomSetting(valueAlreadyStored);

            return valueAlreadyStored;
        }

        public CustomSetting ChangeValueStartingOn(DateTime dateToApplyTheChange, string key, object value, string user)
        {
            if (value == null) throw new ArgumentException(nameof(value));
            if (dateToApplyTheChange == default(DateTime)) throw new ArgumentException(nameof(dateToApplyTheChange));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentException(nameof(key));
            if (string.IsNullOrWhiteSpace(user)) throw new ArgumentException(nameof(user));

            CustomSetting valueAlreadyStored = Get(key);
            valueAlreadyStored.ChangeTo(dateToApplyTheChange, user, value);

            return valueAlreadyStored;
        }

        public int Count
        {
            get 
            {
                return fixedCustomSettings.Count;
            }
            
        }

        private int amountOfPendingChanges = 0;
        internal int AmountOfPendingChanges 
        {
            get
            { 
                return amountOfPendingChanges;
            }
            private set
            {
                if (value < 0) throw new ConnectorException($" {nameof(AmountOfPendingChanges)} ca not be negative.");
                amountOfPendingChanges = value;
            }
        }
        public bool ThereArePendingChanges { get { return AmountOfPendingChanges > 0; } }

        public abstract class CustomSetting
        {
            public string Key { get; private set; }
            protected object val;
            private List<Change> changesSortedByDate = new List<Change> ();
            private List<Change> logs = new List<Change>();

            public override string ToString()
            {
                return $"{Key}:{val.ToString()}";
            }

            public CustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val)
            {
                if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));
                if (customSettings == null) throw new ArgumentException(nameof(customSettings));

                this.ordinalPosition = ordinalPosition;
                this.Key = key;
                this.val = val;
                Version = 1;
                CustomSettings=customSettings;
            }

            public string Who { get; set; }
            public string Description { get; set; }
            public bool Enable { get; set; } = true;
            public bool ExternalVisibility { get; set; }
            public int AsInt { get { return (int)val; } }
            public float AsFloat { get { return (float)val; } }
            public double AsDouble { get { return Convert.ToDouble(val); } }
            public DateTime AsDateTime { get { return (DateTime)val; } }
            public string AsString { get { return val == null ? String.Empty : val.ToString(); } }
            public Secret AsSecret { get { return (Secret)val; } }
            public bool AsBool { get { return (bool)val; } }
            public byte AsByte { get { return (byte)val; } }
            public char AsChar { get { return (char)val; } }
            public decimal AsDecimal { get { return Convert.ToDecimal(val); } }
            public long AsLong { get { return (long)val; } }
            public short AsShort { get { return (short)val; } }
            public T As<T>()
            {
                return (T)val;
            }
            public T[] AsArray<T>()
            {
                object[] objects = val as object[];
                if (objects == null) throw new ConnectorException($"Value is a not the type.");

                var result = objects.OfType<T>();
                return result.ToArray();
            }
            public int ordinalPosition { get; }

            public object Val 
            {
                get 
                { 
                    return val;
                }
                internal set 
                {
                  
                    if (val == EMPTY_VALUE) val = value;
                    //if (value == EMPTY_VALUE) val = value;

                    if ( val!= null && ! HasValidTypeFor(value)) throw new ConnectorException($"Value is a not the type.");
                    val = value;
                } 
            }

            public bool IsEmpty { get { return val == EMPTY_VALUE; } }
            public object AsObject { get { return val; } }

            public int Version { get; private set; }
            public bool IsVariable { get; }
            public CustomSettings CustomSettings { get; }

            public string Type
            {
                get
                {
                    if (val == null) return nameof(String);
                    return val.GetType().Name;
                }
            }
            internal bool HasValidTypeFor(object value)
            {
                return val.GetType()==value.GetType();
            }
            private int version = 0;
            public void ChangeTo(DateTime dateToChangeValue, string user, object val)
            {
                if (default(DateTime) == dateToChangeValue) throw new ArgumentException(nameof(dateToChangeValue));
                if (Now > dateToChangeValue) throw new ConnectorException($"{nameof(dateToChangeValue)} can not be greater then {Now}");
                if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                if (val ==null) throw new ArgumentException(nameof(val));

                var change = new Change(dateToChangeValue, user, val, ++version);
                changesSortedByDate.Add(change);

                changesSortedByDate.Sort(delegate (Change x, Change y)
                {
                    int compare = x.DateToChangeValue.CompareTo(y.DateToChangeValue);
                    bool areExactlyTheSameDay = compare == 0;
                    return areExactlyTheSameDay ? x.Version - y.Version : compare;
                });

                CustomSettings.AmountOfPendingChanges++;
            }

            internal bool ApplyChangeUntil(DateTime now)
            {
                List<Change> changesIndexApplied = new List<Change>();
                for (int i=0; i<changesSortedByDate.Count;i++)
                {
                    var change = changesSortedByDate[i];
                    if (now >= change.DateToChangeValue)
                    {
                        Apply(change);
                        changesIndexApplied.Add(change);
                    }
                    else 
                    {
                        break;
                    }
                    
                }

                Prune(changesIndexApplied);

                int amountOfAppliedChanges = changesIndexApplied.Count;
                CustomSettings.AmountOfPendingChanges -= amountOfAppliedChanges;
                Version = Version + amountOfAppliedChanges;
                
                return amountOfAppliedChanges > 0;
            }

            public DateTime NextDateToChange()
            {
                if (changesSortedByDate.Count == 0) return DateTime.MaxValue;
                return changesSortedByDate.First().DateToChangeValue;
            }

            public object NextValue()
            {
                if (changesSortedByDate.Count == 0) throw new ConnectorException($"No scheduled value yet {Now}");
                return changesSortedByDate.First().Val;
            }

            public bool HasScheduledChange()
            {
                return changesSortedByDate.Count != 0;
            }

            private void Apply(Change change)
            {
                if (this is TownAttributeCustomSetting townAttibuteCS)
                {
                    townAttibuteCS.ActorKey = change.Val.ToString();
                }
                else
                {
                    Val = change.Val;
                }
                logs.Add(change);
            }

            private void Prune(List<Change> changesTobeDeleted)
            {
                foreach (Change change in changesTobeDeleted) changesSortedByDate.Remove(change);
            }

            internal IEnumerable<Change> Changes
            {
                get 
                {
                    return changesSortedByDate;
                }
                
            }
            internal IEnumerable<Change> Logs
            {
                get
                {
                    return logs;
                }

            }

            public DateTime Now { get; internal set; }

            internal struct Change
            {
                public Change(DateTime dateToChangeValue, string user, object val, int version)
                {
                    if (dateToChangeValue == default(DateTime)) throw new ArgumentException(nameof(dateToChangeValue));
                    if (string.IsNullOrEmpty(user)) throw new ArgumentException(nameof(user));
                    if (val == null) throw new ArgumentException(nameof(val));

                    DateToChangeValue = dateToChangeValue;
                    Val = val;
                    User = user;
                    Version = version;
                }

                public DateTime DateToChangeValue { get; }
                public object Val { get; }
                public string User { get; }
                public int Version { get; }
            }

            public struct Secret
            {
                private readonly string val;
                public Secret(string val)
                {
                    this.val = val;
                }

                public override string ToString() { return val; }
            }

            public virtual CustomSetting Clear()
            {
                this.val = null;
                return this;
            }
        }

        public abstract class DynamicCustomSetting : CustomSetting
        {
            public DynamicCustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val)
                : base(customSettings, ordinalPosition, key, val)
            {
            }

        }

        public sealed class TownAttributeCustomSetting : DynamicCustomSetting
        {
            private string actorKey;

            public TownAttributeCustomSetting(CustomSettings customSettings, string key) : base(customSettings, 0, key, EMPTY_VALUE)
            {
            }

            public string ActorKey
            {
                get
                {
                    return actorKey;
                }
                set
                {
                    if (string.IsNullOrWhiteSpace(value)) throw new ConnectorException($"{nameof(ActorKey)} can not be empty.");
                    actorKey = value;
                }
            }

            public bool IsActorKeyEmpty => string.IsNullOrWhiteSpace(ActorKey);

            public void ClearAttibute()
            {
                this.actorKey = null;
                this.val = EMPTY_VALUE;
            }

            public override CustomSetting Clear()
            {
                this.val = actorKey;
                return this;
            }
        }
        public sealed class VariableCustomSetting : DynamicCustomSetting
        {
            public VariableCustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val) :
                base(customSettings, ordinalPosition, key, val)
            {
                ExternalVisibility = true;
            }
        }
        public sealed class FixedCustomSetting : CustomSetting
        {
            public FixedCustomSetting(CustomSettings customSettings, int ordinalPosition, string key, object val) : 
                base(customSettings, ordinalPosition, key, val)
            {
            }
        }

        public bool ContainsKeyName(string val)
        {
            Variable key = fixedCustomSettings.Keys.FirstOrDefault(key => key.Name == val);
            return key != default(Variable);
        }

        private static object EMPTY_VALUE = new object();

        public CustomSetting AddFixedParameter(DateTime now, string key)
        {
            return AddFixedParameter(now, key, EMPTY_VALUE);
        }

        public CustomSetting AddAttributeParameter(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            Variable variable;
            if (!variables.TryGetValue(key, out variable))
            {
                variable = variables.Create(key, typeof(String));

                if (_tempBeforePrepare == null) _tempBeforePrepare = new List<CustomSetting>();

                CustomSetting newCS = new TownAttributeCustomSetting(this, key);

                fixedCustomSettings.Add(variable, newCS);

                CustomParameters.ClearPoolOfRecordSet();
                CustomParameters.AddCustomSetting(newCS);

                return newCS;
            }
            throw new ConnectorException($"ket with {key} was already added as an attribute.");
        }

        public void RemoveAttribute(DateTime now, string key)
        {
            if (now == default(DateTime)) throw new ArgumentException(nameof(now));
            if (string.IsNullOrEmpty(key)) throw new ArgumentException(nameof(key));

            if (!variables.TryGetValue(key, out Variable variable)) throw new ConnectorException($"{nameof(Variable)} {key} does not exists.");

            CustomSetting customSettingToBeRemove = fixedCustomSettings[variable];
            CustomParameters.RemoveCustomSetting(customSettingToBeRemove);

            fixedCustomSettings.Remove(variable);
            variables.Remove(key);

            CustomParameters.ClearPoolOfRecordSet();
        }

        internal class CustomParameterCollection
        {
            private ConcurrentDictionary<RecordSet, bool> poolOfRecordSet = new ConcurrentDictionary<RecordSet, bool>();
            private const int CAPACITY_LENGHT = 10;
            private readonly int capacity;

            private CustomSettings CustomSettings { get; }

            internal IEnumerable<RecordSet> Collection
            {
                get
                {
                    return poolOfRecordSet.Keys;
                }
            }
            public CustomParameterCollection(CustomSettings customSettings) :this(customSettings, CAPACITY_LENGHT)
            {
            }
            public CustomParameterCollection(CustomSettings customSettings, int capacity)
            {
                this.capacity = capacity;
                CustomSettings=customSettings;
            }

            private List<CustomSetting> template;

            internal void Configure(CustomSetting[] customSettings)
            {
                this.template = customSettings.ToList();

                for (int i = 0; i < capacity; i++)
                {
                    var registro = CreateNewRecordSet();
                    poolOfRecordSet.TryAdd(registro, false); // Se añade como disponible.
                }
            }

            internal void AddCustomSetting(CustomSetting customSetting)
            {
                if (customSetting == null) throw new ArgumentNullException(nameof(customSetting));
                if (template == null) return;
                template.Add(customSetting);
            }

            internal void RemoveCustomSetting(CustomSetting customSetting)
            {
                if (customSetting == null) throw new ArgumentNullException(nameof(customSetting));
                if (template == null) return;
                template.Remove(customSetting);
            }

            private RecordSet CreateNewRecordSet()
            {
                Dictionary<string, CustomSetting> maps;
                CustomSetting[] newCustomSettings;
                newCustomSettings = CloneOnlyVariableValues(this.template.ToArray(), out maps);

                int newId = poolOfRecordSet.Count();

                return new RecordSet(this)
                {
                    CustomSettings = newCustomSettings,
                    //IsBeenUsed = false,
                    Id = newId,
                    Mappings = maps
                }; // Crear una nueva instancia del objeto Registro.
            }

            internal RecordSet NextAvalaible()
            {
                foreach (var kvp in poolOfRecordSet)
                {
                    // Encontrar el primer Registro que esté disponible (false).
                    if (poolOfRecordSet.TryUpdate(kvp.Key, true, false))
                    {
                        // Comentario: Aquí hemos bloqueado el Registro, ya que lo hemos marcado como "en uso" (true).
                        return kvp.Key; // Se retorna el Registro como exclusivo para el flujo actual.
                    }
                }

                // Si no se encontró ninguno disponible, creamos uno nuevo.
                RecordSet newRecordSet = CreateNewRecordSet();
                poolOfRecordSet.TryAdd(newRecordSet, true); // El nuevo registro se marca como en uso.
                                                   // Comentario: Aquí hemos bloqueado el nuevo Registro que acabamos de crear, ya que lo hemos marcado como "en uso" (true).
                return newRecordSet;
            }

            public void ReleaseRecordSet(RecordSet recordSet)
            {
                // Intentar marcar el registro como disponible (false) si existe en el pool.
                if (poolOfRecordSet.ContainsKey(recordSet))
                {
                    ClearVariables(recordSet.CustomSettings);
                    poolOfRecordSet.AddOrUpdate(recordSet, false, (key, oldValue) => false);
                }
                // Comentario: Aquí hemos liberado el Registro, ya que lo hemos marcado como disponible (false).
            }

            private CustomSetting[] CloneOnlyVariableValues(CustomSetting[] customSettingsTemplate, out Dictionary<string, CustomSetting> maps)
            {
                CustomSetting[] newCustomSettings = new CustomSetting[customSettingsTemplate.Length];
                maps = new Dictionary<string, CustomSetting>();

                for (int i = 0; i < customSettingsTemplate.Length; i++)
                {
                    if (customSettingsTemplate[i].IsVariable)
                    {
                        newCustomSettings[i] = new VariableCustomSetting(
                            CustomSettings, 
                            customSettingsTemplate[i].ordinalPosition,
                            customSettingsTemplate[i].Key,
                            customSettingsTemplate[i].Val
                            );
                    }
                    else
                    {
                        newCustomSettings[i] = customSettingsTemplate[i];
                    }

                    maps.Add(newCustomSettings[i].Key, newCustomSettings[i]);
                }
                return newCustomSettings;
            }

            private void ClearVariables( CustomSetting[] result)
            {
                foreach (var item in result)
                {
                    if (item is DynamicCustomSetting) item.Clear();
                }
            }

            /// <summary>
            /// Se limpia el pool de recordSet y cuando se vuelva a solicitar un recordSet se llamara a CreateNewRecordSet();
            /// </summary>
            internal void ClearPoolOfRecordSet()
            {
                poolOfRecordSet.Clear();
            }
        }

        public sealed class RecordSet : IDisposable
        {
            public int Id { get; internal set; }
            public Dictionary<string, CustomSetting> Mappings { get; set; }
            internal CustomSetting[] CustomSettings { get; set; }

            private readonly CustomParameterCollection customParameterCollection;

            internal RecordSet(CustomParameterCollection customParameterCollection) 
            { 
                this.customParameterCollection = customParameterCollection;
            }

            public void Release()
            {
                this.customParameterCollection.ReleaseRecordSet(this);
            }
            public void Dispose()
            {
                Release();
            }


            public void SetParameter(CustomSetting customSetting, object val)
            {
                customSetting.Val = val;
            }

            public void SetParameter(string customSettingName, object val)
            {
                CustomSetting cs;
                if (Mappings.TryGetValue(customSettingName, out cs))
                {
                    cs.Val = val;
                }
            }

            private Dictionary<string, object> filterCustomSettings = new Dictionary<string, object>();
            public Dictionary<string, object> CustomSettingsForExternal()
            {
                filterCustomSettings.Clear();
                foreach (string keyCustomSetting in this.Mappings.Keys)
                {
                    var customSetting = this.Mappings[keyCustomSetting];
                    if (customSetting.Enable && !customSetting.IsEmpty && customSetting.ExternalVisibility)
                    {
                        filterCustomSettings.Add(keyCustomSetting,customSetting.Val);
                    }
                }

                return filterCustomSettings;
            }

            private string LogObjectToJson(object obj)
            {
                string serializedData = "";
                try
                {
                     serializedData = JsonConvert.SerializeObject(obj, Formatting.Indented);
                }
                catch (Exception ex)
                {
                    //log.Error("Error serializando objeto", ex);
                }
                return serializedData;
            }

            public override string ToString()
            {
                StringBuilder recordSetStringBuilder = new StringBuilder();
                foreach (string keyCustomSetting in this.Mappings.Keys)
                {
                    var customSetting = this.Mappings[keyCustomSetting];

                    if (!customSetting.ExternalVisibility) continue;

                    if (customSetting.Val is Secret)
                    {
                        recordSetStringBuilder.AppendLine($"Key={keyCustomSetting} Value=****");
                    }
                    else
                    {
                        recordSetStringBuilder.AppendLine($"Key={keyCustomSetting} Value={LogObjectToJson(customSetting.Val)}");
                    }
                }
                return recordSetStringBuilder.ToString();
            }

            public bool ContainsKeyName(string v)
            {
                return Mappings.ContainsKey(v);
            }

            internal void AddParameter(CustomSetting newCustomSetting)
            {
                Mappings.Add(newCustomSetting.Key, newCustomSetting);
            }
        }
    }

    public sealed class Variables
    {
        private Dictionary<string, Variable> variables = new Dictionary<string, Variable>();

        internal bool TryGetValue(string key, out Variable variable)
        {
            return variables.TryGetValue(key, out variable);
        }
        internal Variable Create(string key, Type type)
        {
            if (variables.ContainsKey(key)) throw new ConnectorException($"{nameof(Variable)} {key} already exists.");

            var result = new Variable(key, type);
            variables.Add(key, result);

            return result;
        }

        internal bool Remove(string key)
        {
            return variables.Remove(key);
        }

        internal int Count
        {
            get
            {
                return variables.Count;
            }
            
        }

        public struct Variable
        {
            public Variable(string name, Type type)
            {
                if (string.IsNullOrEmpty(name)) throw new ArgumentException(nameof(name));

                Name = name;
                Type = type;
            }

            public string Name { get; }
            public Type Type { get; }

            public static bool operator ==(Variable c1, Variable c2)
            {
                return c1.Equals(c2);
            }

            public static bool operator !=(Variable c1, Variable c2)
            {
                return !c1.Equals(c2);
            }
        }
    }
}
