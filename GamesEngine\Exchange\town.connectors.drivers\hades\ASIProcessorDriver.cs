﻿using Connectors.town.connectors.drivers;
using log4net;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.hades
{
    public abstract class ASITenantDriver : TenantDriver
    {
		private const float VERSION = 1.0F;
		public const int FAKE_TICKET_NUMBER = -1;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected const int MAX_RETRIES = 3;
		protected int fakeTicketNumber = FAKE_TICKET_NUMBER;

        public ASITenantDriver(Tenant_Actions tenantAction, TransactionType transactionType, string currencyCode)
            : base(tenantAction, transactionType, "Hades", PaymentMethod.ThirdParty, currencyCode, VERSION)
        {
        }

        public override string Description => "Hades driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019,01,01);

		internal const string PRODUCTION_DOES_NOT_NEED_IT = "PRODUCTION_DOES_NOT_NEED_IT";
		internal static string UniqueIdentifierPrefixForLottoReferenceId = System.Guid.NewGuid().ToString().Replace("-", "").Substring(0, 24);
		internal static string NeedsUniqueIdentifierForPaymentHub { get; set; } = PRODUCTION_DOES_NOT_NEED_IT;
		
		protected PostFreeFormTicketAndWagersResponse CreateFakePostFreeFormTicketAndWagersResponse(List<PostFreeFormWager> wagers)
		{
			var fakeResponse = new PostFreeFormTicketAndWagersResponse()
			{
				TicketNumber = fakeTicketNumber,
				Wagers = wagers.ToArray()
			};
			for (int index = 0; index < fakeResponse.Wagers.Length; index++)
			{
				var wager = fakeResponse.Wagers[index];
				wager.WagerNumber = (index + 1).ToString();
			}
			fakeTicketNumber--;
			return fakeResponse;
		}
		protected PostFreeFormWagerCollectionWagers CreateEmptyGetTicketWagers()
		{
			var postFreeFormWagerCollectionWagers = new PostFreeFormWagerCollectionWagers();
			return postFreeFormWagerCollectionWagers;
		}	
	}
	public abstract class ASIProcessorDriver : ProcessorDriver
	{
		public const int FAKE_TICKET_NUMBER = -1;
		protected const int FAKE_DOCUMENT_NUMBER = -1;
		protected const int MAX_RETRIES = 3;
		protected int fakeTicketNumber = FAKE_TICKET_NUMBER;

		public ASIProcessorDriver(TransactionType transactionType, float version)
		: base("Hades", PaymentMethod.ThirdParty, "USD", transactionType, version)
		{
		}

		private enum TranType
		{
			E,
			I,
			C,
			D,
			F,
			H,
			B,
			N,
			T,
			U
		}

		public override string Description => "Hades driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);
	}
	internal sealed class ASIJsonUtils
	{
		internal static List<List<T>> SplitList<T>(List<T> items, int nSize = 30)
		{
			var list = new List<List<T>>();

			for (int i = 0; i < items.Count; i += nSize)
			{
				list.Add(items.GetRange(i, Math.Min(nSize, items.Count - i)));
			}

			return list;
		}
		internal static async Task<string> ToJsonAsync(object instance)
		{
			using (MemoryStream mst = new MemoryStream())
			{
				var serializer = new DataContractJsonSerializer(instance.GetType());
				serializer.WriteObject(mst, instance);
				mst.Position = 0;

				using (StreamReader r = new StreamReader(mst))
				{
					return await r.ReadToEndAsync();
				}
			}
		}
		internal static string HideSensitiveData(MessageToAccountingServices obj, Logger logger = null)
		{
			try
			{
				string passwordtemp = obj.SystemPassword;
				obj.SystemPassword = "XXXX";
				string json = Commons.ToJson(obj);
				obj.SystemPassword = passwordtemp;
				return json;
			}
			catch (Exception e)
			{
				logger?.Error($@"errortype:{e.GetType()} errormessage:{e.Message}", e);
			}
			return "";
		}
		internal static async Task<string> HideSensitiveDataAsync(MessageToAccountingServices obj, Logger logger)
		{
			try
			{
				string passwordtemp = obj.SystemPassword;
				obj.SystemPassword = "XXXX";
				string json = await ToJsonAsync(obj);
				obj.SystemPassword = passwordtemp;
				return json;
			}
			catch (Exception e)
			{
				logger.Error($@"errortype:{e.GetType()} errormessage:{e.Message}", e);
			}
			return "";
		}
		internal static string HideSensitiveData(GradeFreeFormWagerCollectionBody obj, Logger logger)
		{
			try
			{
				string passwordtemp = obj.Collection.SystemPassword;
				obj.Collection.SystemPassword = "XXXX";
				string json = Commons.ToJson(obj);
				obj.Collection.SystemPassword = passwordtemp;
				return json;
			}
			catch (Exception e)
			{
				logger.Error($@"errortype:{e.GetType()} errormessage:{e.Message}", e);
			}
			return "";

		}
	}

	public struct Execution
	{
		public Execution(TransactionStatus status, string details)
		{
			Status = status;
			Details = details;
		}

		public TransactionStatus Status { get; }
		public string Details { get; }

		public static bool operator ==(Execution op1, Execution op2)
		{
			return op1.Status == op2.Status && op1.Details == op2.Details;
		}

		public static bool operator !=(Execution op1, Execution op2)
		{
			return op1.Status != op2.Status || op1.Details != op2.Details;
		}
	}
	
	public struct WithdrawReponse
	{
		public WithdrawReponse(int authorizationId, TransactionStatus status, string processorKeyUsed)
		{
			Status = status;
			AuthorizationId = authorizationId;
			ProcessorKeyUsed = processorKeyUsed;
		}

		public TransactionStatus Status { get; }
		public int AuthorizationId { get; }
		public string ProcessorKeyUsed { get; private set; }
	}
}
