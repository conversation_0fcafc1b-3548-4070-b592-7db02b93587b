﻿using ClickHouse.Client.ADO;
using ClickHouse.Client.Copy;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public interface IAppConnection : IDisposable
    {
        IAppCommand CreateCommand();
    }

    public interface IAppCommand : IDisposable
    {
        string CommandText { get; set; }
        void AddParameter(string name, object value, DbType dbType);
        Task<int> ExecuteNonQueryAsync(CancellationToken cancellationToken = default);
        Task<DbDataReader> ExecuteReaderAsync(CancellationToken cancellationToken = default);
    }

    public class AppConnectionWrapper : IAppConnection
    {
        private readonly IDbConnection _realConnection;

        public AppConnectionWrapper(IDbConnection realConnection)
        {
            _realConnection = realConnection;
        }

        public IAppCommand CreateCommand()
        {
            return new AppCommandWrapper(_realConnection.CreateCommand());
        }

        public void Dispose()
        {
            _realConnection?.Dispose();
        }
    }

    public class AppCommandWrapper : IAppCommand
    {
        private readonly IDbCommand _realCommand;

        public AppCommandWrapper(IDbCommand realCommand)
        {
            _realCommand = realCommand;
        }

        public string CommandText
        {
            get => _realCommand.CommandText;
            set => _realCommand.CommandText = value;
        }

        public void AddParameter(string name, object value, DbType dbType)
        {
            var parameter = _realCommand.CreateParameter();
            parameter.ParameterName = name;
            parameter.Value = value;
            parameter.DbType = dbType;
            _realCommand.Parameters.Add(parameter);
        }

        public Task<int> ExecuteNonQueryAsync(CancellationToken cancellationToken = default)
        {
            if (_realCommand is DbCommand dbCmd)
            {
                return dbCmd.ExecuteNonQueryAsync(cancellationToken);
            }
            throw new NotSupportedException("The underlying command does not support async operations.");
        }

        public async Task<DbDataReader> ExecuteReaderAsync(CancellationToken cancellationToken = default)
        {
            if (_realCommand is DbCommand dbCmd)
            {
                return await dbCmd.ExecuteReaderAsync(cancellationToken);
            }
            throw new NotSupportedException("The underlying command does not support async operations.");
        }

        public void Dispose()
        {
            _realCommand?.Dispose();
        }
    }

    public interface IAppBulkCopy : IDisposable
    {
        void Configure(string destinationTableName, int batchSize);

        Task InitAsync(CancellationToken cancellationToken = default);
        Task WriteToServerAsync(IEnumerable<object[]> data, CancellationToken cancellationToken = default);
    }

    public class AppOlapBulkCopyWrapper : IAppBulkCopy
    {
        private ClickHouseBulkCopy _realBulkCopy;
        private readonly ClickHouseConnection _connection;

        public AppOlapBulkCopyWrapper(ClickHouseConnection connection)
        {
            _connection = connection;
        }

        public void Configure(string destinationTableName, int batchSize)
        {
            // This logic remains the same
            _realBulkCopy = new ClickHouseBulkCopy(_connection)
            {
                DestinationTableName = destinationTableName,
                BatchSize = batchSize
            };
        }

        public async Task InitAsync(CancellationToken cancellationToken = default)
        {
            if (_realBulkCopy == null) throw new InvalidOperationException("Bulk copy not configured. Call Configure first.");
            await _realBulkCopy.InitAsync();
        }

        public async Task WriteToServerAsync(IEnumerable<object[]> data, CancellationToken cancellationToken = default)
        {
            if (_realBulkCopy == null) throw new InvalidOperationException("Bulk copy not configured. Call Configure first.");
            await _realBulkCopy.WriteToServerAsync(data, cancellationToken);
        }

        public void Dispose()
        {
            _realBulkCopy?.Dispose();
        }
    }
}
