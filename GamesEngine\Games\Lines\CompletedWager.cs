﻿using GamesEngine.Business;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedWager: Objeto
    {
        internal int AuthorizationId { get; }

        internal string AccountNumber { get; }

        internal decimal Risk { get; }

        internal decimal ToWin { get; }

        internal GradingStatus Grading { get; }

        internal WagerStatus WagerStatus { get; }

        internal CompletedLine Line { get; }

        internal CompletedScoreGame Game => Line.Game;

        internal string ChosenAnswer { get; }

        internal int DomainId { get; }

        public CompletedWager(CompletedLine line, int authorizationId, string accountNumber, decimal toWin, decimal risk, GradingStatus gradingStatus, WagerStatus status, string chosenAnswer)
        {
            if (line == null) throw new ArgumentNullException(nameof(line));
            if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (toWin < 0) throw new GameEngineException($"{nameof(toWin)} must be greater than 0");
            if (risk <= 0) throw new GameEngineException($"{nameof(risk)} must be greater than 0");
            if (gradingStatus == GradingStatus.PENDING) throw new GameEngineException($"{nameof(CompletedWager)} cannot be {nameof(Games.GradingStatus.PENDING)}.");
            if (toWin == 0 && status == WagerStatus.W) throw new GameEngineException($"{nameof(CompletedWager)} winner cannot have {nameof(toWin)} 0.");
            //if (toWin != 0 && status == WagerStatus.L) throw new GameEngineException($"{nameof(CompletedWager)} loser cannot have {nameof(toWin)} different than 0.");

            Line = line;
            AuthorizationId = authorizationId;
            AccountNumber = accountNumber;
            ToWin = status == WagerStatus.W ? toWin: 0;
            Risk = risk;
            Grading = gradingStatus;
            WagerStatus = status;
            ChosenAnswer = chosenAnswer;
        }

        internal decimal Profit()
        {
            return Risk - ToWin;
        }

        internal bool IsWinner() 
        {
            var result = ToWin > 0 && WagerStatus == WagerStatus.W;
            return result;
        }

        internal string ChosenAnswerAsText()
        {
            switch (ChosenAnswer)
            {
                case "A":
                    return Line.Game.TeamAName;
                case "B":
                    return Line.Game.TeamBName;
                case "D":
                    return "Draw";
                case "O":
                    return CompletedOverUnderLine.OVER_ANSWER;
                case "U":
                    return CompletedOverUnderLine.UNDER_ANSWER;
                case "Y":
                    return CompletedYesNoLine.YES_ANSWER;
                case "N":
                    return CompletedYesNoLine.NO_ANSWER;
                default:
                    throw new GameEngineException($"{nameof(ChosenAnswer)} '{ChosenAnswer}' is not implemented.");
            }
        }

        internal int ChosenAnswerReward()
        {
            switch (ChosenAnswer)
            {
                case "A":
                    return ((CompletedABLine)Line).GetReward('A');
                case "B":
                    return ((CompletedABLine)Line).GetReward('B');
                case "D":
                    return ((CompletedMoneyDrawLine)Line).GetReward('D');
                case "O":
                    return ((CompletedOverUnderLine)Line).GetReward(OverUnderAnswer.OVER);
                case "U":
                    return ((CompletedOverUnderLine)Line).GetReward(OverUnderAnswer.UNDER);
                case "Y":
                    return ((CompletedYesNoLine)Line).GetReward(YesNoAnswer.YES);
                case "N":
                    return ((CompletedYesNoLine)Line).GetReward(YesNoAnswer.NO);
                default:
                    throw new GameEngineException($"{nameof(ChosenAnswer)} '{ChosenAnswer}' is not implemented.");
            }
        }
    }
}
