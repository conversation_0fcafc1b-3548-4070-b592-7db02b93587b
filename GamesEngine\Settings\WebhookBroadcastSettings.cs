﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Svix;
using Svix.Models;
using Puppeteer.EventSourcing;

namespace GamesEngine.Settings
{
    public class WebhookBroadcastSettings
    {
        public static void Configure(IConfiguration configuration)
        {
            var setup = configuration.GetSection("Svix");
            if (setup == null) throw new ArgumentNullException("Svix applications are not defined in appsettings.");

            ApiUrl = setup.GetValue<string>("ApiUrl");
            if (string.IsNullOrEmpty(ApiUrl)) throw new ArgumentException("Svix.ApiUrl is not configured or is empty.");
            if (!Uri.IsWellFormedUriString(ApiUrl, UriKind.Absolute)) throw new ArgumentException($"Svix.ApiUrl '{ApiUrl}' is not a valid absolute URI.");

            ApiToken = setup.GetValue<string>("ApiToken");
            if (string.IsNullOrEmpty(ApiToken)) throw new ArgumentException("Svix.ApiToken is not configured or is empty.");

            ApplicationId = setup.GetValue<string>("ApplicationId");
            if (string.IsNullOrEmpty(ApplicationId)) throw new ArgumentException("Svix.TransactionAppId is not configured or is empty.");

            ApplicationName = setup.GetValue<string>("ApplicationName");
            if (string.IsNullOrEmpty(ApplicationName)) throw new ArgumentException("Svix.ApplicationName is not configured or is empty.");

            SvixClient = new SvixClient(ApiToken, new SvixOptions(ApiUrl));
        }

        private static SvixClient SvixClient { get; set; }

        private static string ApiUrl { get; set; }

        private static string ApiToken { get; set; }

        private static string ApplicationId { get; set; }
        private static string ApplicationName { get; set; }

        public class SvixApplication
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }

        public static async Task<string> BroadcastWebhookAsync(string eventType, string channel, Dictionary<string, object> payload)
        {
            if (string.IsNullOrEmpty(eventType)) throw new ArgumentException("Event type cannot be null or empty.", nameof(eventType));
            if (string.IsNullOrEmpty(channel)) throw new ArgumentException("Channel cannot be null or empty.", nameof(channel));
            if (payload == null || !payload.Any()) throw new ArgumentException("Payload cannot be null or empty.", nameof(payload));

            string resultId = string.Empty;
            try
            {
                var message = new MessageIn
                {
                    EventType = eventType,
                    Payload = payload,
                    Channels = new List<string> { channel }
                };

                var result = await SvixClient.Message.CreateAsync(ApplicationId, message);
                resultId = result.Id;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to broadcast webhook.", ex);
            }
            return resultId;
        }

        public static async Task<bool> UpdateEndpointAsync(string endpointId, string description, List<string> channels, List<string> filterTypes)
        {
            if (string.IsNullOrEmpty(endpointId)) throw new ArgumentException("Endpoint id cannot be null or empty.", nameof(endpointId));
            if (channels == null || !channels.Any()) throw new ArgumentException("Channels cannot be null or empty.", nameof(channels));
            if (filterTypes == null || !filterTypes.Any()) throw new ArgumentException("Filter types cannot be null or empty.", nameof(filterTypes));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException("Description cannot be null or empty.", nameof(description));
            
            try
            {
                var endpointPatch = new EndpointPatch
                {
                    Description = description,
                    Channels = channels,
                    FilterTypes = filterTypes,
                };
                await SvixClient.Endpoint.PatchAsync(ApplicationId, endpointId, endpointPatch);

            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to update webhook endpoint.", ex);
                return false;
            }
            return true;
        }

        public static async Task<string> RegisterEndpointAsync(string urlEndpoint, List<string> channels, List<string> filterTypes, Dictionary<string, string> headers, Dictionary<string, string> metadata, string description)
        {
            if (string.IsNullOrEmpty(urlEndpoint)) throw new ArgumentException("URL endpoint cannot be null or empty.", nameof(urlEndpoint));
            if (channels == null || !channels.Any()) throw new ArgumentException("Channels cannot be null or empty.", nameof(channels));
            if (filterTypes == null || !filterTypes.Any()) throw new ArgumentException("Filter types cannot be null or empty.", nameof(filterTypes));
            if (headers == null || !headers.Any()) throw new ArgumentException("Headers cannot be null or empty.", nameof(headers));
            if (metadata == null || !metadata.Any()) throw new ArgumentException("Metadata cannot be null or empty.", nameof(metadata));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException("Description cannot be null or empty.", nameof(description));

            string endpointId = string.Empty;
            try
            {
                var endpoint = new EndpointIn
                {
                    Url = urlEndpoint,
                    Description = description,
                    Metadata = metadata,
                    Channels = channels,
                    FilterTypes = filterTypes,
                };
                var result = await SvixClient.Endpoint.CreateAsync(ApplicationId, endpoint);
                endpointId = result.Id;

                var headersObj = new EndpointHeadersPatchIn
                {
                    Headers = headers
                };
                
                bool done = await SvixClient.Endpoint.PatchHeadersAsync(ApplicationId, endpointId, headersObj);
                if (!done)
                {
                    Loggers.GetIntance().WebHook.Debug($"Failed to set headers for webhook endpoint with ID {endpointId}.");
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to register webhook endpoint.", ex);
                if (!string.IsNullOrEmpty(endpointId))
                {
                    try
                    {
                        await SvixClient.Endpoint.DeleteAsync(ApplicationId, endpointId);
                    }
                    catch (Exception deleteEx)
                    {
                        Loggers.GetIntance().WebHook.Error($"Failed to clean up webhook endpoint with ID {endpointId} after registration failure.", deleteEx);
                    }
                }
            }
            return endpointId;
        }

        public static async Task<List<WrapperEndpoint>> EndpointListAsync()
        {
            List<WrapperEndpoint> endpoints = new List<WrapperEndpoint>();
            try
            {
                var result = await SvixClient.Endpoint.ListAsync(ApplicationId);
                foreach (var endpoint in result.Data)
                {
                    endpoints.Add(new WrapperEndpoint
                    {
                        Id = endpoint.Id,
                        Description = endpoint.Description,
                        Url = endpoint.Url,
                        FilterTypes = endpoint.FilterTypes?.ToList() ?? new List<string>(),
                        Channels = endpoint.Channels?.ToList() ?? new List<string>(),
                        Metadata = endpoint.Metadata ?? new Dictionary<string, string>(),
                        Disabled = (bool)endpoint.Disabled,
                        CreatedAt = endpoint.CreatedAt
                    });
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to retrieve webhook endpoints.", ex);
            }
            return endpoints;
        }

        public static async Task<bool> EnabledEndpointAsync(string endpointId, bool enabled)
        {
            if (string.IsNullOrEmpty(endpointId)) throw new ArgumentException("Endpoint ID cannot be null or empty.", nameof(endpointId));

            try
            {
                var currentEndpoint = await SvixClient.Endpoint.GetAsync(ApplicationId, endpointId);

                var endpointPatch = new EndpointPatch
                {
                    Disabled = !enabled
                };

                await SvixClient.Endpoint.PatchAsync(ApplicationId, endpointId, endpointPatch);
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error($"Failed to disable webhook endpoint with ID {endpointId}.", ex);
                return false;
            }
            return true;
        }

        public static async Task<bool> DeleteEndpointAsync(string endpointId)
        {
            if (string.IsNullOrEmpty(endpointId)) throw new ArgumentException("Endpoint ID cannot be null or empty.", nameof(endpointId));
            try
            {
                await SvixClient.Endpoint.DeleteAsync(ApplicationId, endpointId);
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error($"Failed to delete webhook endpoint with ID {endpointId}.", ex);
                return false;
            }
            return true;
        }

        public static async Task<bool> CreateEventTypeAsync(string name, string description, string groupName)
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentException("Event type name cannot be null or empty.", nameof(name));
            if (string.IsNullOrEmpty(description)) throw new ArgumentException("Event type description cannot be null or empty.", nameof(description));
            if (string.IsNullOrEmpty(groupName)) throw new ArgumentException("Event type group name cannot be null or empty.", nameof(groupName));

            try
            {
                var eventType = new EventTypeIn
                {
                    Name = name,
                    Description = description,
                    //GroupName = groupName
                    FeatureFlag = groupName
                };
                await SvixClient.EventType.CreateAsync(eventType);
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to create event type.", ex);
                return false;
            }
            return true;
        }

        public static async Task<List<WrapperEventType>> EventTypesAsync()
        {
            var result = new List<WrapperEventType>();
            try
            {
                var eventTypes = await SvixClient.EventType.ListAsync();
                foreach (var eventType in eventTypes.Data)
                {
                    result.Add(new WrapperEventType
                    {
                        Name = eventType.Name,
                        Description = eventType.Description,
                        //GroupName = eventType.GroupName
                        GroupName = eventType.FeatureFlag
                    });
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error("Failed to retrieve event types.", ex);
            }
            return result;
        }

        public static async Task<List<WrapperEventType>> EventTypesAsync(string groupName)
        {
            if (string.IsNullOrEmpty(groupName)) throw new ArgumentException("Group name cannot be null or empty.", nameof(groupName));
            
            var result = new List<WrapperEventType>();
            try
            {
                var eventTypes = await SvixClient.EventType.ListAsync();
                foreach (var eventType in eventTypes.Data)
                {
                    //if (groupName.Equals(eventType.GroupName))
                    if (groupName.Equals(eventType.FeatureFlag))
                    {
                        result.Add(new WrapperEventType
                        {
                            Name = eventType.Name,
                            Description = eventType.Description,
                            GroupName = eventType.FeatureFlag
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().WebHook.Error($"Failed to retrieve event types for group '{groupName}'.", ex);
            }
            return result;
        }

        public class WrapperEventType
        {
            public string Name { get; set; }
            public string Description { get; set; }
            public string GroupName { get; set; }
        }

        public class WrapperEndpoint
        {
            public string Description { get; set; }
            public string Url { get; set; }
            public List<string> FilterTypes { get; set; }
            public List<string> Channels { get; set; }
            public string Id { get; set; }
            public Dictionary<string, string> Metadata { get; set; }
            public bool Disabled { get; set; }
            public DateTime CreatedAt { get; set; }
        }
    }
}
