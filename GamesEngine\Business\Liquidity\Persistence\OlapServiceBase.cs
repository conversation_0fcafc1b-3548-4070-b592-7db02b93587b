﻿using ClickHouse.Client.ADO.Parameters;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public abstract class OlapServiceBase
    {
        protected readonly IDbConnectionFactory _connectionFactory;

        protected OlapServiceBase(IDbConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        }

        protected async Task ExecuteNonQueryAsync(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            try
            {
                using (var connection = await _connectionFactory.CreateAndOpenConnectionAsync())
                {
                    using (var appCommand = connection.CreateCommand())
                    {
                        appCommand.CommandText = sql;
                        if (parameters != null)
                        {
                            foreach (var p in parameters)
                            {
                                appCommand.AddParameter(p.ParameterName, p.Value, p.DbType);
                            }
                        }
                        await appCommand.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"Olap Error executing non-query: {sql}", ex);
                ErrorsSender.Send(ex, sql);
                throw;
            }
        }

        protected void ExecuteNonQuery(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            ExecuteNonQueryAsync(sql, parameters).GetAwaiter().GetResult();
        }
    }
}
