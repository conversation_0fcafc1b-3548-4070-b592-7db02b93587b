﻿using GamesEngine.Business;
using GamesEngine.Settings;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.drivers;
using town.connectors.drivers.fiero;
using static GamesEngine.Exchange.town.connectors.drivers.fierro.processors.UnlockDeposit;

namespace GamesEngine.Exchange.town.connectors.drivers.fierro.processors
{
    internal abstract class UnlockAndDebitDeposit : FierroProcessorDriver, IDriverUserProperties
    {
        private const float VERSION = 1.0F;

        public static TokenDriver CashierToken { get; set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private HttpClient _postTransactionClient;

        internal UnlockAndDebitDeposit(string currencyCode) : base(TransactionType.UnlockDebit, VERSION, currencyCode, PaymentMethod.ThirdParty)
        {
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("sourceNumber");
            CustomSettings.AddVariableParameter("sourceName");
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("documentNumber");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("reference");
            CustomSettings.AddVariableParameter("processorId");

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            string cahiserUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            if (_postTransactionClient == null || _postTransactionClient.BaseAddress.ToString() != cahiserUrl)
            {
                _postTransactionClient = new HttpClient();
                _postTransactionClient.BaseAddress = new Uri(cahiserUrl);
            }

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = Refund.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Refund.CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (Refund.CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) Refund.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            TransactionMovementBody body = new TransactionMovementBody
            {
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                Currency = currencyCode,
                Amount = recordSet.Mappings["amount"].AsDecimal,
                SourceNumber = recordSet.Mappings["sourceNumber"].AsInt,
                SourceName = recordSet.Mappings["sourceName"].AsString,
                AccountNumber = recordSet.Mappings["accountNumber"].AsString,
                Who = recordSet.Mappings["who"].AsString,
                DocumentNumber = recordSet.Mappings["documentNumber"].AsString,
                Store = recordSet.Mappings["storeId"].AsInt,
                Concept = recordSet.Mappings["concept"].AsString,
                Reference = recordSet.Mappings["reference"].AsString,
                ProcessorId = recordSet.Mappings["processorId"].AsInt
            };

            bool unlockAndDepositDone = await UnlockAndDepositAsync(body);
            var result = new DoneResponse(unlockAndDepositDone, Entity, PaymentMethod, CurrencyIsoCodes, TransactionTypeEnum.Value);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        private async Task<bool> UnlockAndDepositAsync(TransactionMovementBody body)
        {
            string url = "api/deposit/unlockAndDebit";

            if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
            {
                _postTransactionClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Refund.CashierToken.access_token);
            }

            string jsonString = System.Text.Json.JsonSerializer.Serialize(body);
            var content = new StringContent(jsonString, System.Text.Encoding.UTF8, "application/json");

            using (HttpResponseMessage response = await _postTransactionClient.PostAsync(url, content))
            {
                string responseString = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    return true;
                }
                return false;
            }
        }
    }
}
