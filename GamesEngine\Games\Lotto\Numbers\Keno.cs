﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{       
    internal struct Keno : IPick
    {
        private readonly KenoBall[] numbers;
        private Multiplier multiplier;
        private Bulleye bulleye;
        private bool isTenNumbers;
        private bool isTwelfthNumbers;
        private <PERSON><PERSON>(<PERSON><PERSON><PERSON><PERSON> pair1, <PERSON><PERSON><PERSON><PERSON> pair2, <PERSON><PERSON><PERSON><PERSON> pair3, <PERSON><PERSON><PERSON><PERSON> pair4, <PERSON><PERSON><PERSON><PERSON> pair5, <PERSON><PERSON><PERSON><PERSON> pair6, <PERSON><PERSON><PERSON><PERSON> pair7, <PERSON><PERSON><PERSON><PERSON> pair8, <PERSON><PERSON><PERSON><PERSON> pair9, <PERSON><PERSON><PERSON><PERSON> pair10, <PERSON><PERSON><PERSON><PERSON> pair11, <PERSON><PERSON><PERSON><PERSON> pair12, Multiplier multiplier, Bulleye bulleye)
        {
            if (
                pair1 == pair2 || pair1 == pair3 || pair1 == pair4 || pair1 == pair5 || pair1 == pair6 || pair1 == pair7 || pair1 == pair8 || pair1 == pair9 || pair1 == pair10 || pair1 == pair11 || pair1 == pair12 ||
                pair2 == pair1 || pair2 == pair3 || pair2 == pair4 || pair2 == pair5 || pair2 == pair6 || pair2 == pair7 || pair2 == pair8 || pair2 == pair9 || pair2 == pair10 || pair2 == pair11 || pair2 == pair12 ||
                pair3 == pair1 || pair2 == pair3 || pair3 == pair4 || pair3 == pair5 || pair3 == pair6 || pair3 == pair7 || pair3 == pair8 || pair3 == pair9 || pair3 == pair10 || pair3 == pair11 || pair3 == pair12 ||
                pair4 == pair1 || pair2 == pair4 || pair3 == pair4 || pair4 == pair5 || pair4 == pair6 || pair4 == pair7 || pair4 == pair8 || pair4 == pair9 || pair4 == pair10 || pair4 == pair11 || pair4 == pair12 ||
                pair5 == pair1 || pair2 == pair5 || pair3 == pair5 || pair4 == pair5 || pair5 == pair6 || pair5 == pair7 || pair5 == pair8 || pair5 == pair9 || pair5 == pair10 || pair5 == pair11 || pair5 == pair12 ||
                pair6 == pair1 || pair2 == pair6 || pair3 == pair6 || pair4 == pair6 || pair5 == pair6 || pair6 == pair7 || pair6 == pair8 || pair6 == pair9 || pair6 == pair10 || pair6 == pair11 || pair6 == pair12 ||
                pair7 == pair1 || pair2 == pair7 || pair3 == pair7 || pair4 == pair7 || pair5 == pair7 || pair6 == pair7 || pair7 == pair8 || pair7 == pair9 || pair7 == pair10 || pair7 == pair11 || pair7 == pair12 ||
                pair8 == pair1 || pair2 == pair8 || pair3 == pair8 || pair4 == pair8 || pair5 == pair8 || pair6 == pair8 || pair7 == pair8 || pair8 == pair9 || pair8 == pair10 || pair8 == pair11 || pair8 == pair12 ||
                pair9 == pair1 || pair2 == pair9 || pair3 == pair9 || pair4 == pair9 || pair5 == pair9 || pair6 == pair9 || pair7 == pair9 || pair8 == pair9 || pair9 == pair10 || pair9 == pair11 || pair9 == pair12 ||
                pair10 == pair1 || pair2 == pair10 || pair3 == pair10 || pair4 == pair10 || pair5 == pair10 || pair6 == pair10 || pair7 == pair10 || pair8 == pair10 || pair9 == pair10 || pair10 == pair11 || pair10 == pair12 || 
                pair11 == pair1 || pair2 == pair11 || pair3 == pair11 || pair4 == pair11 || pair5 == pair11 || pair6 == pair11 || pair7 == pair11 || pair8 == pair11 || pair9 == pair11 || pair10 == pair11 || pair11 == pair12 || 
                pair12 == pair1 || pair2 == pair12 || pair3 == pair12 || pair4 == pair12 || pair5 == pair12 || pair6 == pair12 || pair7 == pair12 || pair8 == pair12 || pair9 == pair12 || pair10 == pair12 || pair11 == pair12
            )
            {
                throw new GameEngineException($"Every Keno number must be unique. {pair1},{pair2},{pair3},{pair4},{pair5},{pair6},{pair7},{pair8},{pair9},{pair10},{pair11},{pair12} has duplicated numbers");
            }
            numbers = new KenoBall[12];
            numbers[0] = pair1;
            numbers[1] = pair2;
            numbers[2] = pair3;
            numbers[3] = pair4;
            numbers[4] = pair5;
            numbers[5] = pair6;
            numbers[6] = pair7;
            numbers[7] = pair8;
            numbers[8] = pair9;
            numbers[9] = pair10;
            numbers[10] = pair11;
            numbers[11] = pair12;
            this.multiplier = multiplier;
            this.bulleye = bulleye;
            isTenNumbers = false;
            isTwelfthNumbers = true;
        }
        private Keno(KenoBall pair1, KenoBall pair2, KenoBall pair3, KenoBall pair4, KenoBall pair5, KenoBall pair6, KenoBall pair7, KenoBall pair8, KenoBall pair9, KenoBall pair10, Multiplier multiplier, Bulleye bulleye)
        {
            if (
                pair1 == pair2 || pair1 == pair3 || pair1 == pair4 || pair1 == pair5 || pair1 == pair6 || pair1 == pair7 || pair1 == pair8 || pair1 == pair9 || pair1 == pair10 ||
                pair2 == pair1 || pair2 == pair3 || pair2 == pair4 || pair2 == pair5 || pair2 == pair6 || pair2 == pair7 || pair2 == pair8 || pair2 == pair9 || pair2 == pair10 ||
                pair3 == pair1 || pair2 == pair3 || pair3 == pair4 || pair3 == pair5 || pair3 == pair6 || pair3 == pair7 || pair3 == pair8 || pair3 == pair9 || pair3 == pair10 ||
                pair4 == pair1 || pair2 == pair4 || pair3 == pair4 || pair4 == pair5 || pair4 == pair6 || pair4 == pair7 || pair4 == pair8 || pair4 == pair9 || pair4 == pair10 ||
                pair5 == pair1 || pair2 == pair5 || pair3 == pair5 || pair4 == pair5 || pair5 == pair6 || pair5 == pair7 || pair5 == pair8 || pair5 == pair9 || pair5 == pair10 ||
                pair6 == pair1 || pair2 == pair6 || pair3 == pair6 || pair4 == pair6 || pair5 == pair6 || pair6 == pair7 || pair6 == pair8 || pair6 == pair9 || pair6 == pair10 ||
                pair7 == pair1 || pair2 == pair7 || pair3 == pair7 || pair4 == pair7 || pair5 == pair7 || pair6 == pair7 || pair7 == pair8 || pair7 == pair9 || pair7 == pair10 ||
                pair8 == pair1 || pair2 == pair8 || pair3 == pair8 || pair4 == pair8 || pair5 == pair8 || pair6 == pair8 || pair7 == pair8 || pair8 == pair9 || pair8 == pair10 ||
                pair9 == pair1 || pair2 == pair9 || pair3 == pair9 || pair4 == pair9 || pair5 == pair9 || pair6 == pair9 || pair7 == pair9 || pair8 == pair9 || pair9 == pair10 ||
                pair10 == pair1 || pair2 == pair10 || pair3 == pair10 || pair4 == pair10 || pair5 == pair10 || pair6 == pair10 || pair7 == pair10 || pair8 == pair10 || pair9 == pair10
            )
            {
                throw new GameEngineException($"Every Keno number must be unique. {pair1},{pair2},{pair3},{pair4},{pair5},{pair6},{pair7},{pair8},{pair9},{pair10} has duplicated numbers");
            }
            numbers = new KenoBall[10];
            numbers[0] = pair1;
            numbers[1] = pair2;
            numbers[2] = pair3;
            numbers[3] = pair4;
            numbers[4] = pair5;
            numbers[5] = pair6;
            numbers[6] = pair7;
            numbers[7] = pair8;
            numbers[8] = pair9;
            numbers[9] = pair10;
            this.multiplier = multiplier;
            this.bulleye = bulleye;
            isTenNumbers = true;
            isTwelfthNumbers = false;
        }

        internal int WhiteMatches(KenoDraw winnerNumber)
        {
            return winnerNumber.Matches(numbers);
        }

        internal Keno(int number1, int number2, int number3, int number4, int number5, int number6, int number7, int number8, int number9, int number10, int number11, int number12, bool multiplier, bool bulleye) :
             this(new KenoBall(number1), new KenoBall(number2), new KenoBall(number3), new KenoBall(number4), new KenoBall(number5), new KenoBall(number6), new KenoBall(number7), new KenoBall(number8), new KenoBall(number9), new KenoBall(number10), new KenoBall(number11), new KenoBall(number12), new Multiplier(multiplier), new Bulleye(bulleye))
        {
        }

        internal Keno(int number1, int number2, int number3, int number4, int number5, int number6, int number7, int number8, int number9, int number10, bool multiplier, bool bulleye) :
     this(new KenoBall(number1), new KenoBall(number2), new KenoBall(number3), new KenoBall(number4), new KenoBall(number5), new KenoBall(number6), new KenoBall(number7), new KenoBall(number8), new KenoBall(number9), new KenoBall(number10), new Multiplier(multiplier), new Bulleye(bulleye))
        {
        }

        Pattern IPick.this[int i] => throw new GameEngineException("Keno does not have single digits as there are in Picks lotteries.");

        IEnumerable<IPick> IPick.Permute() => throw new GameEngineException("Keno does not have permutations.");

        internal Multiplier Multiplier
        {
            get
            {
                return multiplier;
            }
        }
        internal Bulleye Bulleye
        {
            get
            {
                return bulleye;
            }
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            result.Add(this);
            return result;
        }

        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            result.Add(new SubTicket<IPick>(this));
            return result;
        }

        int IPick.Length => Length;
        public int Length
        {
            get
            {
                return numbers.Length;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return 1;
            }
        }

        string IPick.AsString() => AsString();
        internal string AsString()
        {
            if (isTenNumbers)
            {
                return $"[{numbers[0]},{numbers[1]},{numbers[2]},{numbers[3]},{numbers[4]},{numbers[5]},{numbers[6]},{numbers[7]},{numbers[8]},{numbers[9]},{multiplier},{bulleye}]";
            }
            else if (isTwelfthNumbers)
            {
                return $"[{numbers[0]},{numbers[1]},{numbers[2]},{numbers[3]},{numbers[4]},{numbers[5]},{numbers[6]},{numbers[7]},{numbers[8]},{numbers[9]},{numbers[10]},{numbers[11]},{multiplier},{bulleye}]";
            }
            else 
            {
                throw new GameEngineException($"{numbers.Length} numbers is  not valid. ");
            }
            
        }

        internal string SelectedNumbers
        { 
            get
            {
                return string.Join(',', numbers);
            } 
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            if (isTenNumbers)
            {
                return $"[{numbers[0].AsStringForAccounting()}-{numbers[1].AsStringForAccounting()}-{numbers[2].AsStringForAccounting()}-{numbers[3].AsStringForAccounting()}-{numbers[4].AsStringForAccounting()}-{numbers[5].AsStringForAccounting()}-{numbers[6].AsStringForAccounting()}-{numbers[7].AsStringForAccounting()}-{numbers[8].AsStringForAccounting()}-{numbers[9].AsStringForAccounting()}-{multiplier.AsStringForAccounting()}-{bulleye.AsStringForAccounting()}]";
            }
            else if (isTwelfthNumbers)
            {
                return $"[{numbers[0].AsStringForAccounting()}-{numbers[1].AsStringForAccounting()}-{numbers[2].AsStringForAccounting()}-{numbers[3].AsStringForAccounting()}-{numbers[4].AsStringForAccounting()}-{numbers[5].AsStringForAccounting()}-{numbers[6].AsStringForAccounting()}-{numbers[7].AsStringForAccounting()}-{numbers[8].AsStringForAccounting()}-{numbers[9].AsStringForAccounting()}-{numbers[10].AsStringForAccounting()}-{numbers[11].AsStringForAccounting()}-{multiplier.AsStringForAccounting()}-{bulleye.AsStringForAccounting()}]";
            }
            else
            {
                throw new GameEngineException($"{numbers.Length} numbers is  not valid. ");
            }
        }

        public long ToInt64()
        {
            if (isTenNumbers)
            {
                return
                Convert.ToInt32(10) * *********0000000000 +
                this.numbers[3].ToInt32() * *********00000000 +
                this.numbers[4].ToInt32() * *********000000 +
                this.numbers[5].ToInt32() * *********0000 +
                this.numbers[6].ToInt32() * *********00 +
                this.numbers[7].ToInt32() * ********* +
                this.numbers[8].ToInt32() * 1000000 +
                this.numbers[9].ToInt32() * 10000 +
                Convert.ToInt32(this.multiplier.ToInt()) * 100 +
                Convert.ToInt32(this.bulleye.ToInt());
            }
            else if (isTwelfthNumbers)
            {
                return
                Convert.ToInt32(12) * *********0000000000 +
                this.numbers[5].ToInt32() * *********00000000 +
                this.numbers[6].ToInt32() * *********000000 +
                this.numbers[7].ToInt32() * *********0000 +
                this.numbers[8].ToInt32() * *********00 +
                this.numbers[9].ToInt32() * ********* +
                this.numbers[10].ToInt32() * 1000000 +
                this.numbers[11].ToInt32() * 10000 +
                Convert.ToInt32(this.multiplier.ToInt()) * 100 + 
                Convert.ToInt32(this.bulleye.ToInt());
            }
            else
            {
                throw new GameEngineException($"{numbers.Length} numbers is  not valid. ");
            }


        }

        internal bool HasNumber(int n)
        {
            var result = numbers.Any(number => number.ToInt32() == n);
            return result;
        }
    }
}
