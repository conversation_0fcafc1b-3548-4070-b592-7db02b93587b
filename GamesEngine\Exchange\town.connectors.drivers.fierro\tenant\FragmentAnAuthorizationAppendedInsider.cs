﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using RestSharp;
using System;
using System.Net;
using System.Threading.Tasks;
using town.connectors.commons;

namespace town.connectors.drivers.fiero
{
    public abstract class FragmentAnAuthorizationAppendedInsider : FieroTenantDriver, IDriverUserProperties
    {
        public static TokenDriver CashierToken { get; set; }

        private RestClient _postClient;
        public string CashierUrl { get; private set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public FragmentAnAuthorizationAppendedInsider(string currencyCode) : base(Tenant_Actions.Fragment, TransactionType.CreateFragment, currencyCode)
        {
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("authorizationNumber");
            CustomSettings.AddVariableParameter("fragments");
            //CustomSettings.Prepare();
            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            string atAddress = recordSet.Mappings["atAddress"].AsString;
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));

            int authorizationNumber = recordSet.Mappings["authorizationNumber"].AsInt;
            if (authorizationNumber == 0) throw new ArgumentNullException(nameof(authorizationNumber));

            Fragment[] fragments = recordSet.Mappings["fragments"].As<Fragment[]>();
            if (fragments == null) throw new ArgumentNullException(nameof(fragments));

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            if (_postClient == null)
            {
                _postClient = new RestClient(CashierUrl);
            }

            AppendFragmentAuthorization(atAddress, authorizationNumber, fragments);

            var wagers = Array.ConvertAll(fragments, new Converter<Fragment, PostFreeFormWager>(EntitiesConverter.FragmentToWager));
            var fakeResult = EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, 1);

            return (T)Convert.ChangeType(fakeResult, typeof(T));
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        private void AppendFragmentAuthorization(string atAddress, int authorizationNumber, Fragment[] fragments)
        {
            var frgamentBody = new FragmentAuthorizationBody()
            {
                AtAddress = atAddress,
                AuthorizationNumber = authorizationNumber,
                Fragments = fragments
            };

            try
            {
                var url = $"api/multiple/fragments";
                var jsonString = Commons.ToJson(frgamentBody);

                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                }

                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                var resultFromCashier = _postClient.Execute(request);
                if (resultFromCashier.StatusCode != HttpStatusCode.OK)
                {
                    string body = string.Empty;
                    ErrorsSender.Send(body, $@"DepositLock Fragment to {atAddress} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }
        }
    }
}
