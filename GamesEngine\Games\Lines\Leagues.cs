﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    class Leagues
    {
        private readonly Dictionary<Sport, List<League>> leagues = new Dictionary<Sport, List<League>>();
        internal void Add(League league)
        {
            if (league == null) throw new ArgumentNullException(nameof(league));
            Sport sport = league.Sport;
            List<League> currentList = null;

            if (!leagues.TryGetValue(sport, out currentList))
            {
                currentList = new List<League>();
                leagues.Add(sport, currentList);
            }
            else if (currentList.Contains(league))
            {
                throw new GameEngineException($"The {league.Name} league {league.Name} is already added.");
            }
            
            currentList.Add(league);
        }

        internal bool Contains(League league)
        {
            if (league == null) throw new ArgumentNullException(nameof(league));
            Sport sport = league.Sport;
            List<League> currentList = null;

            return leagues.TryGetValue(sport, out currentList) && currentList.Contains(league);
        }

        internal bool Contains(Sport sport)
        {
            if (sport == null) throw new ArgumentNullException(nameof(sport));
            return leagues.ContainsKey(sport);
        }

        internal List<League> BySport(Sport sport)
        {
            if (sport == null) throw new ArgumentNullException(nameof(sport));
            List<League> currentList = null;
            if (!leagues.TryGetValue(sport, out currentList)) throw new GameEngineException($"este deporte no es conocido"); // TODO: cambiar  
            return currentList;
        }

        internal League Search(Sport sport, int leagueId)
        {
            if (sport == null) throw new ArgumentNullException(nameof(sport));
            List<League> currentList = null;
            if (!leagues.TryGetValue(sport, out currentList)) throw new GameEngineException($"este deporte no es conocido");

            League league = currentList.FirstOrDefault(x => x.Id == leagueId);

            if (league == null ) throw new GameEngineException($"There is no {nameof(League)} with id {leagueId}"); 

            return league;
        }
    }
}
