using ExchangeAPI.ExchangeEntities;
using GamesEngine.Bets;
using GamesEngine.Domains;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Bets.PresetBetAmounts;

namespace GamesEngine.Exchange
{
    #region Abstract Base Classes

    abstract class RiskProfiles : Objeto
    {
        internal const string DefaultRiskProfileName = "Preset Risk Profile";
        protected Dictionary<Domain, RiskProfile> Profiles { get; } = new Dictionary<Domain, RiskProfile>();
        internal RiskProfile DefaultRiskProfile { get; private protected set; }
        internal IEnumerable<RiskProfile> GetAll => Profiles.Values.Distinct().ToList();

        internal RiskProfile GetRiskProfile(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!Profiles.ContainsKey(domain)) return DefaultRiskProfile;
            return Profiles[domain];
        }

        internal RiskProfile GetRiskProfile(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            if (DefaultRiskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase)) return DefaultRiskProfile;

            var result = Profiles.Values.FirstOrDefault(riskProfile => riskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"Risk profile {name} not found");
            return result;
        }

        abstract internal RiskProfile CreateRiskProfile(Domain domain, string name);
        abstract internal RiskProfile CreateRiskProfile(RiskProfile riskProfileOriginal, Domain domainToClone, string name);

        internal bool ExistsProfileName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            return Profiles.Values.Any(riskProfile => riskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase)) || DefaultRiskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase);
        }

        internal bool HasProfile(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            return Profiles.ContainsKey(domain);
        }

        internal void AssignDomain(Domain domain, RiskProfile riskProfileTarget)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (riskProfileTarget == null) throw new ArgumentNullException(nameof(riskProfileTarget));
            if (riskProfileTarget == DefaultRiskProfile) throw new GameEngineException("Cannot move domain to default risk profile");
            if (riskProfileTarget.ContainsDomain(domain)) throw new GameEngineException($"Domain {domain.Url} already exists in risk profile {riskProfileTarget.Name}");

            if (Profiles.ContainsKey(domain))
            {
                var riskProfileOrigin = Profiles[domain];
                riskProfileOrigin.RemoveDomain(domain);
            }
            riskProfileTarget.AddDomain(domain);
            Profiles[domain] = riskProfileTarget;
        }
    }

    abstract class RiskProfile : Objeto
    {
        protected RiskProfiles riskProfiles;
        readonly List<Domain> domains = new List<Domain>();
        string name;

        internal IEnumerable<Domain> Domains => domains;

        internal string Name
        {
            get => name;
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                if (value.Equals(RiskProfiles.DefaultRiskProfileName, StringComparison.OrdinalIgnoreCase) && !name.Equals(RiskProfiles.DefaultRiskProfileName, StringComparison.OrdinalIgnoreCase)) throw new GameEngineException("Cannot change name to default risk profile");
                if (riskProfiles.ExistsProfileName(value)) throw new GameEngineException($"Risk profile {value} already exists");

                name = value;
            }
        }

        internal RiskProfile(RiskProfiles riskProfiles, Domain domain, string name)
        {
            if (riskProfiles == null) throw new ArgumentNullException(nameof(riskProfiles));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (riskProfiles.ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");

            this.riskProfiles = riskProfiles;
            domains.Add(domain);
            this.name = name;
        }

        protected RiskProfile(IEnumerable<Domain> domains, string name)
        {
            this.domains.AddRange(domains);
            this.name = name;
        }

        abstract internal void CloneFrom(RiskProfile riskProfile, Domain domain);

        internal void RemoveDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            domains.Remove(domain);
        }

        internal void AddDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            domains.Add(domain);
        }

        internal bool ContainsDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            return Domains.Contains(domain);
        }
    }
    #endregion

    #region Deposit Profile Implementation
    internal class DepositConfirmationRange : Objeto
    {
        internal decimal MinAmount { get; }
        internal decimal MaxAmount { get; }
        internal int ConfirmationsRequired { get; }

        internal DepositConfirmationRange(decimal minAmount, decimal maxAmount, int confirmationsRequired)
        {
            if (minAmount < 0) throw new GameEngineException($"{nameof(minAmount)} cannot be negative.");
            if (maxAmount <= minAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}.");
            if (confirmationsRequired < 0) throw new GameEngineException($"{nameof(confirmationsRequired)} cannot be negative.");

            MinAmount = minAmount;
            MaxAmount = maxAmount;
            ConfirmationsRequired = confirmationsRequired;
        }
    }

    internal class RiskProfileDeposit : RiskProfile
    {
        private readonly List<DepositConfirmationRange> confirmationRanges = new List<DepositConfirmationRange>();
        internal IEnumerable<DepositConfirmationRange> ConfirmationRanges => confirmationRanges;

        internal RiskProfileDeposit(RiskProfilesDeposits riskProfiles, Domain domain, string name) : base(riskProfiles, domain, name) { }

        private RiskProfileDeposit(IEnumerable<Domain> domains, string name) : base(domains, name)
        {
            SetDefaultConfirmationRanges();
        }

        private void SetDefaultConfirmationRanges()
        {
            confirmationRanges.Clear();
            confirmationRanges.Add(new DepositConfirmationRange(0, 100, 1));
            confirmationRanges.Add(new DepositConfirmationRange(100, 1000, 2));
            confirmationRanges.Add(new DepositConfirmationRange(1000, 5000, 4));
            confirmationRanges.Add(new DepositConfirmationRange(5000, decimal.MaxValue, 6));
        }

        internal static RiskProfileDeposit CreateDefault(RiskProfilesDeposits manager, IEnumerable<Domain> domains)
        {
            var profile = new RiskProfileDeposit(domains, RiskProfiles.DefaultRiskProfileName)
            {
                riskProfiles = manager
            };
            return profile;
        }

        internal override void CloneFrom(RiskProfile riskProfile, Domain domain)
        {
            if (riskProfile == null) throw new ArgumentNullException(nameof(riskProfile));
            if (!(riskProfile is RiskProfileDeposit sourceProfile)) throw new InvalidCastException($"Source profile must be of type {nameof(RiskProfileDeposit)}");

            confirmationRanges.Clear();
            foreach (var range in sourceProfile.ConfirmationRanges)
            {
                confirmationRanges.Add(new DepositConfirmationRange(range.MinAmount, range.MaxAmount, range.ConfirmationsRequired));
            }
        }

        internal void SetConfirmationRangesFromLists(List<decimal> minAmounts, List<decimal> maxAmounts, List<int> confirmations)
        {
            if (minAmounts.Count != maxAmounts.Count || minAmounts.Count != confirmations.Count) throw new GameEngineException("All lists for setting confirmation ranges must have the same number of elements.");

            var newRanges = new List<DepositConfirmationRange>();
            for (int i = 0; i < minAmounts.Count; i++)
            {
                var maxAmount = (maxAmounts[i] == -1) ? decimal.MaxValue : maxAmounts[i];
                newRanges.Add(new DepositConfirmationRange(minAmounts[i], maxAmount, confirmations[i]));
            }

            SetConfirmationRanges(newRanges);
        }

        internal void SetConfirmationRanges(IEnumerable<DepositConfirmationRange> newRanges)
        {
            if (newRanges == null || !newRanges.Any()) throw new GameEngineException("Cannot set an empty list of confirmation ranges.");
            
            var sortedRanges = newRanges.OrderBy(r => r.MinAmount).ToList();
            if (sortedRanges.First().MinAmount != 0) throw new GameEngineException("The lowest range must start at 0.");
            
            for (int i = 0; i < sortedRanges.Count - 1; i++)
            {
                var current = sortedRanges[i];
                var next = sortedRanges[i + 1];

                if (current.MaxAmount != next.MinAmount) throw new GameEngineException($"Ranges must be contiguous. A gap or overlap was found between {current.MaxAmount:C} and {next.MinAmount:C}.");
                
            }
            confirmationRanges.Clear();
            confirmationRanges.AddRange(sortedRanges);
        }

        private DepositConfirmationRange FindRangeForAmount(decimal amount)
        {
            if (amount < 0) throw new GameEngineException("Amount cannot be negative.");
            return confirmationRanges.FirstOrDefault(r => amount >= r.MinAmount && amount < r.MaxAmount);
        }

        internal int ConfirmationsForAmount(decimal amount)
        {
            var range = FindRangeForAmount(amount);
            return range?.ConfirmationsRequired ?? 0;
        }
    }

    internal class RiskProfilesDeposits : RiskProfiles
    {
        internal RiskProfilesDeposits(IEnumerable<Domain> allDomains)
        {
            DefaultRiskProfile = RiskProfileDeposit.CreateDefault(this, allDomains);
        }

        internal override RiskProfile CreateRiskProfile(Domain domain, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (Profiles.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} already exists in risk profiles");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");

            var riskProfile = new RiskProfileDeposit(this, domain, name);
            riskProfile.CloneFrom(DefaultRiskProfile, domain);
            Profiles.Add(domain, riskProfile);
            return riskProfile;
        }

        internal override RiskProfile CreateRiskProfile(RiskProfile riskProfileOriginal, Domain domainOfClone, string name)
        {
            if (riskProfileOriginal == null) throw new ArgumentNullException(nameof(riskProfileOriginal));
            if (domainOfClone == null) throw new ArgumentNullException(nameof(domainOfClone));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");
            if (riskProfileOriginal == DefaultRiskProfile) throw new GameEngineException("Cannot clone default risk profile");
            if (!Profiles.ContainsKey(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profiles registered");
            if (!riskProfileOriginal.ContainsDomain(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profile {riskProfileOriginal.Name}");

            var riskProfileOfClone = new RiskProfileDeposit(this, domainOfClone, name);
            riskProfileOfClone.CloneFrom(riskProfileOriginal, domainOfClone);
            riskProfileOriginal.RemoveDomain(domainOfClone);

            Profiles[domainOfClone] = riskProfileOfClone;
            return riskProfileOfClone;
        }
    }

    #endregion

    #region Lottery Profile Implementation
    internal class RiskProfilesLotteries : RiskProfiles
    {
        PicksLotteryGame Lotteries { get; }
        internal RiskProfilesLotteries(PicksLotteryGame picksLotteryGame)
        {
            if (picksLotteryGame == null) throw new ArgumentNullException(nameof(picksLotteryGame));

            Lotteries = picksLotteryGame;
            DefaultRiskProfile = new RiskProfileLotteries(this,
                picksLotteryGame.Company.Sales.AllDomains,
                new BetRanges[] { picksLotteryGame.BetRangesByPick(2), picksLotteryGame.BetRangesByPick(3), picksLotteryGame.BetRangesByPick(4), picksLotteryGame.BetRangesByPick(5) },
                new PresetBetAmounts[] { picksLotteryGame.PresetBetAmountsByPick(2), picksLotteryGame.PresetBetAmountsByPick(3), picksLotteryGame.PresetBetAmountsByPick(4), picksLotteryGame.PresetBetAmountsByPick(5) },
                picksLotteryGame.Prizes,
                picksLotteryGame.Risks,
                Store.MAX_DAYS_FROM_TODAY_TO_SELL_A_TICKET,
                DefaultRiskProfileName
                );
        }

        override internal RiskProfile CreateRiskProfile(Domain domain, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (Profiles.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} already exists in risk profiles");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");

            var riskProfile = new RiskProfileLotteries(this, domain, name);
            riskProfile.CloneFrom(DefaultRiskProfile, domain);
            Profiles.Add(domain, riskProfile);
            return riskProfile;
        }

        override internal RiskProfile CreateRiskProfile(RiskProfile riskProfileOriginal, Domain domainOfClone, string name)
        {
            if (riskProfileOriginal == null) throw new ArgumentNullException(nameof(riskProfileOriginal));
            if (domainOfClone == null) throw new ArgumentNullException(nameof(domainOfClone));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");
            if (riskProfileOriginal == DefaultRiskProfile) throw new GameEngineException("Cannot clone default risk profile");
            if (!Profiles.ContainsKey(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profiles registered");
            if (!riskProfileOriginal.ContainsDomain(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profile {riskProfileOriginal.Name}");

            var riskProfileOfClone = new RiskProfileLotteries(this, domainOfClone, name);
            riskProfileOfClone.CloneFrom(riskProfileOriginal, domainOfClone);
            riskProfileOriginal.RemoveDomain(domainOfClone);

            Profiles[domainOfClone] = riskProfileOfClone;
            return riskProfileOfClone;
        }

        internal class RiskProfileLotteries : RiskProfile
        {
            readonly protected List<BetRanges> betRangesPerGameType = new List<BetRanges>();
            internal IEnumerable<BetRanges> BetRangesPerGameType => betRangesPerGameType;

            readonly protected List<PresetBetAmounts> presetBetAmountsPerGameType = new List<PresetBetAmounts>();
            internal IEnumerable<PresetBetAmounts> PresetBetAmountsPerGameType => presetBetAmountsPerGameType;

            internal Prizes Prizes { get; private protected set; }
            internal Risks Risks { get; private protected set; }
            internal int DaysForwardToSell { get; private set; }

            internal RiskProfileLotteries(RiskProfilesLotteries riskProfiles, Domain domain, string name) : base(riskProfiles, domain, name)
            {
                Risks = new Risks();
                Prizes = new PrizesPicks();
            }

            internal RiskProfileLotteries(RiskProfilesLotteries riskProfiles, IEnumerable<Domain> domains, IEnumerable<BetRanges> betRangesPerGameType, IEnumerable<PresetBetAmounts> presetBetAmountsPerGameType, Prizes prizesTable, Risks risks, int daysForwardToSell, string name) :
                base(domains, name)
            {
                this.riskProfiles = riskProfiles;
                this.betRangesPerGameType.AddRange(betRangesPerGameType);
                this.presetBetAmountsPerGameType.AddRange(presetBetAmountsPerGameType);
                Prizes = prizesTable;
                Risks = risks;
                DaysForwardToSell = daysForwardToSell;
            }

            internal override void CloneFrom(RiskProfile riskProfile, Domain domain)
            {
                if (riskProfile == null) throw new ArgumentNullException(nameof(riskProfile));
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (!(riskProfile is RiskProfileLotteries sourceProfile)) throw new InvalidCastException($"Source profile must be of type {nameof(RiskProfileLotteries)}");

                betRangesPerGameType.Clear();
                foreach (var betRanges in sourceProfile.BetRangesPerGameType)
                {
                    var newBetRanges = new BetRanges();
                    for (int index = betRanges.Count - 1; index >= 0; index--)
                    {
                        var betRange = betRanges.GetAll.ElementAt(index);
                        if (Domains.Contains(betRange.Domain))
                        {
                            newBetRanges.Add(betRange);
                            betRanges.Remove(betRange);
                        }
                    }
                    betRangesPerGameType.Add(newBetRanges);
                }

                presetBetAmountsPerGameType.Clear();
                foreach (var presetBetAmounts in sourceProfile.PresetBetAmountsPerGameType)
                {
                    var newPresetBetAmounts = new PresetBetAmounts();
                    foreach (var presetBetAmount in presetBetAmounts.GetAll)
                    {
                        newPresetBetAmounts.Add(presetBetAmount.Amount, presetBetAmount.WhoEnteredLastChange, presetBetAmount.DateLastChange);
                        if (!presetBetAmount.Enabled) newPresetBetAmounts.Disable(presetBetAmount.Amount, presetBetAmount.WhoEnteredLastChange, presetBetAmount.DateLastChange);
                    }
                    presetBetAmountsPerGameType.Add(newPresetBetAmounts);
                }

                Prizes.CloneFrom(sourceProfile.Prizes);
                Risks.CloneFrom(sourceProfile.Risks, domain);
                DaysForwardToSell = sourceProfile.DaysForwardToSell;
            }

            internal void UpdateDaysForwardToSell(int daysForwardToSell, string employeeName, DateTime now)
            {
                if (daysForwardToSell <= 0) throw new GameEngineException($"{nameof(daysForwardToSell)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

                DaysForwardToSell = daysForwardToSell;
            }

            internal IEnumerable<BetRange> AllBetRangesByPick(int pick)
            {
                switch (pick)
                {
                    case 2: return betRangesPerGameType[0].GetAll;
                    case 3: return betRangesPerGameType[1].GetAll;
                    case 4: return betRangesPerGameType[2].GetAll;
                    case 5: return betRangesPerGameType[3].GetAll;
                    default: throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal void AddBetRanges(int pick, DateTime now, Domain domain, decimal maxAmount, decimal minAmount, string employeeName)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (riskProfiles.DefaultRiskProfile != this && !Domains.Contains(domain)) throw new GameEngineException($"Domain {domain.Url} must exist in risk profile {Name}");
                if (maxAmount <= 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater than 0");
                if (minAmount <= 0) throw new GameEngineException($"{nameof(minAmount)} must be greater than 0");
                if (minAmount > maxAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var betRanges = betRangesPerGameType[pick - 2];
                betRanges.Add(now, domain, maxAmount, minAmount, employeeName);
            }

            internal void ClearBetRanges(int pick)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no {nameof(pick)} {pick}");

                var betRanges = betRangesPerGameType[pick - 2];
                betRanges.Clear();
            }

            internal IEnumerable<PresetBetAmount> AllPresetBetAmountsByPick(int pick)
            {
                switch (pick)
                {
                    case 2: return presetBetAmountsPerGameType[0].GetAll;
                    case 3: return presetBetAmountsPerGameType[1].GetAll;
                    case 4: return presetBetAmountsPerGameType[2].GetAll;
                    case 5: return presetBetAmountsPerGameType[3].GetAll;
                    default: throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal int CountPresetBetAmountsByPick(int pick)
            {
                switch (pick)
                {
                    case 2: return presetBetAmountsPerGameType[0].Count;
                    case 3: return presetBetAmountsPerGameType[1].Count;
                    case 4: return presetBetAmountsPerGameType[2].Count;
                    case 5: return presetBetAmountsPerGameType[3].Count;
                    default: throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal void AddPresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Add(amount, employeeName, now);
            }

            internal void UpdatePresetBetAmounts(int pick, decimal currentAmount, decimal newAmount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (currentAmount <= 0) throw new GameEngineException($"{nameof(currentAmount)} must be greater than 0");
                if (newAmount <= 0) throw new GameEngineException($"{nameof(newAmount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Update(currentAmount, newAmount, employeeName, now);
            }

            internal void EnablePresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Enable(amount, employeeName, now);
            }

            internal void DisablePresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Disable(amount, employeeName, now);
            }
        }
    }
    #endregion
}