﻿using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tanker : Container
    {
        internal Tanker(string kind, Source source) : base(kind)
        {
            if (source == null) throw new GameEngineException("The Source is null.");
            if (source.Jar == null) throw new GameEngineException("The Jar is null.");
            this.Source = source;
        }

        internal Source Source { get; private set; }
        internal string Type => this.GetType().Name;

        internal int Id { get; private set; }
        internal string Name { get; set; }
        internal string Description { get; set; }
        internal DateTime CreatedAt { get; set; }


        internal abstract IEnumerable<Tank> Tanks { get; }

        internal int TotalTanks => Tanks.Count();


        internal class TankerPending : Tanker
        {
            private readonly IEnumerable<TankReady> tanksReady;

            internal TankerPending(int tankerId, string name, string description, DateTime createdAt, string kind, Source source, IEnumerable<TankReady> tanksReady) : base(kind, source)
            {
                this.tanksReady = tanksReady;
                this.Name = name;
                this.Description = description;
                this.Id = tankerId;
                this.CreatedAt = createdAt;

                foreach (var tankReady in this.tanksReady)
                {
                    Amount += tankReady.Amount;
                }
            }

            internal override IEnumerable<TankReady> Tanks =>  tanksReady;

            internal void Add(TankReady tankReady)
            {
                if (tankReady == null) throw new GameEngineException("The TankReady is null.");
                if(tanksReady.Contains(tankReady)) throw new GameEngineException("The TankReady already exists in the TankerPending.");

                tanksReady.Append(tankReady);
                Amount += tankReady.Amount;
            }

            internal TankerSealed Sealed()
            {
                if (tanksReady == null || !tanksReady.Any()) throw new GameEngineException("The TankerPending has no tanks ready.");

                var result = new TankerSealed(this);
                Source.AddOrUpdateTanker(result);
                return result;
            }

        }


        internal class TankerSealed : Tanker
        {
            private TankerPending tankerPending;
            private readonly IEnumerable<TankDiscarded> tanksDiscarded;

            internal TankerSealed(TankerPending tankerPending) : base(tankerPending.Kind, tankerPending.Source)
            {
                this.tankerPending = tankerPending;
                this.tanksDiscarded = new List<TankDiscarded>();
                this.Name = tankerPending.Name;
                this.Description = tankerPending.Description;
                this.Id = tankerPending.Id;
                this.Amount = tankerPending.Amount;
            }

            internal override IEnumerable<TankDiscarded> Tanks => tanksDiscarded;

            internal TankerDispatched Dispatched()
            {
                var result = new TankerDispatched(this);
                foreach (var tankReady in this.tankerPending.Tanks)
                {
                    var tankDiscarded = new TankDiscarded(tankReady);
                    Source.AddOrUpdateTank(tankDiscarded);
                    this.tanksDiscarded.Append(tankDiscarded);
                }
                Source.AddOrUpdateTanker(result);
            
                return result;
            }
        }

        internal class TankerDispatched : Tanker
        {
            private TankerSealed tankerSealed;


            internal TankerDispatched(TankerSealed tankerSealed) : base(tankerSealed.Kind, tankerSealed.Source)
            {
                this.tankerSealed = tankerSealed;
            }

            internal override IEnumerable<Tank> Tanks => tankerSealed.Tanks;

            internal TankerArchived Archived()
            {
                var result = new TankerArchived(this);
                Source.AddOrUpdateTanker(result);
                return result;
            }
        }

        internal class TankerArchived : Tanker
        {
            private TankerDispatched tankerDispached;

            internal TankerArchived(TankerDispatched tankerDispached) : base(tankerDispached.Kind, tankerDispached.Source)
            {
                this.tankerDispached = tankerDispached;
            }
            internal override IEnumerable<Tank> Tanks => tankerDispached.Tanks;

        }
    }
}
