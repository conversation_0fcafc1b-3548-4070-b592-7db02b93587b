﻿using GamesEngine.Finance;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Business
{
    internal abstract class WholeCatalog : Objeto, IEnumerable<CatalogMember>
    {
        protected List<CatalogMember> members = new List<CatalogMember>();
        protected Company company;

        internal WholeCatalog(Company company)
        {
            this.company = company;
        }

        internal IEnumerable<CatalogMember> VisibleMembers => members.Where(member => member.Visible == true);
        internal IEnumerable<CatalogMember> EnabledMembers => members.Where(member => member.Enabled == true);

        protected int nextConsecutive = 0;
        protected int NextConsecutive()
        {
            return nextConsecutive + 1;
        }

        protected CatalogMember Add(CatalogMember member)
        {
            members.Add(member);

            member.Visible = true;
            member.Enabled = true;

            return member;
        }
        abstract internal CatalogMember Add(int id, string name);

        internal CatalogMember Find(int id)
        {
            if (id < 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var result = members.Find(x => x.Id == id);
            if (result == null) throw new GameEngineException($"No {nameof(CatalogMember)} with {nameof(id)} '{id}'");
            return result;
        }

        internal CatalogMember Find(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            var result = members.Find(x => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"No {nameof(CatalogMember)} with {nameof(name)} '{name}'");
            return result;
        }

        internal bool Exists(int id)
        {
            if (id < 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var result = members.Exists(x => x.Id == id);
            return result;
        }

        internal bool Exists(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            var result = members.Exists(x => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            return result;
        }

        internal T SearchByName<T>(string name)
        {
            var result = members.Find(x => x.Name == name.Replace("_", " "));
            if (result == null) throw new GameEngineException($"No {nameof(WholeCatalog)} with {nameof(name)} '{name}'");
            return (T)Convert.ChangeType(result, typeof(T)); ;
        }

        internal void MakeVisible(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var member = Find(id);
            member.Visible = true;
        }

        internal void Hide(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var member = Find(id);
            member.Visible = false;
            member.Enabled = false;
        }

        internal void Enable(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var member = Find(id);
            if (!member.Visible) throw new GameEngineException($"Catalog member cannot be enabled if it is hid. Use method {nameof(MakeVisible)} first");
            member.Enabled = true;
        }

        internal void Disable(int id)
        {
            if (id <= 0) throw new GameEngineException($"{nameof(id)} '{id}' must be greater than 0");

            var member = Find(id);
            member.Enabled = false;
        }

        public IEnumerator<CatalogMember> GetEnumerator()
        {
            return members.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return members.GetEnumerator();
        }
    }

    public abstract class CatalogMember : Objeto
    {
        internal int Id { get; private protected set; }
        internal string Name { get; private protected set; }
        private bool visible;
        internal bool Visible
        {
            get
            {
                return visible;
            }
            set
            {
                bool change = visible != value;
                visible = value;
            }
        }
        private bool enabled;
        internal virtual bool Enabled
        {
            get
            {
                return enabled;
            }
            set
            {
                bool change = enabled != value;
                if (!Visible && value) throw new GameEngineException($"Non visible {nameof(CatalogMember)} can be enable.");
                enabled = value;
            }
        }
        public override string ToString()
        {
            return $"{Name} Visible:{Visible} Enabled:{Enabled}";
        }
        public CatalogMember(int id, string name)
        {
            Id = id;
            Name = name;
        }

    }

    public enum CustomSettingType
    {
        Boolean,
        Integer,
        String,
        Secret
    }

    public sealed class StoreRegistrationMessage : KafkaMessage
    {
        public string TenantName { get; private set; }
        public string StoreName { get; private set; }
        public string StoreAlias { get; private set; }
        public List<CustomSettingStarter> CustomSettings { get; private set; } = new List<CustomSettingStarter>();
        const string Empty = "-";

        public StoreRegistrationMessage(string tenantName, string storeName, string storeAlias, IEnumerable<CustomSetting> customSettings)
        {
            if (string.IsNullOrWhiteSpace(tenantName)) throw new ArgumentNullException(nameof(tenantName));
            if (string.IsNullOrWhiteSpace(storeName)) throw new ArgumentNullException(nameof(storeName));
            if (string.IsNullOrWhiteSpace(storeAlias)) throw new ArgumentNullException(nameof(storeAlias));

            TenantName = tenantName;
            StoreName = storeName;
            StoreAlias = storeAlias;
            if (customSettings != null)
            {
                foreach (var setting in customSettings)
                {
                    var cs = new CustomSettingStarter()
                    {
                        Key = setting.Key,
                        Value = setting.AsString,
                        Type = setting.Type,
                        Description = setting.Description
                    };
                    CustomSettings.Add(cs);
                }
            }
            
        }

        public StoreRegistrationMessage(string message) : base(message)
        {
        }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            base.Deserialize(serializedMessage, out fieldOrder);
            TenantName = serializedMessage[fieldOrder++];
            StoreName = serializedMessage[fieldOrder++];
            StoreAlias = serializedMessage[fieldOrder++];
            while (fieldOrder < serializedMessage.Length)
            {
                var key = serializedMessage[fieldOrder++];
                var value = serializedMessage[fieldOrder++];
                var type = serializedMessage[fieldOrder++];
                var description = serializedMessage[fieldOrder++];
                var cs = new CustomSettingStarter()
                {
                    Key = key,
                    Value = value == Empty ? string.Empty : value,
                    Type = type,
                    Description = description == Empty ? string.Empty : description
                };

                CustomSettings.Add(cs);
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TenantName).
            AddProperty(StoreName).
            AddProperty(StoreAlias);
            foreach (var customSetting in CustomSettings)
            {
                AddProperty(customSetting.Key);
                AddProperty(string.IsNullOrEmpty(customSetting.Value) ? Empty : customSetting.Value);
                AddProperty(customSetting.Type);
                if (string.IsNullOrWhiteSpace(customSetting.Description))
                {
                    AddProperty(Empty);
                }
                else
                {
                    AddProperty(customSetting.Description);
                }
            }
        }
    }

    public class CustomSettingStarter
    {
        public string Key { get; set; }
        public string Value { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }
        public CustomSettingType EnumType 
        { 
            get {
                var successful = Enum.TryParse(Type, out CustomSettingType customSettingType);
                if (!successful) throw new GameEngineException($"{nameof(Type)} '{Type}' is not valid");
                return customSettingType;
            } 
        }
    }

    public sealed class StoreRegistrationCallbackMessage : KafkaMessage
    {
        public string TenantName { get; private set; }
        public string StoreName { get; private set; }
        public int TenantId { get; private set; }
        public int StoreId { get; private set; }
        public string StoreAlias { get; private set; }

        public StoreRegistrationCallbackMessage(string tenantName, string storeName, int tenantId, int storeId, string storeAlias)
        {
            if (string.IsNullOrWhiteSpace(tenantName)) throw new ArgumentNullException(nameof(tenantName));
            if (string.IsNullOrWhiteSpace(storeName)) throw new ArgumentNullException(nameof(storeName));
            if (tenantId <= 0) throw new GameEngineException($"{nameof(tenantId)} must be greater than 0");
            if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(storeAlias)) throw new ArgumentNullException(nameof(storeAlias));

            TenantName = tenantName;
            StoreName = storeName;
            TenantId = tenantId;
            StoreId = storeId;
            StoreAlias = storeAlias;
        }

        public StoreRegistrationCallbackMessage(string message) : base(message)
        {
        }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            base.Deserialize(serializedMessage, out fieldOrder);
            TenantName = serializedMessage[fieldOrder++];
            StoreName = serializedMessage[fieldOrder++];
            TenantId = int.Parse(serializedMessage[fieldOrder++]);
            StoreId = int.Parse(serializedMessage[fieldOrder++]);
            StoreAlias = serializedMessage[fieldOrder++];
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TenantName).
            AddProperty(StoreName).
            AddProperty(TenantId).
            AddProperty(StoreId).
            AddProperty(StoreAlias);
        }
    }

    public sealed class CustomSettingMessage : KafkaMessage
    {
        public int OriginTenantId { get; private set; }
        public int OriginStoreId { get; private set; }
        public string OriginTenantName { get; private set; }
        public string OriginStoreAlias { get; private set; }
        public CustomSettingType? SettingType { get; private set; }
        public string DateToApplyTheChange { get; private set; }
        public string Key { get; private set; }
        public string EmployeeName { get; private set; }
        public string Value { get; private set; }
        public string Description { get; set; }
        public bool? Enabled { get; set; }
        const string Empty = "-";

        private CustomSettingMessage(int originTenantId, int originStoreId, string originTenantName, string originStoreAlias, string key)
        {
            if (originTenantId <= 0) throw new GameEngineException($"{nameof(originTenantId)} '{originTenantId}' must be greater than 0");
            if (originStoreId <= 0) throw new GameEngineException($"{nameof(originStoreId)} '{originStoreId}' must be greater than 0");
            if (string.IsNullOrWhiteSpace(originTenantName)) throw new ArgumentNullException(nameof(originTenantName));
            if (string.IsNullOrWhiteSpace(originStoreAlias)) throw new ArgumentNullException(nameof(originStoreAlias));
            if (string.IsNullOrWhiteSpace(key)) throw new ArgumentNullException(nameof(key));

            OriginTenantId = originTenantId;
            OriginStoreId = originStoreId;
            OriginTenantName = originTenantName;
            OriginStoreAlias = originStoreAlias;
            Key = key;
        }

        public CustomSettingMessage(int originTenantId, int originStoreId, string originTenantName, string originStoreAlias, string key, CustomSettingType settingType, string changeDateAsText, string employeeName, string value) :
            this(originTenantId, originStoreId, originTenantName, originStoreAlias, key)
        {
            if (string.IsNullOrWhiteSpace(changeDateAsText)) throw new ArgumentNullException(nameof(changeDateAsText));
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));

            SettingType = settingType;
            DateToApplyTheChange = changeDateAsText;
            EmployeeName = employeeName;
            Value = value;
        }

        public CustomSettingMessage(int originTenantId, int originStoreId, string originTenantName, string originStoreAlias, string key, string description) :
            this(originTenantId, originStoreId, originTenantName, originStoreAlias, key)
        {
            if (string.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

            Description = description;
        }

        public CustomSettingMessage(int originTenantId, int originStoreId, string originTenantName, string originStoreAlias, string key, bool enabled) :
            this(originTenantId, originStoreId, originTenantName, originStoreAlias, key)
        {
            if (originTenantId <= 0) throw new GameEngineException($"{nameof(originTenantId)} '{originTenantId}' must be greater than 0");
            if (originStoreId <= 0) throw new GameEngineException($"{nameof(originStoreId)} '{originStoreId}' must be greater than 0");

            Enabled = enabled;
        }

        public CustomSettingMessage(string message) : base(message)
        {
        }

        
        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            base.Deserialize(serializedMessage, out fieldOrder);
            OriginTenantId = int.Parse(serializedMessage[fieldOrder++]);
            OriginStoreId = int.Parse(serializedMessage[fieldOrder++]);
            OriginTenantName = serializedMessage[fieldOrder++];
            OriginStoreAlias = serializedMessage[fieldOrder++];
            Key = serializedMessage[fieldOrder++];
            if (serializedMessage.Length > 7)
            {
                SettingType = (CustomSettingType)int.Parse(serializedMessage[fieldOrder++]);
                DateToApplyTheChange = serializedMessage[fieldOrder++];
                EmployeeName = serializedMessage[fieldOrder++];
                Value = serializedMessage[fieldOrder++];
            }
            if (fieldOrder < serializedMessage.Length)
            {
                if (bool.TryParse(serializedMessage[fieldOrder], out bool result))
                {
                    Enabled = result;
                    fieldOrder++;
                }
                else
                {
                    Description = serializedMessage[fieldOrder++];
                    if (serializedMessage.Length != fieldOrder)
                    {
                        Enabled = bool.Parse(serializedMessage[fieldOrder]);
                    }
                }
            }
            
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(OriginTenantId).
            AddProperty(OriginStoreId).
            AddProperty(OriginTenantName).
            AddProperty(OriginStoreAlias).
            AddProperty(Key);
            if (SettingType.HasValue)
            {
                AddProperty((int)SettingType.Value).
                AddProperty(DateToApplyTheChange).
                AddProperty(EmployeeName).
                AddProperty(Value);
            }
            if (!string.IsNullOrWhiteSpace(Description))
            {
                AddProperty(Description);
            }
            if (Enabled.HasValue)
            {
                AddProperty(Enabled.Value);
            }
        }
    }

}