﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    internal abstract class Tank : Container
    {
        protected IEnumerable<Deposit> deposits;
        private Jar jar;

        internal Tank(int id, string name, string description, DateTime createdAt, Jar jar, string kind, IEnumerable<Deposit> depositos) : base(kind)
        {
            if (jar == null) throw new GameEngineException("The Jar is null.");
            if (depositos == null || !depositos.Any()) throw new ArgumentException("Deposits cannot be null or empty.");
            if (string.IsNullOrEmpty(name)) throw new GameEngineException("The name cannot be null or empty.");
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt cannot be MinValue.");

            this.Id = id;
            this.Name = name;
            this.Description = description;
            this.jar = jar;
            this.deposits = depositos;
            this.CreatedAt = createdAt;

            foreach (var deposit in deposits)
            {
                if (deposit == null) throw new GameEngineException("The Deposit is null.");
                if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                Amount += deposit.Amount;
            }

        }

        internal string Type => this.GetType().Name;
        internal Source Source => this.jar.Source;
        internal int Id { get; private set; }
        internal string Name { get; set; }
        internal string Description { get; set; }
        internal int Version => this.jar.Version;
        internal DateTime CreatedAt { get; set; }
        internal IEnumerable<Deposit> Deposits => deposits;

        internal class TankReady : Tank
        {
            internal TankReady(int id, string name, string description, DateTime createdAt, Jar jar, string kind, IEnumerable<Deposit> deposits) : base(id, name, description, createdAt, jar, kind, deposits) { }

            internal TankReady MergeWith(bool itIsThePresent, int draftTankId, DateTime createdAt, TankReady otherTank)
            {
                if (otherTank == null) throw new ArgumentException("Cannot merge with null tank.");
                if (otherTank == this) throw new ArgumentException("Cannot merge with the same tank.");
                if (otherTank.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                string name = "Merged Tank " + draftTankId;
                string description = $"Merged Tank from {this.Name} and {otherTank.Name}";

                IEnumerable<Deposit> mergedDeposits = new List<Deposit>(this.Deposits)
                    .Concat(otherTank.Deposits)
                    .Distinct()
                    .ToList();
                var result = new TankReady(draftTankId, name, description, createdAt, this.jar, Kind, mergedDeposits);//Rubicon Todo: Revisar que jar version debe darle origen al merged tank
                if (result.Amount != this.Amount + otherTank.Amount) throw new GameEngineException("The amount of the merged tank is not correct.");
                Source.AddOrUpdateTank(result);

                if (Integration.UseKafka)
                {

                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankMergedMessage tankMergedMessage = new TankMergedMessage(
                            tankId: result.Id,
                            kind: result.Kind,
                            description: $"Created Merged Tank {result.Name} - Jar version ${result.Version}",
                            jarVersion: result.Version,
                            originTankId: otherTank.Id,
                            createdAt: createdAt,
                            depositIds: result.Deposits.Select(d => d.Id).ToList()
                        );
                        buffer.Send(tankMergedMessage);
                    }
                }

                if (itIsThePresent)
                {
                    CreatedTankEvent createdTankEvent = new CreatedTankEvent(createdAt, result.Id, result.Name);
                    PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
                }

                TankDiscarded thisTankDiscarded = new TankDiscarded(this);
                Source.AddOrUpdateTank(thisTankDiscarded);
                TankDiscarded tankDiscarded = new TankDiscarded(otherTank);
                Source.AddOrUpdateTank(tankDiscarded);
                return result;
            }

            internal TankReady MergeWith(TankReady tankReady)
            {
                if (tankReady == null) throw new ArgumentException("Cannot merge with null tank.");
                if (tankReady.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                IEnumerable<Deposit> mergedDeposits = new List<Deposit>(this.Deposits)
                .Concat(tankReady.Deposits)
                .Distinct()
                .ToList();

                this.deposits = mergedDeposits;

                decimal newAmount = 0;
                foreach (var deposit in this.deposits)
                {
                    if (deposit == null) throw new GameEngineException("The Deposit is null.");
                    if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    newAmount += deposit.Amount;
                }

                decimal actualMergeAmount = this.Amount + tankReady.Amount;
                if (newAmount != actualMergeAmount) throw new GameEngineException("The amount of the merged tank is not correct.");

                Amount = newAmount;

                TankDiscarded tankDiscarded = new TankDiscarded(tankReady);
                Source.AddOrUpdateTank(tankDiscarded);
                return this;
            }
        }

        internal class TankDiscarded : Tank
        {
            internal TankDiscarded(TankReady tankReady) : base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.Deposits) { }
        }

        internal class TankDispatched : Tank
        {
            internal TankDispatched(TankReady tankReady) : base(tankReady.Id, tankReady.Name, tankReady.Description, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.Deposits) { }

        }
    }
}
