﻿using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.Location;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using town.connectors.commons;
using town.connectors.drivers;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static GamesEngine.Business.Company;
using static GamesEngine.Exchange.RiskProfilesLotteries;

namespace GamesEngine.Business
{
    [DataContract(Name = "UnlockAuthorizationBody")]
    public class TransactionMovementBody
    {
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "currency")]
        public string Currency { get; set; }
        [DataMember(Name = "amount")]
        public decimal Amount { get; set; }
        [DataMember(Name = "accountNumber")]
        public string AccountNumber { get; set; }
        [DataMember(Name = "who")]
        public string Who { get; set; }
        [DataMember(Name = "sourceNumber")]
        public int SourceNumber { get; set; }
        [DataMember(Name = "sourceName")]
        public string SourceName { get; set; }
        [DataMember(Name = "documentNumber")]
        public string DocumentNumber { get; set; }
        [DataMember(Name = "store")]
        public int Store { get; set; }
        [DataMember(Name = "concept")]
        public string Concept { get; set; }
        [DataMember(Name = "reference")]
        public string Reference { get; set; }
        [DataMember(Name = "processorId")]
        public int ProcessorId { get; set; }
    }

    [DataContract(Name = "ConfirmedDepositDueBody")]
    public class ConfirmedDepositDueBody
    {
        [DataMember(Name = "Due")]
        public decimal? Due { get; set; }
        [DataMember(Name = "TotalPaid")]
        public decimal? TotalPaid { get; set; }
        [DataMember(Name = "Rate")]
        public decimal? Rate { get; set; }
    }

    public class CurrencyProtoype
    {
        [DataMember(Name = "currency")]
        public string currency { get; set; }
    }
    public class CurrenciesPrototype
    {
        [DataMember(Name = "currencies")]
        public List<CurrencyProtoype> currencies { get; set; }
    }

    public class TotalOrderByToWin : TotalOrderAndExclusionsByToWin
    {
        public TotalOrderByToWin() : base(1)
        {

        }

        static string GetRuleType(TicketType ticketType)
        {
            switch (ticketType)
            {
                case TicketType.P2S:
                case TicketType.P3S:
                case TicketType.P4S:
                case TicketType.P5S:
                    return "Straight";
                case TicketType.P2B:
                case TicketType.P3B:
                case TicketType.P4B:
                case TicketType.P5B:
                    return "Boxed";
                default:
                    throw new GameEngineException($"Unknown {nameof(ticketType)} '{ticketType}'");
            }
        }

        internal void AddToWinByDrawAndNumbers(Company company, Domain domain, IEnumerable<SubTicket<IPick>> subtickets, decimal betAmount, TicketType ticketType, int pickType, State state, DateTime drawDate, string currencyCode, bool withFireBall)
        {
            var riskProfile = (RiskProfileLotteries)company.Lotto900().RiskProfiles.GetRiskProfile(domain);
            foreach (var subticket in subtickets)
            {
                var prizes = riskProfile.Prizes;
                var prizeCriteria = prizes.WayOfSubticket(ticketType, subticket);
                decimal prize = prizes.Prize(ticketType, prizeCriteria) * betAmount;
                var type = Disincentives.PrizeCriteriaName(pickType, prizeCriteria);
                var number = subticket.Number.AsStringForAccounting().Replace("-", string.Empty);
                var code = currencyCode == "FP" ? " FP" : string.Empty;
                var fireballTag = withFireBall ? " FB" : string.Empty;
                Tickets.Add(new ToWinByDrawAndNumber()
                {
                    draw = state.Abbreviation,
                    drawDate = drawDate.ToString("MM/dd/yyyy"),
                    drawHour = drawDate.ToString("h:mm:ss tt"),
                    number = number,
                    pick = pickType,
                    risk = betAmount,
                    toWin = prize - betAmount,
                    type = type,
                    description = $"Lotto{fireballTag}{code} {state.Abbreviation} {drawDate.ToString("h:mm:ss tt")} Pick {pickType} {drawDate.ToString("MM/dd/yyyy")} {GetRuleType(ticketType)}",
                    freePlay = currencyCode == "FP"
                }
                );
            }
        }
    }

    [DataContract(Name = "DepositResponse")]
    public class DepositResponse
    {
        [DataMember(Name = "depositId")]
        public int DepositId { get; set; }

        [DataMember(Name = "authorizationId")]
        public int AuthorizationId { get; set; }

    }


    [DataContract(Name = "QrCodeResponse")]
    public class QrCodeResponse
    {
        [DataMember(Name = "invoiceId")]
        public string InvoiceId { get; set; }

        [DataMember(Name = "destination")]
        public string Destination { get; set; }
        
        [DataMember(Name = "confirmedCurrency")]
        public string ConfirmedCurrency { get; set; } 
        
        [DataMember(Name = "amount")]
        public string Amount { get; set; } 
        
        [DataMember(Name = "rate")]
        public decimal Rate { get; set; }
    }

    public class WebhookPayloadRequest
    {
        [DataMember(Name = "eventType")]
        public string EventType { get; set; }
        [DataMember(Name = "channel")]
        public string Channel { get; set; }
        [DataMember(Name = "payload")]
        public Dictionary<string, object> Payload { get; set; }
    }

    [DataContract(Name = "TransactionResponse")]
    public class TransactionResponse
    {
        [DataMember(Name = "authorization")]
        public int Authorization { get; set; }
    }

    [DataContract(Name = "TransactionBody")]
    public class TransactionBody
    {
        [DataMember(Name = "now")]
        public DateTime Now { get; set; }
        [DataMember(Name = "amount")]
        public decimal Amount { get; set; }
        [DataMember(Name = "accountNumber")]
        public string AccountNumber { get; set; }
        [DataMember(Name = "currency")]
        public string Currency { get; set; }
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "domain")]
        public string Domain { get; set; }
        [DataMember(Name = "sourceNumber")]
        public int SourceNumber { get; set; }
        [DataMember(Name = "sourceName")]
        public string SourceName { get; set; }
        [DataMember(Name = "who")]
        public string Who { get; set; }
        [DataMember(Name = "agent")]
        public int Agent { get; set; }
        [DataMember(Name = "storeId")]
        public int StoreId { get; set; }
        [DataMember(Name = "processorId")]
        public int ProcessorId { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "reference")]
        public string Reference { get; set; }
        [DataMember(Name = "WithLock")]
        public bool WithLock { get; set; }

        public bool HasSource()
        {
            return SourceNumber != GamesEngine.Finance.TransactionMessage.NO_SOURCE;
        }
    }

    [DataContract(Name = "ConfirmBody")]
    public class DraftConfirmationBody
    {
        [DataMember(Name = "confirmedAmount")]
        public decimal ConfirmedAmount { get; set; }
        [DataMember(Name = "confirmedCurrency")]
        public string ConfirmedCurrency { get; set; }
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "externalReference")]
        public int ExternalReference { get; set; }
        [DataMember(Name = "employeeName")]
        public string EmployeeName { get; set; }
    }

    [DataContract(Name = "DepositCreationBody")]
    public class DepositCreationBody
    {
        [DataMember(Name = "toCurrencyCode")]
        public string ToCurrencyCode { get; set; }
        [DataMember(Name = "amount")]
        public decimal Amount { get; set; }
        [DataMember(Name = "toIdentifier")]
        public string ToIdentifier { get; set; }
        [DataMember(Name = "accountNumber")]
        public string AccountNumber { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "voucher")]
        public string Voucher { get; set; }
        [DataMember(Name = "voucherurl")]
        public string VoucherUrl { get; set; }
        [DataMember(Name = "depositor")]
        public string Depositor { get; set; }

        [DataMember(Name = "sendersName")]
        public string SendersName { get; set; }
        [DataMember(Name = "country")]
        public string Country { get; set; }
        [DataMember(Name = "state")]
        public string State { get; set; }
        [DataMember(Name = "city")]
        public string City { get; set; }
        [DataMember(Name = "controlNum")]
        public string ControlNum { get; set; }
        [DataMember(Name = "providerId")]
        public int ProviderId { get; set; }
        [DataMember(Name = "paymentMethod")]
        public PaymentMethod? PaymentMethod { get; set; }
        [DataMember(Name = "entityId")]
        public int EntityId { get; set; }
    }

    [DataContract(Name = "excludeSubticket")]
    public class ExcludeSubticket
    {
        [DataMember(Name = "state")]
        public string State { get; set; }
        [DataMember(Name = "hour")]
        public string Hour { get; set; }
        [DataMember(Name = "date")]
        public string Date { get; set; }
        [DataMember(Name = "subticket")]
        public string Subticket { get; set; }

        public ExcludeSubticket(ChosenDrawBody drawSchedule, string date)
        {
            State = drawSchedule.State;
            Hour = drawSchedule.Hour;
            Date = date;
            Subticket = ExcludeSubtickets.INDICATOR_FOR_ALL_SUBTICKETS_EXCLUDED;
        }

        public ExcludeSubticket(ToWinByDrawAndNumberResponse drawSchedule)
        {
            State = drawSchedule.draw;
            Hour = drawSchedule.drawHour;
            Date = drawSchedule.date;
            Subticket = drawSchedule.number;
        }
    }

    [DataContract(Name = "OrderErrorResponseForSingleSelection")]
    public class OrderErrorResponseForSingleSelection
    {
        [DataMember(Name = "errorMessage")]
        public string ErrorMessage { get; set; }
        [DataMember(Name = "isValidToRetryPurchase")]
        public bool IsValidToRetryPurchase { get; set; }
        [DataMember(Name = "excludeSubtickets")]
        public ExcludeSubticket[] ExcludeSubtickets { get; set; }

        public OrderErrorResponseForSingleSelection()
        {

        }
    }

    public class ChosenDrawBody
    {
        public string State { get; set; }
        public string Hour { get; set; }

        public ChosenDrawBody(string state, string hour)
        {
            State = state;
            Hour = hour;
        }
    }

    public struct ExcludedSubticketKey
    {
        public string State { get; set; }
        public string Hour { get; set; }
        public string StrNumbers { get; set; }
    }

    public class GroupedExcludedSubticket
    {
        public string Dates => string.Join(",", DatesSet);
        public string Subtickets => string.Join(",", NumbersSet);
        public HashSet<string> DatesSet { get; set; } = new HashSet<string>();
        public HashSet<string> NumbersSet { get; set; } = new HashSet<string>();
    }

    [DataContract(Name = "RefundBody")]
    public class RefundBody : MessageToAccountingServices
    {
        [DataMember(Name = "storeId")]
        public int StoreId { get; set; }
        [DataMember(Name = "domainUrl")]
        public string DomainUrl { get; set; }
        [DataMember(Name = "who")]
        public string Who { get; set; }
        [DataMember(Name = "concept")]
        public string Concept { get; set; }
        [DataMember(Name = "payFragments")]
        public List<FragmentPaymentBody> FragmentPayments { get; set; }
        [DataMember(Name = "account")]
        public string Account { get; set; }
        [DataMember(Name = "processorId")]
        public int ProcessorId { get; set; }

        private List<string> currencies;
        public IEnumerable<string> Currencies()
        {
            if (currencies == null)
            {
                currencies = new List<string>();
                foreach (var fragmentPayment in FragmentPayments)
                {
                    if (!currencies.Any(currency => currency == fragmentPayment.Currency))
                    {
                        currencies.Add(fragmentPayment.Currency);
                    }
                }
            }
            return currencies;
        }

        private List<FragmentPaymentBody> payFragmentsMessages;
        public List<FragmentPaymentBody> MessagesBy(string currencyCode)
        {
            if (payFragmentsMessages == null) payFragmentsMessages = new List<FragmentPaymentBody>();
            payFragmentsMessages.Clear();

            foreach (var fragmentPayment in FragmentPayments)
            {
                if (fragmentPayment.Currency == currencyCode)
                {
                    payFragmentsMessages.Add(fragmentPayment);
                }
            }

            return payFragmentsMessages;
        }
    }

    [DataContract(Name = "FragmentPaymentBody")]
    public sealed class FragmentPaymentBody
    {
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "authorizationNumber")]
        public int AuthorizationNumber { get; set; }
        [DataMember(Name = "wagerNumber")]
        public int WagerNumber { get; set; }
        [DataMember(Name = "wagerStatus")]
        public string Outcome { get; set; }
        [DataMember(Name = "fragmentPaymentDate")]
        public DateTime FragmentPaymentDate { get; set; }
        [DataMember(Name = "adjustedWinAmount")]
        public decimal AdjustedWinAmount { get; set; }
        [DataMember(Name = "adjustedLossAmount")]
        public decimal AdjustedLossAmount { get; set; }
        [DataMember(Name = "agentId")]
        public int agentId { get; set; }
        [DataMember(Name = "currency")]
        public string Currency { get; set; }
        [DataMember(Name = "isValidTicketNumber")]
        public bool IsValidTicketNumber { get; set; }
    }

    public class RefundResponse : Result
    {
        public RefundResponse(string entity, PaymentMethod paymentMethod, string[] currencyIsoCode) : base(entity, paymentMethod, currencyIsoCode, TransactionType.Refund)
        {
        }

        public enum RefundResponseCode
        {
            OK = 0,
            OkWithProblems = 0,
            RefundFail = 1
        }

        public RefundResponseCode Code { get; set; }
        public List<AuthorizationAndFragmentPair> FragmentsWithProblems { get; set; }
    }

    public class MultiRefundResponse
    {
        public List<AuthorizationAndFragmentPair> FragmentsWithProblems { get; private set; } = new List<AuthorizationAndFragmentPair>();
        public List<AuthorizationAndFragmentPair> FragmentsSuccessful { get; private set; } = new List<AuthorizationAndFragmentPair>();

        public IEnumerable<TicketNumberResult> AuthorizationsNumbersSuccessful => FragmentsSuccessful.Select(x => new TicketNumberResult() { TicketNumber = x.Authorization, GradingStatus = x.InitialGradingStatus });
        public IEnumerable<TicketNumberResult> AuthorizationsNumbersFailed => FragmentsWithProblems.Select(x => new TicketNumberResult() { TicketNumber = x.Authorization, GradingStatus = x.InitialGradingStatus });

        public void Append(RefundResponse response, FragmentPaymentBody fragmentPaymentBody)
        {
            if (response.FragmentsWithProblems != null && response.FragmentsWithProblems.Any()) FragmentsWithProblems.AddRange(response.FragmentsWithProblems);
            else FragmentsSuccessful.Add(new AuthorizationAndFragmentPair(fragmentPaymentBody.AuthorizationNumber.ToString(), fragmentPaymentBody.WagerNumber, string.Empty));
        }

    }
}
