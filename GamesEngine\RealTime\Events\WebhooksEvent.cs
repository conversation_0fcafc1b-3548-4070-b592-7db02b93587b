﻿using System;
using static GamesEngine.Settings.PaymentManager.Invoice;

namespace GamesEngine.RealTime.Events
{
    internal class CompletedDepositEvent : PlatformEvent
    {
        public string PlayerId { get; set; }
        public int AuthorizationId { get; set; }

        public CompletedDepositEvent(DateTime timeStamp, string playerId, int authorizationId) : base(timeStamp, PlatformEventType.DEPOSIT_COMPLETED)
        {
            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));
            if (authorizationId <= 0) throw new ArgumentOutOfRangeException(nameof(authorizationId));
            PlayerId = playerId;
            AuthorizationId = authorizationId;
        }
    }

    internal class InprocessDepositEvent : PlatformEvent
    {
        public string PlayerId { get; set; }
        public int AuthorizationId { get; set; }
        public DueStatus Status { get; set; }
        public decimal Amount { get; set; }

        public InprocessDepositEvent(DateTime timeStamp, string playerId, int authorizationId, DueStatus status, decimal amount) : base(timeStamp, PlatformEventType.PROCESSING_DEPOSIT)
        {
            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));
            if (authorizationId <= 0) throw new ArgumentOutOfRangeException(nameof(authorizationId));
            PlayerId = playerId;
            AuthorizationId = authorizationId;
            Status = status;
            Amount = amount;
        }
    }
}
