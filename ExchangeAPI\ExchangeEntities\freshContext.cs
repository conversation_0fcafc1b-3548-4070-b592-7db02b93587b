﻿using System;
using GamesEngine.Settings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace ExchangeAPI.ExchangeEntities
{
    public partial class freshContext : DbContext
    {
        public freshContext()
        {
        }

        public freshContext(DbContextOptions<freshContext> options)
            : base(options)
        {
        }

        public virtual DbSet<CreditNote> CreditNote { get; set; }
        public virtual DbSet<CreditNoteAttachment> CreditNoteAttachment { get; set; }
        public virtual DbSet<Currency> Currency { get; set; }
        public virtual DbSet<Dailyprofits> Dailyprofits { get; set; }
        public virtual DbSet<DebitNote> DebitNote { get; set; }
        public virtual DbSet<DebitNoteAttachment> DebitNoteAttachment { get; set; }
        public virtual DbSet<Deposit> Deposit { get; set; }
        public virtual DbSet<Domains> Domains { get; set; }
        public virtual DbSet<Exchangerate> Exchangerate { get; set; }
        public virtual DbSet<Exchangeuser> Exchangeuser { get; set; }
        public virtual DbSet<Journalentry> Journalentry { get; set; }
        public virtual DbSet<Journalentrydetails> Journalentrydetails { get; set; }
        public virtual DbSet<Profitabletransaction> Profitabletransaction { get; set; }
        public virtual DbSet<Transfer> Transfer { get; set; }
        public virtual DbSet<Withdrawal> Withdrawal { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
                var connectionString = "server=localhost;port=3306;user=*********;password=*********;database=fresh";
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<CreditNote>(entity =>
            {
                entity.ToTable("credit_note");

                entity.HasIndex(e => e.Currencyid)
                    .HasName("CURRENCYID_FK");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN_FK");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.HasIndex(e => e.Whorejected)
                    .HasName("WHOREJECTED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Approvals)
                    .HasColumnName("APPROVALS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Approvalsrequired)
                    .HasColumnName("APPROVALSREQUIRED")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Currencyid)
                    .HasColumnName("CURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Customer)
                    .IsRequired()
                    .HasColumnName("CUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dateapproved)
                    .HasColumnName("DATEAPPROVED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Datecreated)
                    .HasColumnName("DATECREATED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Daterejected)
                    .HasColumnName("DATEREJECTED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Description)
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .HasColumnName("DOMAIN")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Journalentrydetailid)
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Processorid)
                    .HasColumnName("PROCESSORID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Referenceid)
                    .HasColumnName("REFERENCEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Rejectionreason)
                    .HasColumnName("REJECTIONREASON")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejections)
                    .HasColumnName("REJECTIONS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Voucherurl)
                    .HasColumnName("VOUCHERURL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whorejected)
                    .HasColumnName("WHOREJECTED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.CreditNote)
                    .HasForeignKey(d => d.Currencyid)
                    .HasConstraintName("credit_note_ibfk_1");

                entity.HasOne(d => d.DomainNavigation)
                    .WithMany(p => p.CreditNote)
                    .HasForeignKey(d => d.Domain)
                    .HasConstraintName("credit_note_ibfk_5");

                entity.HasOne(d => d.Journalentrydetail)
                    .WithMany(p => p.CreditNote)
                    .HasPrincipalKey(p => p.Journalentrydetailid)
                    .HasForeignKey(d => d.Journalentrydetailid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("credit_note_ibfk_6");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.CreditNoteWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("credit_note_ibfk_3");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.CreditNoteWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("credit_note_ibfk_2");

                entity.HasOne(d => d.WhorejectedNavigation)
                    .WithMany(p => p.CreditNoteWhorejectedNavigation)
                    .HasForeignKey(d => d.Whorejected)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("credit_note_ibfk_4");
            });

            modelBuilder.Entity<CreditNoteAttachment>(entity =>
            {
                entity.ToTable("credit_note_attachment");

                entity.HasIndex(e => e.Creditnoteid)
                    .HasName("CREDITNOTEID_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Creditnoteid)
                    .HasColumnName("CREDITNOTEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Url)
                    .IsRequired()
                    .HasColumnName("URL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Creditnote)
                    .WithMany(p => p.CreditNoteAttachment)
                    .HasForeignKey(d => d.Creditnoteid)
                    .HasConstraintName("credit_note_attachment_ibfk_1");
            });

            modelBuilder.Entity<Currency>(entity =>
            {
                entity.ToTable("currency");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Decimalprecision)
                    .HasColumnName("DECIMALPRECISION")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Isocode)
                    .IsRequired()
                    .HasColumnName("ISOCODE")
                    .HasColumnType("varchar(5)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Sign)
                    .IsRequired()
                    .HasColumnName("SIGN")
                    .HasColumnType("varchar(5)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Unicode)
                    .IsRequired()
                    .HasColumnName("UNICODE")
                    .HasColumnType("varchar(5)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<Dailyprofits>(entity =>
            {
                entity.HasKey(e => new { e.Day, e.Currencyid })
                    .HasName("PRIMARY");

                entity.ToTable("dailyprofits");

                entity.HasIndex(e => e.Currencyid)
                    .HasName("CURRENCYID_FK");

                entity.Property(e => e.Day)
                    .HasColumnName("DAY")
                    .HasColumnType("datetime");

                entity.Property(e => e.Currencyid)
                    .HasColumnName("CURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Profits)
                    .HasColumnName("PROFITS")
                    .HasColumnType("decimal(16,4)");

                entity.Property(e => e.Purchases)
                    .HasColumnName("PURCHASES")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Sales)
                    .HasColumnName("SALES")
                    .HasColumnType("decimal(16,4)");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Dailyprofits)
                    .HasForeignKey(d => d.Currencyid)
                    .HasConstraintName("dailyprofits_ibfk_1");
            });

            modelBuilder.Entity<DebitNote>(entity =>
            {
                entity.ToTable("debit_note");

                entity.HasIndex(e => e.Currencyid)
                    .HasName("CURRENCYID_FK");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN_FK");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.HasIndex(e => e.Whorejected)
                    .HasName("WHOREJECTED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Approvals)
                    .HasColumnName("APPROVALS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Approvalsrequired)
                    .HasColumnName("APPROVALSREQUIRED")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Currencyid)
                    .HasColumnName("CURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Customer)
                    .IsRequired()
                    .HasColumnName("CUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Dateapproved)
                    .HasColumnName("DATEAPPROVED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Datecreated)
                    .HasColumnName("DATECREATED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Daterejected)
                    .HasColumnName("DATEREJECTED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Description)
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .HasColumnName("DOMAIN")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Journalentrydetailid)
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Processorid)
                    .HasColumnName("PROCESSORID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Referenceid)
                    .HasColumnName("REFERENCEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Rejectionreason)
                    .HasColumnName("REJECTIONREASON")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejections)
                    .HasColumnName("REJECTIONS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Voucherurl)
                    .HasColumnName("VOUCHERURL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whorejected)
                    .HasColumnName("WHOREJECTED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.DebitNote)
                    .HasForeignKey(d => d.Currencyid)
                    .HasConstraintName("debit_note_ibfk_1");

                entity.HasOne(d => d.DomainNavigation)
                    .WithMany(p => p.DebitNote)
                    .HasForeignKey(d => d.Domain)
                    .HasConstraintName("debit_note_ibfk_5");

                entity.HasOne(d => d.Journalentrydetail)
                    .WithMany(p => p.DebitNote)
                    .HasPrincipalKey(p => p.Journalentrydetailid)
                    .HasForeignKey(d => d.Journalentrydetailid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("debit_note_ibfk_6");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.DebitNoteWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("debit_note_ibfk_3");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.DebitNoteWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("debit_note_ibfk_2");

                entity.HasOne(d => d.WhorejectedNavigation)
                    .WithMany(p => p.DebitNoteWhorejectedNavigation)
                    .HasForeignKey(d => d.Whorejected)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("debit_note_ibfk_4");
            });

            modelBuilder.Entity<DebitNoteAttachment>(entity =>
            {
                entity.ToTable("debit_note_attachment");

                entity.HasIndex(e => e.Debitnoteid)
                    .HasName("DEBITNOTEID_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Debitnoteid)
                    .HasColumnName("DEBITNOTEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Url)
                    .IsRequired()
                    .HasColumnName("URL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.HasOne(d => d.Debitnote)
                    .WithMany(p => p.DebitNoteAttachment)
                    .HasForeignKey(d => d.Debitnoteid)
                    .HasConstraintName("debit_note_attachment_ibfk_1");
            });

            modelBuilder.Entity<Deposit>(entity =>
            {
                entity.ToTable("deposit");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN_FK");

                entity.HasIndex(e => e.Exchangerateid)
                    .HasName("EXCHANGERATEID_FK");

                entity.HasIndex(e => e.Fromcurrencyid)
                    .HasName("FROMCURRENCYID_FK");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID_FK");

                entity.HasIndex(e => e.Tocurrencyid)
                    .HasName("TOCURRENCYID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.HasIndex(e => e.Whorejected)
                    .HasName("WHOREJECTED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Approvals)
                    .HasColumnName("APPROVALS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Approvalsrequired)
                    .HasColumnName("APPROVALSREQUIRED")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Authorizationid)
                    .HasColumnName("AUTHORIZATIONID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Dateapproved)
                    .HasColumnName("DATEAPPROVED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Datecreated)
                    .HasColumnName("DATECREATED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Daterejected)
                    .HasColumnName("DATEREJECTED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Depositor)
                    .HasColumnName("DEPOSITOR")
                    .HasColumnType("varchar(15)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Description)
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .HasColumnName("DOMAIN")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Exchangerateid)
                    .HasColumnName("EXCHANGERATEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Fromcurrencyid)
                    .HasColumnName("FROMCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Gross)
                    .HasColumnName("GROSS")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Journalentrydetailid)
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Net)
                    .HasColumnName("NET")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Processorid)
                    .HasColumnName("PROCESSORID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Profit)
                    .HasColumnName("PROFIT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Rejectionreason)
                    .HasColumnName("REJECTIONREASON")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejections)
                    .HasColumnName("REJECTIONS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Tocurrencyid)
                    .HasColumnName("TOCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Tocustomer)
                    .IsRequired()
                    .HasColumnName("TOCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Voucher)
                    .HasColumnName("VOUCHER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Voucherurl)
                    .HasColumnName("VOUCHERURL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whorejected)
                    .HasColumnName("WHOREJECTED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.DomainNavigation)
                    .WithMany(p => p.Deposit)
                    .HasForeignKey(d => d.Domain)
                    .HasConstraintName("deposit_ibfk_7");

                entity.HasOne(d => d.Exchangerate)
                    .WithMany(p => p.Deposit)
                    .HasForeignKey(d => d.Exchangerateid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("deposit_ibfk_3");

                entity.HasOne(d => d.Fromcurrency)
                    .WithMany(p => p.DepositFromcurrency)
                    .HasForeignKey(d => d.Fromcurrencyid)
                    .HasConstraintName("deposit_ibfk_1");

                entity.HasOne(d => d.Journalentrydetail)
                    .WithMany(p => p.Deposit)
                    .HasPrincipalKey(p => p.Journalentrydetailid)
                    .HasForeignKey(d => d.Journalentrydetailid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("deposit_ibfk_8");

                entity.HasOne(d => d.Tocurrency)
                    .WithMany(p => p.DepositTocurrency)
                    .HasForeignKey(d => d.Tocurrencyid)
                    .HasConstraintName("deposit_ibfk_2");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.DepositWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("deposit_ibfk_5");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.DepositWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("deposit_ibfk_4");

                entity.HasOne(d => d.WhorejectedNavigation)
                    .WithMany(p => p.DepositWhorejectedNavigation)
                    .HasForeignKey(d => d.Whorejected)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("deposit_ibfk_6");
            });

            modelBuilder.Entity<Domains>(entity =>
            {
                entity.ToTable("domains");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Domain)
                    .IsRequired()
                    .HasColumnName("DOMAIN")
                    .HasColumnType("varchar(45)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<Exchangerate>(entity =>
            {
                entity.ToTable("exchangerate");

                entity.HasIndex(e => e.Fromcurrencyid)
                    .HasName("FROMCURRENCYID_FK");

                entity.HasIndex(e => e.Tocurrencyid)
                    .HasName("TOCURRENCYID_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Date)
                    .HasColumnName("DATE")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Fromcurrencyid)
                    .HasColumnName("FROMCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Purchaseprice)
                    .HasColumnName("PURCHASEPRICE")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Saleprice)
                    .HasColumnName("SALEPRICE")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Tocurrencyid)
                    .HasColumnName("TOCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.Fromcurrency)
                    .WithMany(p => p.ExchangerateFromcurrency)
                    .HasForeignKey(d => d.Fromcurrencyid)
                    .HasConstraintName("exchangerate_ibfk_1");

                entity.HasOne(d => d.Tocurrency)
                    .WithMany(p => p.ExchangerateTocurrency)
                    .HasForeignKey(d => d.Tocurrencyid)
                    .HasConstraintName("exchangerate_ibfk_2");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.Exchangerate)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("exchangerate_ibfk_3");
            });

            modelBuilder.Entity<Exchangeuser>(entity =>
            {
                entity.ToTable("exchangeuser");

                entity.HasIndex(e => e.Name)
                    .HasName("NAME")
                    .IsUnique();

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Name)
                    .IsRequired()
                    .HasColumnName("NAME")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");
            });

            modelBuilder.Entity<Journalentry>(entity =>
            {
                entity.ToTable("journalentry");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID")
                    .IsUnique();

                entity.HasIndex(e => e.Reference)
                    .HasName("REFERENCE")
                    .IsUnique();

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Date)
                    .HasColumnName("DATE")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Journalentrydetailid)
                    .IsRequired()
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Reference)
                    .IsRequired()
                    .HasColumnName("REFERENCE")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Systemid)
                    .IsRequired()
                    .HasColumnName("SYSTEMID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Title)
                    .IsRequired()
                    .HasColumnName("TITLE")
                    .HasColumnType("varchar(300)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.Journalentry)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("journalentry_ibfk_1");
            });

            modelBuilder.Entity<Journalentrydetails>(entity =>
            {
                entity.ToTable("journalentrydetails");

                entity.HasIndex(e => e.Journalentryid)
                    .HasName("JOURNALENTRYID_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Credit)
                    .HasColumnName("CREDIT")
                    .HasColumnType("decimal(8,2)");

                entity.Property(e => e.Debit)
                    .HasColumnName("DEBIT")
                    .HasColumnType("decimal(8,2)");

                entity.Property(e => e.Description)
                    .IsRequired()
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("varchar(250)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Journalentryid)
                    .HasColumnName("JOURNALENTRYID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Sequence)
                    .HasColumnName("SEQUENCE")
                    .HasColumnType("tinyint(3) unsigned");

                entity.HasOne(d => d.Journalentry)
                    .WithMany(p => p.Journalentrydetails)
                    .HasForeignKey(d => d.Journalentryid)
                    .HasConstraintName("journalentrydetails_ibfk_1");
            });

            modelBuilder.Entity<Profitabletransaction>(entity =>
            {
                entity.ToTable("profitabletransaction");

                entity.HasIndex(e => e.Exchangerateid)
                    .HasName("EXCHANGERATEID_FK");

                entity.HasIndex(e => e.Fromcurrencyid)
                    .HasName("FROMCURRENCYID_FK");

                entity.HasIndex(e => e.Tocurrencyid)
                    .HasName("TOCURRENCYID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Authorizationid)
                    .HasColumnName("AUTHORIZATIONID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Date)
                    .HasColumnName("DATE")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Exchangerateid)
                    .HasColumnName("EXCHANGERATEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Fromcurrencyid)
                    .HasColumnName("FROMCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Fromcustomer)
                    .IsRequired()
                    .HasColumnName("FROMCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gross)
                    .HasColumnName("GROSS")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Net)
                    .HasColumnName("NET")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Profit)
                    .HasColumnName("PROFIT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Targetaccount)
                    .IsRequired()
                    .HasColumnName("TARGETACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Tocurrencyid)
                    .HasColumnName("TOCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Tocustomer)
                    .IsRequired()
                    .HasColumnName("TOCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Type)
                    .IsRequired()
                    .HasColumnName("TYPE")
                    .HasColumnType("enum('DEPOSIT','WITHDRAWAL','TRANSFER')")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.Exchangerate)
                    .WithMany(p => p.Profitabletransaction)
                    .HasForeignKey(d => d.Exchangerateid)
                    .HasConstraintName("profitabletransaction_ibfk_3");

                entity.HasOne(d => d.Fromcurrency)
                    .WithMany(p => p.ProfitabletransactionFromcurrency)
                    .HasForeignKey(d => d.Fromcurrencyid)
                    .HasConstraintName("profitabletransaction_ibfk_1");

                entity.HasOne(d => d.Tocurrency)
                    .WithMany(p => p.ProfitabletransactionTocurrency)
                    .HasForeignKey(d => d.Tocurrencyid)
                    .HasConstraintName("profitabletransaction_ibfk_2");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.ProfitabletransactionWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("profitabletransaction_ibfk_5");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.ProfitabletransactionWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("profitabletransaction_ibfk_4");
            });

            modelBuilder.Entity<Transfer>(entity =>
            {
                entity.ToTable("transfer");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN_FK");

                entity.HasIndex(e => e.Exchangerateid)
                    .HasName("EXCHANGERATEID_FK");

                entity.HasIndex(e => e.Fromcurrencyid)
                    .HasName("FROMCURRENCYID_FK");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID_FK");

                entity.HasIndex(e => e.Tocurrencyid)
                    .HasName("TOCURRENCYID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.HasIndex(e => e.Whorejected)
                    .HasName("WHOREJECTED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Approvals)
                    .HasColumnName("APPROVALS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Approvalsrequired)
                    .HasColumnName("APPROVALSREQUIRED")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Authorizationid)
                    .HasColumnName("AUTHORIZATIONID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Dateapproved)
                    .HasColumnName("DATEAPPROVED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Datecreated)
                    .HasColumnName("DATECREATED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Daterejected)
                    .HasColumnName("DATEREJECTED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Description)
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .HasColumnName("DOMAIN")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Exchangerateid)
                    .HasColumnName("EXCHANGERATEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Fromcurrencyid)
                    .HasColumnName("FROMCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Fromcustomer)
                    .IsRequired()
                    .HasColumnName("FROMCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gross)
                    .HasColumnName("GROSS")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Journalentrydetailid)
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Net)
                    .HasColumnName("NET")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Processorid)
                    .HasColumnName("PROCESSORID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Profit)
                    .HasColumnName("PROFIT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Realaccount)
                    .HasColumnName("REALACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejectionreason)
                    .HasColumnName("REJECTIONREASON")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejections)
                    .HasColumnName("REJECTIONS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Targetaccount)
                    .IsRequired()
                    .HasColumnName("TARGETACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Tocurrencyid)
                    .HasColumnName("TOCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Tocustomer)
                    .IsRequired()
                    .HasColumnName("TOCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Voucherurl)
                    .HasColumnName("VOUCHERURL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whorejected)
                    .HasColumnName("WHOREJECTED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.DomainNavigation)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.Domain)
                    .HasConstraintName("transfer_ibfk_7");

                entity.HasOne(d => d.Exchangerate)
                    .WithMany(p => p.Transfer)
                    .HasForeignKey(d => d.Exchangerateid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("transfer_ibfk_3");

                entity.HasOne(d => d.Fromcurrency)
                    .WithMany(p => p.TransferFromcurrency)
                    .HasForeignKey(d => d.Fromcurrencyid)
                    .HasConstraintName("transfer_ibfk_1");

                entity.HasOne(d => d.Journalentrydetail)
                    .WithMany(p => p.Transfer)
                    .HasPrincipalKey(p => p.Journalentrydetailid)
                    .HasForeignKey(d => d.Journalentrydetailid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("transfer_ibfk_8");

                entity.HasOne(d => d.Tocurrency)
                    .WithMany(p => p.TransferTocurrency)
                    .HasForeignKey(d => d.Tocurrencyid)
                    .HasConstraintName("transfer_ibfk_2");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.TransferWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("transfer_ibfk_5");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.TransferWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("transfer_ibfk_4");

                entity.HasOne(d => d.WhorejectedNavigation)
                    .WithMany(p => p.TransferWhorejectedNavigation)
                    .HasForeignKey(d => d.Whorejected)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("transfer_ibfk_6");
            });

            modelBuilder.Entity<Withdrawal>(entity =>
            {
                entity.ToTable("withdrawal");

                entity.HasIndex(e => e.Domain)
                    .HasName("DOMAIN_FK");

                entity.HasIndex(e => e.Exchangerateid)
                    .HasName("EXCHANGERATEID_FK");

                entity.HasIndex(e => e.Fromcurrencyid)
                    .HasName("FROMCURRENCYID_FK");

                entity.HasIndex(e => e.Journalentrydetailid)
                    .HasName("JOURNALENTRYDETAILID_FK");

                entity.HasIndex(e => e.Tocurrencyid)
                    .HasName("TOCURRENCYID_FK");

                entity.HasIndex(e => e.Whoapproved)
                    .HasName("WHOAPPROVED_FK");

                entity.HasIndex(e => e.Whocreated)
                    .HasName("WHOCREATED_FK");

                entity.HasIndex(e => e.Whorejected)
                    .HasName("WHOREJECTED_FK");

                entity.Property(e => e.Id)
                    .HasColumnName("ID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Account)
                    .IsRequired()
                    .HasColumnName("ACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Amount)
                    .HasColumnName("AMOUNT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Approvals)
                    .HasColumnName("APPROVALS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Approvalsrequired)
                    .HasColumnName("APPROVALSREQUIRED")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Authorizationid)
                    .HasColumnName("AUTHORIZATIONID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Batchnumber)
                    .HasColumnName("BATCHNUMBER")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Dateapproved)
                    .HasColumnName("DATEAPPROVED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Datecreated)
                    .HasColumnName("DATECREATED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Daterejected)
                    .HasColumnName("DATEREJECTED")
                    .HasColumnType("datetime(3)");

                entity.Property(e => e.Deleted).HasColumnName("DELETED");

                entity.Property(e => e.Description)
                    .HasColumnName("DESCRIPTION")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Domain)
                    .HasColumnName("DOMAIN")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Exchangerateid)
                    .HasColumnName("EXCHANGERATEID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Fromcurrencyid)
                    .HasColumnName("FROMCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Fromcustomer)
                    .IsRequired()
                    .HasColumnName("FROMCUSTOMER")
                    .HasColumnType("varchar(10)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Gross)
                    .HasColumnName("GROSS")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Journalentrydetailid)
                    .HasColumnName("JOURNALENTRYDETAILID")
                    .HasColumnType("varchar(50)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Minerfee)
                    .HasColumnName("MINERFEE")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Net)
                    .HasColumnName("NET")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Processorid)
                    .HasColumnName("PROCESSORID")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Profit)
                    .HasColumnName("PROFIT")
                    .HasColumnType("decimal(16,8)");

                entity.Property(e => e.Realaccount)
                    .HasColumnName("REALACCOUNT")
                    .HasColumnType("varchar(34)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejectionreason)
                    .HasColumnName("REJECTIONREASON")
                    .HasColumnType("text")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Rejections)
                    .HasColumnName("REJECTIONS")
                    .HasColumnType("tinyint(4)");

                entity.Property(e => e.Tocurrencyid)
                    .HasColumnName("TOCURRENCYID")
                    .HasColumnType("int(11)");

                entity.Property(e => e.Voucherurl)
                    .HasColumnName("VOUCHERURL")
                    .HasColumnType("varchar(255)")
                    .HasCharSet("utf8mb4")
                    .HasCollation("utf8mb4_0900_ai_ci");

                entity.Property(e => e.Whoapproved)
                    .HasColumnName("WHOAPPROVED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whocreated)
                    .HasColumnName("WHOCREATED")
                    .HasColumnType("bigint(20)");

                entity.Property(e => e.Whorejected)
                    .HasColumnName("WHOREJECTED")
                    .HasColumnType("bigint(20)");

                entity.HasOne(d => d.DomainNavigation)
                    .WithMany(p => p.Withdrawal)
                    .HasForeignKey(d => d.Domain)
                    .HasConstraintName("withdrawal_ibfk_7");

                entity.HasOne(d => d.Exchangerate)
                    .WithMany(p => p.Withdrawal)
                    .HasForeignKey(d => d.Exchangerateid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("withdrawal_ibfk_3");

                entity.HasOne(d => d.Fromcurrency)
                    .WithMany(p => p.WithdrawalFromcurrency)
                    .HasForeignKey(d => d.Fromcurrencyid)
                    .HasConstraintName("withdrawal_ibfk_1");

                entity.HasOne(d => d.Journalentrydetail)
                    .WithMany(p => p.Withdrawal)
                    .HasPrincipalKey(p => p.Journalentrydetailid)
                    .HasForeignKey(d => d.Journalentrydetailid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("withdrawal_ibfk_8");

                entity.HasOne(d => d.Tocurrency)
                    .WithMany(p => p.WithdrawalTocurrency)
                    .HasForeignKey(d => d.Tocurrencyid)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("withdrawal_ibfk_2");

                entity.HasOne(d => d.WhoapprovedNavigation)
                    .WithMany(p => p.WithdrawalWhoapprovedNavigation)
                    .HasForeignKey(d => d.Whoapproved)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("withdrawal_ibfk_5");

                entity.HasOne(d => d.WhocreatedNavigation)
                    .WithMany(p => p.WithdrawalWhocreatedNavigation)
                    .HasForeignKey(d => d.Whocreated)
                    .HasConstraintName("withdrawal_ibfk_4");

                entity.HasOne(d => d.WhorejectedNavigation)
                    .WithMany(p => p.WithdrawalWhorejectedNavigation)
                    .HasForeignKey(d => d.Whorejected)
                    .OnDelete(DeleteBehavior.Cascade)
                    .HasConstraintName("withdrawal_ibfk_6");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
