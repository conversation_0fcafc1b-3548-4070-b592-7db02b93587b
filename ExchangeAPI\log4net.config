﻿<?xml version="1.0" encoding="utf-8"?>
<log4net>
	<appender name="drivers-execute" type="log4net.Appender.RollingFileAppender">
		<file value="/tmp/exchangeln/drivers-execute.log" />
		<appendToFile value="true" />
		<rollingStyle value="Size" />
		<maxSizeRollBackups value="-1" />
		<maximumFileSize value="5MB" />
		<staticLogFileName value="true" />
		<countDirection value="1"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %thread | %message%newline" />
		</layout>
	</appender>
	<logger additivity="false" name="drivers-execute">
		<level value="DEBUG"/>
		<appender-ref ref="drivers-execute" />
	</logger>
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender" >
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="database" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/database.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-removetransaction" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-removetransaction.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-posttransaction" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-posttransaction.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
	<appender name="accountingservices-asi-posttransactionwref" type="log4net.Appender.RollingFileAppender">
		<file value="/tmp/exchangeln/accountingservices-asi-posttransactionwref.log" />
		<appendToFile value="true" />
		<rollingStyle value="Size" />
		<maxSizeRollBackups value="-1" />
		<maximumFileSize value="5MB" />
		<staticLogFileName value="true" />
		<countDirection value="1"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date %thread | %message%newline" />
		</layout>
	</appender>
  <appender name="accountingservices-asi-postfreeformticket" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-postfreeformticket.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-postfreeformwagercollection" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-postfreeformwagercollection.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-postfreeformticketandwagers" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-postfreeformticketandwagers.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-gradefreeformwagercollection" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-gradefreeformwagercollection.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-svcvalidatecustomer" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-svcvalidatecustomer.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-asi-getticketwagers" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-asi-getticketwagers.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-gettoken" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-gettoken.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-deposit" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-deposit.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-withdraw" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-withdraw.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-validatecustomer" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-validatecustomer.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-createwagers" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-createwagers.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="accountingservices-dgs-updatewagers" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/accountingservices-dgs-updatewagers.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="emails" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/emails.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <appender name="scripts" type="log4net.Appender.RollingFileAppender">
    <file value="/tmp/exchangeln/scripts.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="-1" />
    <maximumFileSize value="5MB" />
    <staticLogFileName value="true" />
    <countDirection value="1"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date %thread | %message%newline" />
    </layout>
  </appender>
  <root>
    <level value="ALL" />
    <appender-ref ref="ConsoleAppender" />
  </root>
  <logger additivity="false" name="database">
    <level value="DEBUG"/>
    <appender-ref ref="database" />
  </logger>
  <logger additivity="false" name="accountingservices-asi">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-removetransaction">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-removetransaction" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-posttransaction">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-posttransaction" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-postfreeformticket">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-postfreeformticket" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-postfreeformwagercollection">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-postfreeformwagercollection" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-postfreeformticketandwagers">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-postfreeformticketandwagers" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-gradefreeformwagercollection">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-gradefreeformwagercollection" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-svcvalidatecustomer">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-svcvalidatecustomer" />
  </logger>
  <logger additivity="false" name="accountingservices-asi-getticketwagers">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-asi-getticketwagers" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-gettoken">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-gettoken" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-deposit">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-deposit" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-withdraw">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-withdraw" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-validatecustomer">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-validatecustomer" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-createwagers">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-createwagers" />
  </logger>
  <logger additivity="false" name="accountingservices-dgs-updatewagers">
    <level value="DEBUG"/>
    <appender-ref ref="accountingservices-dgs-updatewagers" />
  </logger>
  <logger additivity="false" name="emails">
    <level value="DEBUG"/>
    <appender-ref ref="emails" />
  </logger>
  <logger additivity="false" name="scripts">
    <level value="DEBUG"/>
    <appender-ref ref="scripts" />
  </logger>
</log4net>