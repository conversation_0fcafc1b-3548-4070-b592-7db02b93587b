﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace ExchangeAPI.Controllers
{
    public class AgentsController : AuthorizeController
    {
        [HttpPost("api/agent")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AddAgentAsync([FromBody] AgentAdditionBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
            if (string.IsNullOrWhiteSpace(body.Path)) return BadRequest($"{nameof(body.Path)} is required");

            var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
                    agentParent = marketplace.SearchAgent('{body.Path}');
                    agentParent.AddAgent('{body.Name}');
				}}
			");

            return result;
        }

        [HttpGet("api/agents/stores/{storeId}/domains")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> DomainsFromAgentStoreAsync(int storeId, string path)
		{
            if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");

            if (string.IsNullOrWhiteSpace(path)) path = Security.UserPath(HttpContext);

            var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    agent = marketplace.SearchAgent('{path}');
                    if (agent.ContainsStore({storeId}))
                    {{
                        store = agent.SearchStore({storeId});
                        for (domains:store.VisiblesDomains)
                        {{
						    domain = domains;
						    print domain.Id id;
                            print domain.Url url;
                            print store.IsEnabledOn(domain) enabled;
                            lastEntries = domain.LastEntries(5);
						    for (log:lastEntries)
						    {{
							    print log.DateFormattedAsText date;
							    print log.Who who;
							    print log.Message message;
						    }}
                        }}
                    }}
                }}
            ");

			return result;
		}

		[HttpPost("api/agents/stores/{storeId}/domains/{domainId}/activation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnableDomainInStoreAsync(int storeId, int domainId)
		{
			if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");
			if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			var msg = "Enabled";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
            {{
				Check(marketplace.ExistsAgent('{path}')) Error 'Agent {path} does not exist';
				agent = marketplace.SearchAgent('{path}');
				Check(agent.ContainsStore({storeId})) Error 'Agent does not have store {storeId}';
				existsDomainInCompany = company.Sales.ExistsDomain({domainId});
				Check(existsDomainInCompany) Error 'Domain {domainId} does not belong to company';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain({domainId});
				Check(existsDomainInMarketplace) Error 'Domain {domainId} does not belong to marketplace';
				if (existsDomainInCompany && existsDomainInMarketplace)
                {{
					domain = company.Sales.DomainFrom({domainId});
					containsDomain = agent.ContainsDomain({storeId}, domain);
					Check(containsDomain) Error 'Agent does not have domain {domainId}';
					isEnabledInStore = agent.IsEnabledInStore({storeId}, domain);
					Check(!isEnabledInStore) Error 'Domain is already enabled in store {storeId}';
				}}
            }}
            ",$@"
			{{
				domain = company.Sales.DomainFrom({domainId});
				agent = marketplace.SearchAgent('{path}');
				agent.EnableDomain(itIsThePresent, {storeId}, domain);
				domain.AddAnnotation('{msg}', '{employeeName}', Now);
			}}
			");

			return result;
		}

		[HttpPost("api/agents/stores/{storeId}/domains/{domainId}/deactivation")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> DisableDomainInStoreAsync(int storeId, int domainId)
		{
			if (storeId <= 0) return BadRequest($"{nameof(storeId)} must be greater than 0");
			if (domainId <= 0) return BadRequest($"{nameof(domainId)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			var msg = "Disabled";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
            {{
				Check(marketplace.ExistsAgent('{path}')) Error 'Agent {path} does not exist';
				agent = marketplace.SearchAgent('{path}');
				Check(agent.ContainsStore({storeId})) Error 'Agent does not have store {storeId}';
				existsDomainInCompany = company.Sales.ExistsDomain({domainId});
				Check(existsDomainInCompany) Error 'Domain {domainId} does not belong to company';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain({domainId});
				Check(existsDomainInMarketplace) Error 'Domain {domainId} does not belong to marketplace';
				if (existsDomainInCompany && existsDomainInMarketplace)
                {{
					domain = company.Sales.DomainFrom({domainId});
					containsDomain = agent.ContainsDomain({storeId}, domain);
					Check(containsDomain) Error 'Agent does not have domain {domainId}';
					isEnabledInStore = agent.IsEnabledInStore({storeId}, domain);
					Check(isEnabledInStore) Error 'Domain is already disabled in store {storeId}';
				}}
            }}
            ", $@"
			{{
				domain = company.Sales.DomainFrom({domainId});
				agent = marketplace.SearchAgent('{path}');
				agent.DisableDomain(itIsThePresent, {storeId}, domain);
				domain.AddAnnotation('{msg}', '{employeeName}', Now);
			}}
			");

			return result;
		}

		[DataContract(Name = "AgentCreationBody")]
        public class AgentAdditionBody
        {
            [DataMember(Name = "name")]
            public string Name { get; set; }
            [DataMember(Name = "path")]
            public string Path { get; set; }
        }
    }
}
