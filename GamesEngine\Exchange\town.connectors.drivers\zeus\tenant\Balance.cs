﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.zeus;

namespace Connectors.town.connectors.drivers.zeus
{
    public class Balance : DGSTenantDriver
	{
        private RestClient _getBalanceClient;
        public Balance() : base(Tenant_Actions.Balance, TransactionType.RetrieveInfo, "USD")
        {
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getBalanceClient == null)
			{
				if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
				if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
				if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                }

                _getBalanceClient = new RestClient(ServicesUrl);
			}

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    }

                    _getBalanceClient = new RestClient(ServicesUrl);
                }
            }

            var result = await AvailableBalanceAsync(now, recordSet);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("playerId");
            CustomSettings.AddVariableParameter("httpContext");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
        private async Task<decimal> AvailableBalanceAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            var PlayerId = recordSet.Mappings["playerId"];
            var HttpContext = recordSet.Mappings["httpContext"];

            string playerId = PlayerId.AsString;
            var httpContext = HttpContext.As<HttpContext>();

            if (string.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException(nameof(playerId));

            string url = $"/GetPlayerBalance";

            string responseString = "";
            int retryNumber = 0;

            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Get);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddParameter("playerId", playerId);

                    StringValues tokenValue;
                    httpContext.Request.Headers.TryGetValue(PLAYER_TOKEN_NAME, out tokenValue);
                    var tokenToUse = StringValues.IsNullOrEmpty(tokenValue) ? SUBSTITUTE_PLAYER_TOKEN : tokenValue.ToString();
                    request.AddParameter("token", tokenToUse);

                    var response = await _getBalanceClient.ExecuteAsync(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesInfo.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                    }
                    break;
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesInfo.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

                    retryNumber++;

                    await Task.Delay(5000);
                    if (retryNumber == MAX_RETRIES) return 0;
                }
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
                return 0;
            }
            else
            {
                decimal amount = 0;
                var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
                if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                {
                    Loggers.GetIntance().AccountingServicesInfo.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                }
                else
                {
                    try
                    {
                        var responseObj = Commons.FromJson<PlayerBalanceBody>(responseString);
                        amount = responseObj.realAvailableBalance;
                    }
                    catch (Exception e)
                    {
                        NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                    }
                }
                return amount;
            }
        }
    }
}
