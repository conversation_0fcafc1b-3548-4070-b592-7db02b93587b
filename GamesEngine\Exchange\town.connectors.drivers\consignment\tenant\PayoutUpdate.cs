﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.consignment
{
    public class PayoutUpdate: ConsignmentTenantDriver
    {
        public PayoutUpdate() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            WithdrawalTransaction result;
            var authorizationNumber = await UpdatePayoutAsync(recordSet);
            if (authorizationNumber == FAKE_DOCUMENT_NUMBER) result = new WithdrawalTransaction(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            else result = new WithdrawalTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<int> UpdatePayoutAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseEditPayoutEnvelope responsePayout = null;
            string responseString = string.Empty;
            string valuesWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=Edit_payout";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out valuesWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetPayoutUpdate.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetPayoutUpdate.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseEditPayoutEnvelope));
                responsePayout = (ResponseEditPayoutEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetPayoutUpdate.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(UpdatePayoutAsync), 1, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");
            }

            if (responsePayout == null || string.IsNullOrWhiteSpace(responsePayout.Body.Edit_payoutResponse.Edit_payoutResult))
            {
                NotifyWarn(nameof(UpdatePayoutAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                var isSuccessful = responsePayout.Body.Edit_payoutResponse.Edit_payoutResult.StartsWith('1');
                if (isSuccessful)
                {
                    var tempResult = responsePayout.Body.Edit_payoutResponse.Edit_payoutResult.Remove(0, "1,Payout Inserted,".Length);
                    var isNumeric = int.TryParse(tempResult, out int authorizationNumber);
                    if (isNumeric) return authorizationNumber;
                    else NotifyWarn(nameof(UpdatePayoutAsync), $"Url:{url}\nResponse: {responseString}", "No authorization number found");
                }
                else NotifyWarn(nameof(UpdatePayoutAsync), $"Url:{url}\nResponse: {responseString}", "Response is not successful");
            }

            return FAKE_DOCUMENT_NUMBER;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var customerId = recordSet.Mappings["merchId"].AsString;
            var payoutId = recordSet.Mappings["payoutId"].AsString;
            var receiversName = recordSet.Mappings["receiversName"].AsString;
            var country = recordSet.Mappings["country"].AsString;
            var state = recordSet.Mappings["state"].AsString;
            var city = recordSet.Mappings["city"].AsString;
            var amount = recordSet.Mappings["amount"].AsDecimal;
            var sendersAddress = recordSet.Mappings["sendersAddress"].AsString;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<Edit_payout xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<user>").Append(SystemId).AppendLine("</user>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilder.Append("<payoutId>").Append(customerId).AppendLine("</payoutId>");
            xmlBuilder.Append("<receiversName>").Append(receiversName).AppendLine("</receiversName>");
            xmlBuilder.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilder.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilder.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilder.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilder.Append("<sendersAddress>").Append(sendersAddress).AppendLine("</sendersAddress>");
            xmlBuilder.AppendLine("</Edit_payout>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<Edit_payout xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<user>XXXX</user>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<merchId>").Append(customerId).AppendLine("</merchId>");
            xmlBuilderWithHiddenFields.Append("<payoutId>").Append(payoutId).AppendLine("</payoutId>");
            xmlBuilderWithHiddenFields.Append("<receiversName>").Append(receiversName).AppendLine("</receiversName>");
            xmlBuilderWithHiddenFields.Append("<country>").Append(country).AppendLine("</country>");
            xmlBuilderWithHiddenFields.Append("<state>").Append(state).AppendLine("</state>");
            xmlBuilderWithHiddenFields.Append("<city>").Append(city).AppendLine("</city>");
            xmlBuilderWithHiddenFields.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilderWithHiddenFields.Append("<sendersAddress>").Append(sendersAddress).AppendLine("</sendersAddress>");
            xmlBuilderWithHiddenFields.AppendLine("</Edit_payout>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseEditPayoutEnvelope
        {
            public ResponseEditPayoutBody Body { get; set; }
        }

        public class ResponseEditPayoutBody
        {
            [XmlElement(ElementName = "Edit_payoutResponse", Namespace = "http://tempuri.org/")]
            public EditPayoutResponse Edit_payoutResponse { get; set; }
        }

        public class EditPayoutResponse
        {
            public string Edit_payoutResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("merchId");
            CustomSettings.AddVariableParameter("payoutId");
            CustomSettings.AddVariableParameter("receiversName");
            CustomSettings.AddVariableParameter("country");
            CustomSettings.AddVariableParameter("state");
            CustomSettings.AddVariableParameter("city");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("sendersAddress");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
