﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class Balance : ASITenantDriver
	{
		protected HttpClient _getBalanceClient;
		private string SystemId;
        private string SystemPassword;

        public Balance() : base(Tenant_Actions.Balance, TransactionType.RetrieveInfo, "USD")
        {
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_getBalanceClient == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;

				_getBalanceClient = new HttpClient
				{
					BaseAddress = new Uri(companyBaseUrlServices),
					Timeout = TimeSpan.FromSeconds(200)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_getBalanceClient = new HttpClient
					{
						BaseAddress = new Uri(companyBaseUrlServices),
						Timeout = TimeSpan.FromMinutes(10)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			}

			var CustomerId = recordSet.Mappings["customerId"];

			if (String.IsNullOrWhiteSpace(CustomerId.AsString)) throw new ArgumentNullException(nameof(CustomerId.AsString));

			var result = await GetCustomerBalanceAsync(now, CustomerId.AsString);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
			CustomSettings.AddVariableParameter("customerId");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
		}

        private async Task<decimal> GetCustomerBalanceAsync(DateTime now, string customerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

			var values = new CustomerBalance()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				CustomerId = customerId
			};

			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");
			const string url = "/v1/5dimesAPI/GetCustomerBalance";

			string responseString = "";
			int retryNumber = 0;

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASI);

			while (true)
			{
				try
				{
					var response = await _getBalanceClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();
					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASI.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					await Task.Delay(5000);
					if (retryNumber == MAX_RETRIES) return 0;
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetCustomerBalanceAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return 0;
			}
			else
			{
				var errorResponse = Commons.FromJson<JsonErrorResponse>(responseString);
				if (errorResponse != null)
				{
					NotifyWarn(nameof(GetCustomerBalanceAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");
					return 0;
				}
				else
				{
					decimal amount = 0;
					try
					{
						amount = Convert.ToDecimal(responseString);
					}
					catch (Exception e)
					{
						NotifyWarn(nameof(GetCustomerBalanceAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
					}
					return amount;
				}
			}
		}
	}
}
