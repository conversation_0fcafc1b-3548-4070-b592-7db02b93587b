﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
	internal class FixedLine : Line
	{
		private readonly IEnumerable<int> rewards;
		private readonly double[] coefficientRewards;
		private readonly IEnumerable<string> options;
		private FixedAnswer realAnswer;
		private MultipleOptions answers;

		internal FixedLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, IEnumerable<int> rewards, IEnumerable<string> options, string text, string who, DateTime now) : base(lineId, question, tier, game, shelve, index, who, now)
		{
			if (options == null) throw new ArgumentNullException(nameof(options));
			if (options.Any(x => string.IsNullOrWhiteSpace(x))) throw new ArgumentNullException(nameof(options));
			if (rewards == null) throw new ArgumentNullException(nameof(rewards));
			var rewardsCount = rewards.Count();
			var optionsCount = options.Count();
			if (optionsCount != rewardsCount) throw new GameEngineException("Each option must have exact one corresponding reward");
			if (optionsCount < 2) throw new GameEngineException("Fixed line must provide two or more options");
			ValidateRewards(rewards);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			this.rewards = rewards;
			this.options = options;
			this.coefficientRewards = new double[rewardsCount];
			for (int i = 0; i < coefficientRewards.Length; i++)
			{
				this.coefficientRewards[i] = CalculateRewards(rewards.ElementAt(i));
			}
			this.answers = new MultipleOptions(game, options);
			base.Text = ReplacePlaceholders(text, game);

			LogRewards();
		}

		internal FixedLine(FixedLine previousVersion, IEnumerable<int> rewards, string who, DateTime now) : base(previousVersion, who, now)
		{
			if (rewards == null) throw new ArgumentNullException(nameof(rewards));
			if (previousVersion.options.Count() != rewards.Count()) throw new GameEngineException("Each option must have exact one corresponding reward");
			ValidateRewards(rewards);

			this.rewards = rewards;
			this.options = previousVersion.options;
			this.coefficientRewards = new double[this.rewards.Count()];
			for (int i = 0; i < coefficientRewards.Length; i++)
			{
				this.coefficientRewards[i] = CalculateRewards(rewards.ElementAt(i));
			}
			this.answers = previousVersion.answers;

			if ( ! base.IsDraft  && ! base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
			
			LogRewards(previousVersion.Rewards);
		}

		internal FixedLine NewVersion(IEnumerable<int> rewards, string who, DateTime now)
		{
			var result = new FixedLine(this, rewards, who, now);
			return result;
		}

		private double CalculateRewards(int reward)
		{
			double result = 0;
			if (reward < 0)
			{
				result = 100 / (double)Math.Abs(reward);
			}
			else
			{
				result = Math.Abs(reward) / 100.0;
			}

			if (result == 0) throw new GameEngineException($"Prize reward for a {nameof(FixedLine)} can not be zero.");

			return result;
		}

		internal static void ValidateRewards(IEnumerable<int> rewards)
		{
			foreach (var reward in rewards) Line.ValidateReward(reward);
		}

		protected override double CoefficientReward(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			for (int i = 0; i < this.options.Count(); i++)
			{
				if (answer == this.answers.GetAnswer(this.options.ElementAt(i)))
				{
					return this.coefficientRewards[i];
				}
			}

			throw new GameEngineException("Wager does not have a valid chosen answer");
		}

		internal override string RewardAsString(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			FixedAnswer fixedAnswer = (FixedAnswer)answer;

			var ordinal = fixedAnswer.Ordinal;
			if (ordinal + 1 > this.options.Count()) throw new GameEngineException("Wager does not have a valid chosen answer");

			int result = rewards.ToArray<int>()[ordinal];
			return (result > 0 ? "+" : "-") + result;
		}

		internal WagerAnswer GetAnAnswer(string choosenAnswer)
		{
			if (string.IsNullOrWhiteSpace(choosenAnswer)) throw new ArgumentNullException(nameof(choosenAnswer));

			var result = this.answers.GetAnswer(choosenAnswer);
			return result;
		}

		internal bool ExistsOption(string option)
		{
			if (string.IsNullOrWhiteSpace(option)) throw new ArgumentNullException(nameof(option));

			var result = this.answers.ExistsOption(option);
			return result;
		}

		internal int GetReward(string option)
		{
			if (string.IsNullOrWhiteSpace(option)) throw new ArgumentNullException(nameof(option));

			var answer = this.answers.GetAnswer(option);
			return this.rewards.ElementAt(answer.Ordinal);
		}

		internal string ChosenOption()
        {
			return realAnswer.ChosenOption;
		}

		internal void SetRealAnswer(string answer)
		{
			if (string.IsNullOrWhiteSpace(answer)) throw new ArgumentNullException(nameof(answer));
			if (!this.IsLastVersion()) throw new GameEngineException("Answer should be set only for the original line");

			var realAnswer = this.answers.GetAnswer(answer);
			var currVersion = this;
			var exit = false;
			while (!exit)
			{
				currVersion.realAnswer = realAnswer;
				exit = currVersion.IsOriginalVersion();
				if(!exit) currVersion = (FixedLine)currVersion.PreviousVersion;
			}
		}

		internal int Count
		{
			get
			{
				return this.options.Count();
			}
		}

		internal override decimal Grade(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (this.realAnswer == null) throw new GameEngineException("Real answer of this line has not been set");

			var answer = (FixedAnswer)wager.ChosenAnswer;
			decimal prize = 0;
			bool won = answer == realAnswer;

			if (won)
			{
				prize = wager.ToWin();
				wager.GradeAsWinner();
			}
			else
			{
				prize = 0;
				wager.GradeAsLoser();
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal override string LineTypeAsString => LineType.FIXED_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (this.IsPending()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.FIXED_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(this.realAnswer.ChosenOption);
			message.AddProperty(base.Text);

			KafkaMessages options = new KafkaMessages();
			KafkaMessages rewards = new KafkaMessages();

			for (int i = 0; i < this.options.Count(); i++)
			{
				options.Add(this.options.ElementAt(i));
				rewards.Add(this.rewards.ElementAt(i));
			}

			message.AddProperty(options);
			message.AddProperty(rewards);
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedFixedLineEvent(timestamp, this);
			return result;
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent,  DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedFixedLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		private void LogRewards()
		{
			var rewardsAsText = string.Join(",", rewards);
			Log.AppendAtTheEndOfTheLastEntry($"Rewards: {rewardsAsText}");
		}

		private void LogRewards(IEnumerable<int> previousRewards)
		{
			var rewardsAsText = string.Join(",", rewards);
			var previousRewardsAsText = string.Join(",", previousRewards);
			var log = OriginalVersion.Log;
			log.AppendAtTheEndOfTheLastEntry($"Previous rewards: {previousRewardsAsText}. New rewards: {rewardsAsText}");
		}

		internal IEnumerable<int> Rewards
		{
			get
			{
				return this.rewards.ToList();
			}
		}

		internal IEnumerable<string> Options
		{
			get
			{
				return this.options.ToList();
			}
		}
	}
}
