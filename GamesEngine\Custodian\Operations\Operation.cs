﻿using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Custodian.Operations.DisbursementExecutionResponse;
using static GamesEngine.Custodian.Operations.Operation;

using static GamesEngine.Exchange.TransactionFee;
using static town.connectors.drivers.Result;

[assembly: InternalsVisibleTo("GuardianAPI")]
namespace GamesEngine.Custodian.Operations
{
	[Puppet]
	internal class Operations : Objeto
	{
		private List<Operation> operations;
		private int transationIdConsecutive = 0;
		private Guardian guardian;

		internal Operations(Guardian guardian)
		{
			this.guardian = guardian;
		}
		internal PendingDeposit CreateDeposit(bool itIsThePresent, DateTime creationDate, int transactionId, int authorizationId, string identificationDocumentNumber, string accountNumber, Currency amount, 
			string description, int groupId, PaymentProcessor processor, int domainId, string domainUrl)
		{
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));

			if (operations == null) operations = new List<Operation>();

			DateTime now = creationDate;

			transationIdConsecutive = transactionId;

			bool exist = guardian.Company.Sales.ExistsDomain(domainUrl);
			Domain domain;
			if (!exist)
			{
				domain = guardian.Company.Sales.CreateDomain(true, domainId, domainUrl, PaymentChannels.Agents.INSIDER);//Drivers: el Domain se estara enviando 0. El mensaje para notificar solo podra con dominios existentes
			}
			else
			{
				domain = guardian.Company.Sales.DomainFrom(domainUrl);
			}

			PendingDeposit pendingDeposit = new PendingDeposit(
				guardian,
				creationDate,
				transactionId,
				authorizationId,
				description,
				amount,
				groupId,
				processor,
				domain,
				identificationDocumentNumber,
				accountNumber);

			operations.Add(pendingDeposit);

			return pendingDeposit;
		}

		internal PendingWithDrawal CreateWithdrawal(bool itIsThePresent, DateTime creationDate, int transactionId, int referenceNumber, string identificationDocumentNumber, Currency disbursementAmount, 
			int group, PaymentProcessor processor, int domainId, string domainUrl, string employeeName)
		{
			if (processor == null) throw new ArgumentNullException(nameof(processor));
			if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));

			if (operations == null) operations = new List<Operation>();

			DateTime now = creationDate;

			transationIdConsecutive = transactionId;
			bool exist = guardian.Company.Sales.ExistsDomain(domainUrl);
			Domain domain;
			if ( ! exist)
			{
				domain = guardian.Company.Sales.CreateDomain(true, domainId, domainUrl, PaymentChannels.Agents.INSIDER);
			}
			else
			{
				domain = guardian.Company.Sales.DomainFrom(domainUrl);
			}

			Profiles approvers = guardian.RiskAssignments().SearchProfilesFor(disbursementAmount);

			PendingWithDrawal withDrawal = new PendingWithDrawal(
				guardian,
				creationDate,
				transactionId,
				referenceNumber,
				disbursementAmount,
				group,
				processor, 
				domain,
				approvers,
				identificationDocumentNumber,
				employeeName);

			operations.Add(withDrawal);
			
			if (itIsThePresent)
			{
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, withDrawal.GenerateMessage(now));
			}

			return withDrawal;
		}
		internal int NewOperationNumber()
		{
			transationIdConsecutive += 1;
			return transationIdConsecutive;
		}
		internal WithDrawal SearchWithdrawalByTransactionId(int transactionId)
		{
			if (operations != null) 
			{
				foreach (Operation operation in operations)
				{
					if (operation.TransactionId == transactionId)
					{
						return (WithDrawal)operation;
					}
				}
			}
			throw new GameEngineException($"There is no {nameof(Operation)} with id {transactionId}");
		}

		internal bool Remove(Operation withDrawal)
		{
			return operations.Remove(withDrawal);
		}

		internal void Approve(bool itIsThePresent, DateTime now, int transactionId, string approverEmail)
		{
			if (string.IsNullOrEmpty(approverEmail)) throw new ArgumentNullException(nameof(approverEmail));
			if (transactionId <= 0) throw new ArgumentNullException(nameof(transactionId));

			int index = operations.FindIndex(operation => operation.TransactionId == transactionId);
			GamesEngine.Custodian.Operations.PendingWithDrawal withdrawal = (GamesEngine.Custodian.Operations.PendingWithDrawal)operations[index];
			Approver approver = guardian.Approvers().Get(approverEmail);

			if ( ! withdrawal.RequiredProfilesApproval.Exists(approver))
			{
				throw new GameEngineException($"{nameof(Approver)} can not approve trandaction number {transactionId}.");
			}

			bool itsCompletlyApproved = withdrawal.Approve(itIsThePresent, now, approver);

			if (itsCompletlyApproved)
			{
				InprocessWithDrawal inprocessWithDrawal = new InprocessWithDrawal(guardian, withdrawal);
				operations.RemoveAt(index);
				operations.Add(inprocessWithDrawal);
				if (itIsThePresent) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, inprocessWithDrawal.GenerateMessageWithProfiles(now));
			}
			
		}

		internal void RemoveAt(int index)
		{
			operations.RemoveAt(index);
		}
		internal void ApproveWithIds(bool itIsThePresent, DateTime now, int transactionId, string approverEmail, List<int> profielsAuthorizedByTheOwnerIds)
		{
			Profiles profielsAuthorizedByTheOwner = new Profiles();
			foreach (int profielsAuthorizedByTheOwnerId in profielsAuthorizedByTheOwnerIds)
			{
				profielsAuthorizedByTheOwner.Add(guardian.Profiles().SearchById(profielsAuthorizedByTheOwnerId));
			}
			Approve(itIsThePresent, now, transactionId, approverEmail, profielsAuthorizedByTheOwner);
		}
		internal void Approve(bool itIsThePresent, DateTime now, int transactionId, string approverEmail, Profiles profielsAuthorizedByTheOwner)
		{
			if (string.IsNullOrEmpty(approverEmail)) throw new ArgumentNullException(nameof(approverEmail));
			if (transactionId <= 0) throw new ArgumentNullException(nameof(transactionId));

			int index = operations.FindIndex(operation => operation.TransactionId == transactionId);
			GamesEngine.Custodian.Operations.Operation operation = operations[index];
			if(!(operation is PendingWithDrawal)) throw new GameEngineException($"Transaction with id { operation.TransactionId} is not {nameof(StatusCodes.PENDING)}.");
			GamesEngine.Custodian.Operations.PendingWithDrawal withdrawal = (GamesEngine.Custodian.Operations.PendingWithDrawal)operation; 
			Approver approver = guardian.Approvers().Get(approverEmail);

			bool theApproverItsAnOwner = approver.ItsAnOwner;

			if (!theApproverItsAnOwner)
			{
				throw new GameEngineException($"{nameof(Approver)} can not approve trandaction number {transactionId}.");
			}

			bool itsCompletlyApproved = withdrawal.Approve(itIsThePresent, now, approver, profielsAuthorizedByTheOwner);

			if (itsCompletlyApproved)
			{
				InprocessWithDrawal inprocessWithDrawal = new InprocessWithDrawal(guardian, withdrawal);
				operations.RemoveAt(index);
				operations.Add(inprocessWithDrawal);

				if (itIsThePresent) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForGuardianInternalOperations, inprocessWithDrawal.GenerateMessageWithProfiles(now));
			}

		}
		internal Operations FilterBy(StatusCodes status)
		{
			Operations result = new Operations(guardian);
			foreach (Operation operation in operations)
			{
				if (operation is WithDrawal && ((WithDrawal)operation).Status== status)
				{
					result.Add(operation);
				}
			}
			return result;
		}

		internal void Add(Operation operation)
		{
			if (operation == null) throw new ArgumentNullException(nameof(operation));
			if (operations == null) operations = new List<Operation>();
			operations.Add(operation);
		}

		internal Operation First()
		{
			if (operations == null || operations.Count == 0) throw new GameEngineException($"There is no operations.");

			return operations.First();
		}
		internal bool TryToGet(int transactionId, out Operation operation)
		{
			operation = null;
			if (operations == null) return false;
			foreach (Operation inprocessWithDrawal in operations)
			{
				if (inprocessWithDrawal.TransactionId == transactionId)
				{
					operation = inprocessWithDrawal;
					return true;
				}
			}

			return false;
		}

		internal Operation SearchFor(int transactionId)
		{
			if(operations==null) throw new GameEngineException($"There is no transacion with Id {transactionId}");
			foreach (Operation operation in operations)
			{
				if (operation.TransactionId == transactionId)
				{
					return operation;
				}
			}
			throw new GameEngineException($"There is no transacion with Id {transactionId}");
		}


	}
	
	[Puppet]
	public abstract class Operation : Objeto
	{
		public enum StatusCodes { PENDING=0, IN_PROCESS=1, COMPLETE = 2 };
		private readonly int transactionId;
		internal int TransactionId { get { return transactionId; } }
		internal Operation( int transactionId)
		{
			this.transactionId = transactionId;
		}
	}

	public sealed class OperationMessage : KafkaMessage
	{
		private const string NO_EXECUTION = "NE";
		private const string NO_FEES = "NF";
		public int TransactionId { get; private set; }
		public string Description { get; private set; }
		public Currency DisbursementAmount { get; private set; }
		public int GroupId { get; private set; }
		public string ProcessorId { get; private set; }
		public TransactionType Abbreviation { get; private set; }
		public TransactionFees Fees { get; private set; }
		public DateTime CreationDate { get; private set; }
		public int DomainId { get; private set; }
		public string DomainUrl { get; private set; }
		public Execution Execution { get; private set; }
		public string AccountNumber { get; private set; }
		public string Identifier { get; private set; }
		public int StoreId { get; private set; }
		public string EmployeeName { get; private set; }

		internal OperationMessage(string accountNumber, TransactionType abbreviation, DateTime creationDate, int transactionId, string description, Currency disbursementAmount, ProcessorPaymentMethod group, 
			string processorId, int domainId, string domainUrl, TransactionFees fees, Execution execution, string identificationDocumentNumber, int storeId, string employeeName)
		{
			if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrEmpty(processorId)) throw new ArgumentNullException(nameof(processorId));
			if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
			if (fees == null) throw new ArgumentNullException(nameof(fees));

			AccountNumber = accountNumber;
			Description = description;
			DisbursementAmount = disbursementAmount;
			GroupId = group.Id;
			ProcessorId = processorId;
			Abbreviation = abbreviation;
			Fees = fees;
			CreationDate = creationDate;
			TransactionId = transactionId;
			DomainId = domainId;
			DomainUrl = domainUrl;
			Execution = execution;
			Identifier = identificationDocumentNumber;
			StoreId = storeId;
			EmployeeName = employeeName;
		}

		public OperationMessage(string msg):base(msg)
		{
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)Abbreviation).
			AddProperty(AccountNumber).
			AddProperty(CreationDate).
			AddProperty(TransactionId).
			AddProperty(ProcessorId).
			AddProperty(GroupId).
			AddProperty(Description).
			AddProperty(DisbursementAmount.Value).
			AddProperty(DisbursementAmount.CurrencyCode).
			AddProperty(DomainId).
			AddProperty(DomainUrl).
			AddProperty(Identifier).
			AddProperty(StoreId).
			AddProperty(EmployeeName);

			if (Execution != default(Execution))
			{
				AddProperty((int)Execution.Status);
				string details = (!string.IsNullOrEmpty(Execution.Details)) ? 
					Execution.Details : 
					NO_EXECUTION;
				AddProperty(details);
			}
			else
			{
				AddProperty(NO_EXECUTION);
			}

			if (Fees.List() != null)
			{
				foreach (TransactionFee fee in Fees.List())
				{
					AddProperty((int)fee.Type).
					AddProperty(fee.Value);
				}
			}
			else
			{
				AddProperty(NO_FEES);
			}

		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			this.Abbreviation = (TransactionType)int.Parse(message[fieldOrder++]);
			this.AccountNumber = message[fieldOrder++];
			int year = int.Parse(message[fieldOrder++]);
			int month = int.Parse(message[fieldOrder++]);
			int day = int.Parse(message[fieldOrder++]);
			int hour = int.Parse(message[fieldOrder++]);
			int minute = int.Parse(message[fieldOrder++]);
			int second = int.Parse(message[fieldOrder++]);
			this.CreationDate = new DateTime(year, month, day, hour, minute, second);

			this.TransactionId = int.Parse(message[fieldOrder++]);
			this.ProcessorId = message[fieldOrder++];
			this.GroupId = int.Parse(message[fieldOrder++]);
			this.Description = message[fieldOrder++];

			decimal disbursementAmountDecimal = decimal.Parse(message[fieldOrder++]);
			Coin disbursementAmountCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			this.DisbursementAmount = Currency.Factory(disbursementAmountCurrencyCode, disbursementAmountDecimal);

			this.DomainId = int.Parse(message[fieldOrder++]);
			this.DomainUrl = message[fieldOrder++];
			Identifier = message[fieldOrder++];
			StoreId = int.Parse(message[fieldOrder++]);
			EmployeeName = message[fieldOrder++];

			string value= message[fieldOrder++];
			if (value != NO_EXECUTION)
			{
				string details = message[fieldOrder++];
				details = (details == NO_EXECUTION) ? "" : details;
				this.Execution = new Execution(
					(TransactionStatus)int.Parse(value),
					details
					);
			}
			
			Fees = new TransactionFees();
			
			value = message[fieldOrder];
			if (value != NO_FEES)
			{
				for (; fieldOrder < message.Length;)
				{
					FeeType feeType = (FeeType)int.Parse(message[fieldOrder++]);
					decimal decimalfee = decimal.Parse(message[fieldOrder++]);
					Currency fee = Currency.Factory(disbursementAmountCurrencyCode, decimalfee);

					TransactionFee transactionFee = TransactionFee.Factory(fee, feeType);
					Fees.Add(transactionFee);
				}
			}
		}

    }
	public abstract class InternalOperationMessage : KafkaMessage
	{
		public HEADER Header { get; }

		public enum HEADER { OPERATION = 0, OPERATION_WITH_PROFILES = 1, APPROVER = 2, PROFILE = 3, ACCOUNT = 4, CURRENCY = 5 }

		internal InternalOperationMessage(HEADER header)
		{
			Header = header;
		}

		public InternalOperationMessage(HEADER header, string message) : base(message)
		{
			Header = header;//TODO xxxx Revisar con Cris
		}

		public static InternalOperationMessage Factory(string message)
		{
			HEADER header = (HEADER)int.Parse(message.Substring(0, 1));

			switch (header)
			{
				case HEADER.OPERATION:
					return new InternalOperationUpdateMessage(message.Substring(2));
				case HEADER.OPERATION_WITH_PROFILES:
					return new InternalOperationUpdateMessageWithProfiles(message.Substring(2));
				case HEADER.APPROVER:
					return new ApproverCreationMessage(message.Substring(2));
				case HEADER.PROFILE:
					return new ProfileCreationMessage(message.Substring(2));
				case HEADER.ACCOUNT:
					return new AccountNumberCreationMessage(message.Substring(2));
				case HEADER.CURRENCY:
					return new CoinCreationMessage(message.Substring(2));
				default:
					throw new GameEngineException($"There is no header for {header}.");
			}
		}
	}

	public sealed class ApproverCreationMessage : InternalOperationMessage
	{
		public int AccountId { get; private set; }
		public string Email { get; private set; }

		internal ApproverCreationMessage(int accountId, string email): base(HEADER.APPROVER)
		{
			AccountId = accountId;
			Email = email;
		}

		public ApproverCreationMessage(string message): base(HEADER.APPROVER, message)
		{
			
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty(AccountId).
			AddProperty(Email);
		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			AccountId = int.Parse(message[fieldOrder++]);
			Email = message[fieldOrder++];
		}
    }
	public sealed class ProfileCreationMessage : InternalOperationMessage
	{
		public int ProfileId { get; private set; }
		public string Name { get; private set; }

		internal ProfileCreationMessage(int profileId, string name): base(HEADER.PROFILE)
		{
			ProfileId = profileId;
			Name = name;
		}
		public ProfileCreationMessage(string message): base(HEADER.PROFILE, message)
		{

		}

		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			ProfileId = int.Parse(message[fieldOrder++]);
			Name = message[fieldOrder++];
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty(ProfileId).
			AddProperty(Name);
		}
    }

	public sealed class AccountNumberCreationMessage : InternalOperationMessage
	{
		public int Id { get; private set; }
		public string Number { get; private set; }
		public string ProcessorKey { get; private set; }
		public string CurrencyCode { get { return Coin.Iso4217Code;  } }
		public Coin Coin { get; private set; }

		internal AccountNumberCreationMessage(int profileId, string number, string processorKey, Coin currencyCode): base(HEADER.ACCOUNT)
		{
			Id = profileId;
			Number = number;
			ProcessorKey = processorKey;
			Coin = currencyCode;
		}
		public AccountNumberCreationMessage(string message): base(HEADER.ACCOUNT, message)
		{

		}
		//public override string Serialize()
		//{
		//	return Result();
		//}
		protected override void Deserialize(string [] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			Id = int.Parse(message[fieldOrder++]);
			Number = message[fieldOrder++];
			ProcessorKey = message[fieldOrder++];
			Coin = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty(Id).
			AddProperty(Number).
			AddProperty(ProcessorKey).
			AddProperty(CurrencyCode);
		}
    }

	public sealed class CoinCreationMessage : InternalOperationMessage
	{
		public Coin Coin { get; private set; }

		internal CoinCreationMessage(Coin coin) : base(HEADER.CURRENCY)
		{
			if (coin == null) throw new ArgumentException(nameof(coin));

			Coin = coin;
		}
		public CoinCreationMessage(string message) : base(HEADER.CURRENCY, message)
		{

		}

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			var id = int.Parse(message[fieldOrder++]);
			var isocode = message[fieldOrder++];
			var sign = message[fieldOrder++];
			var precision = int.Parse(message[fieldOrder++]);
			var unicode = char.Parse(message[fieldOrder++]);
			var name = message[fieldOrder++];
			var type = (CoinType)int.Parse(message[fieldOrder++]);
			Coin = new Coin(id, isocode, sign, precision, unicode, name, type);
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty(Coin.Id).
			AddProperty(Coin.Iso4217Code).
			AddProperty(Coin.Sign).
			AddProperty(Coin.DecimalPrecision).
			AddProperty(Coin.Unicode).
			AddProperty(Coin.Name).
			AddProperty((int)Coin.Type)
			;
		}
	}

	public sealed class InternalOperationUpdateMessage : InternalOperationMessage
	{
		private const string APPROVERS_END = "AE";
		public int TransactionId { get; private set; }
		public int AccountId { get; private set; }
		public int ReferenceNumber { get; private set; }
		public string Description { get; private set; }
		public Currency DisbursementAmount { get; private set; }
		public int Group { get; private set; }
		public string ProcessorId { get; private set; }
		public TransactionType Abbreviation { get; private set; }
		public TransactionFees Fees { get; private set; }
		public DateTime CreationDate { get; private set; }
		public DateTime Date { get; private set; }
		public int Approvals { get; private set; }
		public int ApprovalsRequired { get; private set; }
		public bool Scheduled { get; private set; }
		public bool HasExecutions { get; private set; }
		public int PercentageExecuted { get; private set; }
		public string Identifier { get; private set; }
		public StatusCodes Status { get; set; }
		public string Domain { get; internal set; }
		internal Approvers Approvers { get; set; }
		public InternalOperationUpdateMessage(TransactionType abbreviation, DateTime creationDate, DateTime date, int transctionId, int accountId, int referenceNumber, string description, 
			Currency disbursementAmount, int group, string processorId, int approvals, int approvalsRequired, bool scheduled, string domain, Approvers approvers, StatusCodes status, bool hasExecutions, 
			int percentageExecuted, string identificationDocumentNumber)
			: base(HEADER.OPERATION)
		{
			if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrEmpty(processorId)) throw new ArgumentNullException(nameof(processorId));
			if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));
			if (approvers == null) throw new ArgumentNullException(nameof(approvers));

			TransactionId = transctionId;
			AccountId = accountId;
			Description = description;
			DisbursementAmount = disbursementAmount;
			Group = group;
			ProcessorId = processorId;
			Abbreviation = abbreviation;
			Fees = new TransactionFees();
			CreationDate = creationDate;
			Date = date;
			ReferenceNumber = referenceNumber;
			Approvals = approvals;
			ApprovalsRequired = approvalsRequired;
			Scheduled = scheduled;
			Domain = domain;
			Approvers = approvers;
			Status = status;
			HasExecutions = hasExecutions;
			PercentageExecuted = percentageExecuted;
			Identifier = identificationDocumentNumber;
		}

		public InternalOperationUpdateMessage(string msg): base(HEADER.OPERATION, msg)
		{

		}

		internal void Add(TransactionFee fee)
		{
			if (DisbursementAmount.CurrencyCode != fee.CurrencyCode) throw new GameEngineException($"{nameof(DisbursementAmount)} and {nameof(fee)} has no the same currency.");

			Fees.Add(fee);
		}

		protected override void Deserialize(string [] messages, out int fieldOrder)
		{
			base.Deserialize(messages, out fieldOrder);
			Abbreviation = (TransactionType)int.Parse(messages[fieldOrder++]);

			int year = int.Parse(messages[fieldOrder++]);
			int month = int.Parse(messages[fieldOrder++]);
			int day = int.Parse(messages[fieldOrder++]);
			int hour = int.Parse(messages[fieldOrder++]);
			int minute = int.Parse(messages[fieldOrder++]);
			int second = int.Parse(messages[fieldOrder++]);
			CreationDate = new DateTime(year, month, day, hour, minute, second);

			year = int.Parse(messages[fieldOrder++]);
			month = int.Parse(messages[fieldOrder++]);
			day = int.Parse(messages[fieldOrder++]);
			hour = int.Parse(messages[fieldOrder++]);
			minute = int.Parse(messages[fieldOrder++]);
			second = int.Parse(messages[fieldOrder++]);
			Date = new DateTime(year, month, day, hour, minute, second);

			TransactionId = int.Parse(messages[fieldOrder++]);
			AccountId = int.Parse(messages[fieldOrder++]);
			ProcessorId = messages[fieldOrder++];
            Group = int.Parse(messages[fieldOrder++]);
			Description = messages[fieldOrder++];

			decimal disbursementAmountDecimal = decimal.Parse(messages[fieldOrder++]);
			Coin coin = Coinage.KafkaProperty2Coin(messages[fieldOrder++]);
			DisbursementAmount = Currency.Factory(coin, disbursementAmountDecimal);

			ReferenceNumber = int.Parse(messages[fieldOrder++]);
			Approvals = int.Parse(messages[fieldOrder++]);
			ApprovalsRequired = int.Parse(messages[fieldOrder++]);
			Scheduled = bool.Parse(messages[fieldOrder++]);
			Status = (StatusCodes)int.Parse(messages[fieldOrder++]);
			Domain = messages[fieldOrder++];
			HasExecutions = bool.Parse(messages[fieldOrder++]);
			PercentageExecuted = int.Parse(messages[fieldOrder++]);
			Identifier = messages[fieldOrder++];

			Approvers = new Approvers();
			for (; fieldOrder < messages.Length;)
			{
				string email = messages[fieldOrder++];
				if (email == APPROVERS_END) break;

				int id = int.Parse(messages[fieldOrder++]);

				Approvers.Add(new Approver(id, email));
			}

			Fees = new TransactionFees();
			for (; fieldOrder < messages.Length;)
			{
				FeeType feeType = (FeeType)int.Parse(messages[fieldOrder++]);
				decimal decimalfee = decimal.Parse(messages[fieldOrder++]);
				Currency fee = Currency.Factory(coin, decimalfee);

				TransactionFee transactionFee = TransactionFee.Factory(fee, feeType);
				Fees.Add(transactionFee);
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty((int)Abbreviation).
			AddProperty(CreationDate).
			AddProperty(Date).
			AddProperty(TransactionId).
			AddProperty(AccountId).
			AddProperty(ProcessorId).
			AddProperty((int)Group).
			AddProperty(Description).
			AddProperty(DisbursementAmount.Value).
			AddProperty(DisbursementAmount.Coin.Iso4217Code).
			AddProperty(ReferenceNumber).
			AddProperty(Approvals).
			AddProperty(ApprovalsRequired).
			AddProperty(Scheduled).
			AddProperty((int)Status).
			AddProperty(Domain).
			AddProperty(HasExecutions).
			AddProperty(PercentageExecuted).
			AddProperty(Identifier);

			if (Approvers.List().Count() > 0)
			{
				foreach (Approver approver in Approvers.List())
				{
					AddProperty(approver.EmailAddress);
					AddProperty(approver.Id);
				}
			}

			AddProperty(APPROVERS_END);

			if (Fees.List() != null)
			{
				foreach (TransactionFee fee in Fees.List())
				{
					AddProperty((int)fee.Type).
					AddProperty(fee.Value);
				}
			}
		}
    }
	
	public sealed class InternalOperationUpdateMessageWithProfiles : InternalOperationMessage
	{
		private const string APPROVERS_END = "AE";
		private const string PROFILES_END = "PE";
		public int TransactionId { get; private set; }
		public int AccountId { get; private set; }
		public int ReferenceNumber { get; private set; }
		public string Description { get; private set; }
		public Currency DisbursementAmount { get; private set; }
		public int Group { get; private set; }
		public string ProcessorId { get; private set; }
		public TransactionType Abbreviation { get; private set; }
		public TransactionFees Fees { get; private set; }
		public DateTime CreationDate { get; private set; }
		public DateTime Date { get; private set; }
		public int Approvals { get; private set; }
		public int ApprovalsRequired { get; private set; }
		public bool Scheduled { get; private set; }
		public bool HasExecutions { get; private set; }
		public StatusCodes Status { get; set; }
		public string Domain { get; internal set; }
		internal Approvers Approvers { get; set; }
		internal List<int> RequiredProfilesIds { get; set; }
		public int PercentageExecuted { get; private set; }
		public string Identifier { get; private set; }

		public InternalOperationUpdateMessageWithProfiles(TransactionType abbreviation, DateTime creationDate, DateTime date, int transctionId, int accountId, int referenceNumber, string description, 
			Currency disbursementAmount, int group, string processorId, int approvals, int approvalsRequired, bool scheduled, string domain, Approvers approvers, StatusCodes status, 
			List<int> requiredProfilesApproval, bool hasExecutions, int percentageExecuted, string identificationDocumentNumber)
			: base(HEADER.OPERATION_WITH_PROFILES)
		{
			if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrEmpty(processorId)) throw new ArgumentNullException(nameof(processorId));
			if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));
			if (approvers == null) throw new ArgumentNullException(nameof(approvers));

			TransactionId = transctionId;
			AccountId = accountId;
			Description = description;
			DisbursementAmount = disbursementAmount;
			Group = group;
			ProcessorId = processorId;
			Abbreviation = abbreviation;
			Fees = new TransactionFees();
			CreationDate = creationDate;
			Date = date;
			ReferenceNumber = referenceNumber;
			Approvals = approvals;
			ApprovalsRequired = approvalsRequired;
			Scheduled = scheduled;
			Domain = domain;
			Approvers = approvers;
			Status = status;
			RequiredProfilesIds = requiredProfilesApproval;
			HasExecutions = hasExecutions;
			PercentageExecuted = percentageExecuted;
			Identifier = identificationDocumentNumber;
		}

		public InternalOperationUpdateMessageWithProfiles(string msg): base(HEADER.OPERATION_WITH_PROFILES, msg)
		{

		}

		internal void Add(TransactionFee fee)
		{
			if (DisbursementAmount.CurrencyCode != fee.CurrencyCode) throw new GameEngineException($"{nameof(DisbursementAmount)} and {nameof(fee)} has no the same currency.");

			Fees.Add(fee);
		}

		protected override void Deserialize(string [] messages, out int fieldOrder)
		{
			base.Deserialize(messages, out fieldOrder);
			Abbreviation = (TransactionType)int.Parse(messages[fieldOrder++]);

			int year = int.Parse(messages[fieldOrder++]);
			int month = int.Parse(messages[fieldOrder++]);
			int day = int.Parse(messages[fieldOrder++]);
			int hour = int.Parse(messages[fieldOrder++]);
			int minute = int.Parse(messages[fieldOrder++]);
			int second = int.Parse(messages[fieldOrder++]);
			CreationDate = new DateTime(year, month, day, hour, minute, second);

			year = int.Parse(messages[fieldOrder++]);
			month = int.Parse(messages[fieldOrder++]);
			day = int.Parse(messages[fieldOrder++]);
			hour = int.Parse(messages[fieldOrder++]);
			minute = int.Parse(messages[fieldOrder++]);
			second = int.Parse(messages[fieldOrder++]);
			Date = new DateTime(year, month, day, hour, minute, second);

			TransactionId = int.Parse(messages[fieldOrder++]);
			AccountId = int.Parse(messages[fieldOrder++]);
			ProcessorId = messages[fieldOrder++];
            Group = int.Parse(messages[fieldOrder++]);
			Description = messages[fieldOrder++];

			decimal disbursementAmountDecimal = decimal.Parse(messages[fieldOrder++]);
			Coin disbursementAmountCurrencyCode = Coinage.KafkaProperty2Coin(messages[fieldOrder++]);
			DisbursementAmount = Currency.Factory(disbursementAmountCurrencyCode, disbursementAmountDecimal);

			ReferenceNumber = int.Parse(messages[fieldOrder++]);
			Approvals = int.Parse(messages[fieldOrder++]);
			ApprovalsRequired = int.Parse(messages[fieldOrder++]);
			Scheduled = bool.Parse(messages[fieldOrder++]);
			Status = (StatusCodes)int.Parse(messages[fieldOrder++]);
			Domain = messages[fieldOrder++];
			HasExecutions = bool.Parse(messages[fieldOrder++]);
			PercentageExecuted = int.Parse(messages[fieldOrder++]);
			Identifier = messages[fieldOrder++];

			Approvers = new Approvers();
			for (; fieldOrder < messages.Length;)
			{
				string email = messages[fieldOrder++];
				if (email == APPROVERS_END) break;

				int id = int.Parse(messages[fieldOrder++]);

				Approvers.Add(new Approver(id, email));
			}

			Fees = new TransactionFees();
			for (; fieldOrder < messages.Length;)
			{
				string token = messages[fieldOrder++];
				if (token == PROFILES_END) break;

				FeeType feeType = (FeeType)int.Parse(messages[fieldOrder++]);
				decimal decimalfee = decimal.Parse(messages[fieldOrder++]);
				Currency fee = Currency.Factory(disbursementAmountCurrencyCode, decimalfee);

				TransactionFee transactionFee = TransactionFee.Factory(fee, feeType);
				Fees.Add(transactionFee);
			}

			RequiredProfilesIds = new List<int>();
			for (; fieldOrder < messages.Length;)
			{
				int profileId = int.Parse(messages[fieldOrder++]);
				RequiredProfilesIds.Add(profileId);
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)Header).
			AddProperty((int)Abbreviation).
			AddProperty(CreationDate).
			AddProperty(Date).
			AddProperty(TransactionId).
			AddProperty(AccountId).
			AddProperty(ProcessorId).
			AddProperty((int)Group).
			AddProperty(Description).
			AddProperty(DisbursementAmount.Value).
			AddProperty(DisbursementAmount.Coin.Iso4217Code).
			AddProperty(ReferenceNumber).
			AddProperty(Approvals).
			AddProperty(ApprovalsRequired).
			AddProperty(Scheduled).
			AddProperty((int)Status).
			AddProperty(Domain).
			AddProperty(HasExecutions).
			AddProperty(PercentageExecuted).
			AddProperty(Identifier);


			if (Approvers.List().Count() > 0)
			{
				foreach (Approver approver in Approvers.List())
				{
					AddProperty(approver.EmailAddress);
					AddProperty(approver.Id);
				}
			}

			AddProperty(APPROVERS_END);

			if (Fees.List() != null)
			{
				foreach (TransactionFee fee in Fees.List())
				{
					AddProperty((int)fee.Type).
					AddProperty(fee.Value);
				}
			}

			AddProperty(PROFILES_END);

			if (RequiredProfilesIds != null)
			{
				foreach (int profileId in RequiredProfilesIds)
				{
					AddProperty(profileId);
				}
			}
		}
    }
	public sealed class StoredOperation
	{
		public int Id { get; internal set; }
		public int TransactionId { get; internal set; }
		public string CustomerId { get; internal set; }
		public string Abbreviation { get; internal set; }
		public decimal Fees { get; internal set; }
		public string ApproverName { get; internal set; }
		public decimal Value { get; internal set; }
		public int Reference { get; internal set; }
		public DateTime CreationDate { get; internal set; }
		public int CurrencyId { get; internal set; }
		public int ProcessorAccountId { get; internal set; }
		[IgnoreDataMember]
		public Coin Coin => Coinage.GetById(CurrencyId);
		public string CurrencyCode => Coin.Iso4217Code;
		public int DomainId { get; internal set; }
		public string DomainUrl { get; internal set; }
		public DateTime Date { get; internal set; }
		public bool Scheduled { get; internal set; }
		public int Required_approvals { get; internal set; }
		public int Approvals { get; internal set; }
		public string ValueFormatted { get; internal set; }
		public string DateDateFormated { get; internal set; }
		public string CreationDateFormated { get; internal set; }
		public string Status { get; internal set; }
		public bool HasExecutions { get; internal set; }
		public int PercentageExecuted { get; internal set; }
	}
}
