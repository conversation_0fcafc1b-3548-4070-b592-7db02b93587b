﻿using System;
using System.Net.Http.Json;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class ConfirmationsInboundTask : InboundTask
    {
        internal Deposit Deposit { get; private set; }

        internal int TotalConfirmations { get; private set; }

        internal int CurrentConfirmations { get; private set; }

        internal Invoice Invoice { get; private set; }

        internal SentinelInboundTasks SentinelInboundTasks { get; private set; }

        internal CancellationTokenSource Cancellation { get; private set; } = new();

        internal ConfirmationsInboundTask(SentinelInboundTasks inboundTasks, Deposit deposit, int totalConfirmations)
        {
            if (inboundTasks == null) throw new ArgumentNullException(nameof(inboundTasks), "Inbound tasks cannot be null.");
            if (deposit == null) throw new ArgumentNullException(nameof(deposit), "Deposit cannot be null.");
            if (totalConfirmations < 0) throw new ArgumentNullException("Total confirmations cannot be negative.", nameof(totalConfirmations));

            SentinelInboundTasks = inboundTasks;
            Deposit = deposit;
            TotalConfirmations = totalConfirmations;
        }

        internal void StartInboundTask(Invoice invoice)
        {
            if (invoice == null) throw new ArgumentNullException(nameof(invoice), "Invoice cannot be null.");
            if (string.IsNullOrWhiteSpace(invoice.Id)) throw new ArgumentNullException("Invoice ID cannot be null or empty.", nameof(invoice.Id));
            Invoice = invoice;
            StartInboundTask();
        }

        internal override void StartInboundTask()
        {
            if (Cancellation.IsCancellationRequested)
            {
                SentinelInboundTasks.Detach(Deposit.InvoiceId);
                return;
            }

            if (Invoice == null) throw new ArgumentNullException(nameof(Invoice));
            if (task == null)
            {
                task = Task.Run(async () =>
                {
                    while (CurrentConfirmations < TotalConfirmations && !Cancellation.IsCancellationRequested)
                    {
                        await Task.Delay(SentinelInboundTasks.DELAY_SECONDS_PER_CONFIRMATION * 1000);                        
                        try
                        {
                            CurrentConfirmations = await PaymentManager.TotalConfirmatiosAsync(Invoice);
                        }
                        catch(Exception e)
                        {
                            Loggers.GetIntance().Sentinel.Error($"Failed to get confirmations for invoice {Invoice.Id}.", e);
                        }
                    }
                    if (Cancellation.IsCancellationRequested) return; // If the task is cancelled, exit early.

                    //await ConfirmedDepositAsync(DateTime.Now, Deposit, Invoice.Status);
                    await ConfirmedDepositAsync(DateTime.Now, Deposit);
                    SentinelInboundTasks.Detach(this);
                });
            }
        }

        public override string ToString()
        {
            string status = string.Empty;
            if (task == null)
            {
                status = "Not started";
            }
            else if (task.IsCompleted)
            {
                status = "Completed";
            }
            else if (task.IsCanceled)
            {
                status = "Canceled";
            }
            else if (task.IsFaulted)
            {
                status = "Faulted";
            }
            else
            {
                status = "Running";
            }
            return $"Liquidity Deposit ID {Deposit.Id} Has Risk Confirmations: {TotalConfirmations}, Current Confirmations: {CurrentConfirmations} and Task Status: {status}";
        }

        private async Task ConfirmedDepositAsync(DateTime now, Deposit draftConfirmDeposit)
        {
            if (now == DateTime.MinValue) throw new ArgumentException("The provided date is not valid.", nameof(now));
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit), "Unconfirmed deposit cannot be null.");

            try
            {
                if (!TownSettings.TryToGetActorUrl("LIQUIDITY", out string hostnameIp)) throw new ArgumentException("Hostname or IP address is not configured.", nameof(hostnameIp));

                var client = new HttpClient();
                using var request = new HttpRequestMessage(HttpMethod.Post, $"{hostnameIp}/api/liquidity/{draftConfirmDeposit.Kind}/deposit/{draftConfirmDeposit.Id}/confirm")
                {
                    Content = JsonContent.Create(new ConfirmedDepositDueBody()
                    {
                        Due = Invoice.Due,
                        TotalPaid = Invoice.TotalPaid,
                        Rate = Invoice.Rate,
                    })
                };

                HttpResponseMessage response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    Loggers.GetIntance().Sentinel.Debug($"Confirmed deposit {draftConfirmDeposit.Id} at {now} successfully notified.");
                }
                else
                {
                    Loggers.GetIntance().Sentinel.Debug($"Failed to notify confirmed deposit {draftConfirmDeposit.Id} at {now}. Status code: {response.StatusCode}. Body data {Invoice.Due}, {Invoice.TotalPaid}, {Invoice.Rate}, {draftConfirmDeposit.Kind}, {draftConfirmDeposit.Id}");
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming deposit {draftConfirmDeposit.Id} at {now}: {ex.Message}", ex);
            }
        }
    }
}
