﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class OverUnderLine : Line
	{
		private readonly int overReward;
		private readonly int underReward;
		private readonly double score;
		private readonly double overCoefficientReward;
		private readonly double underCoefficientReward;
		private OverUnderAnswer realAnswer;

		internal OverUnderLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, double score, int overReward, int underReward, string text, string who, DateTime now) : base(lineId, question, tier, game, shelve, index, who, now)
		{
			ValidateReward(overReward, underReward);
			ValidateSpread(score, game.Sport);
			if (string.IsNullOrWhiteSpace(text)) throw new ArgumentNullException(nameof(text));

			this.overReward = overReward;
			this.underReward = underReward;
			this.overCoefficientReward = CalculateRewardCoefficient(OverUnderAnswer.OVER);
			this.underCoefficientReward = CalculateRewardCoefficient(OverUnderAnswer.UNDER);
			this.score = score;
			base.Text = ReplacePlaceholders(text, game);

			LogRewards();
		}

		internal OverUnderLine(OverUnderLine previousVersion, int overReward, int underReward, string who, DateTime now) : base(previousVersion, who, now)
		{
			ValidateReward(overReward, underReward);

			this.overReward = overReward;
			this.underReward = underReward;
			this.score = previousVersion.score;
			this.overCoefficientReward = CalculateRewardCoefficient(OverUnderAnswer.OVER);
			this.underCoefficientReward = CalculateRewardCoefficient(OverUnderAnswer.UNDER);

			if (!base.IsDraft && !base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}

			LogRewards(previousVersion.OverReward, previousVersion.UnderReward);
		}

		internal OverUnderLine NewVersion(int overReward, int underReward, string who, DateTime now)
		{
			var result = new OverUnderLine(this, overReward, underReward, who, now);
			return result;
		}

		private double CalculateRewardCoefficient(OverUnderAnswer answer)
		{
			double result = 0;
			if (answer == OverUnderAnswer.OVER)
			{
				if (this.overReward < 0)
				{
					result = 100 / (double)Math.Abs(this.overReward);
				}
				else
				{
					result = Math.Abs(this.overReward) / 100.0;
				}
			}
			else if (answer == OverUnderAnswer.UNDER)
			{
				if (this.underReward < 0)
				{
					result = 100 / (double)Math.Abs(this.underReward);
				}
				else
				{
					result = Math.Abs(this.underReward) / 100.0;
				}
			}
			else
			{
				throw new GameEngineException("Wager does not have a valid chosen answer");
			}

			if (result == 0) throw new GameEngineException($"Prize reward for a {nameof(OverUnderLine)} can not be zero.");

			return result;
		}

		protected override double CoefficientReward(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			if (answer == OverUnderAnswer.OVER)
			{
				return this.overCoefficientReward;
			}
			else if (answer == OverUnderAnswer.UNDER)
			{
				return this.underCoefficientReward;
			}

			throw new GameEngineException("Wager does not have a valid chosen answer");
		}

		internal override string RewardAsString(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			int result;
			if (answer == OverUnderAnswer.OVER)
			{
				result = this.overReward;
			}
			else if (answer == OverUnderAnswer.UNDER)
			{
				result = this.underReward;
			}
			else
			{
				throw new GameEngineException("Wager does not have a valid chosen answer");
			}

			return (result > 0 ? "+" : "-") + result;
		}

		internal bool AnswerIsOver()
		{
			return realAnswer == OverUnderAnswer.OVER;
		}

		internal WagerAnswer GetAnswer(bool answer)
		{
			OverUnderAnswer result = answer ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
			return result;
		}

		internal void SetRealAnswerAsOver()
		{
			SetRealAnswer(OverUnderAnswer.OVER);
		}
		
		internal void SetRealAnswerAsUnder()
		{
			SetRealAnswer(OverUnderAnswer.UNDER);
		}

		internal void SetRealAnswerAsNoAction()
		{
			SetRealAnswer(OverUnderAnswer.NO_ACTION);
		}

		private void SetRealAnswer(OverUnderAnswer answer)
		{
			if (!this.IsLastVersion()) throw new GameEngineException("Answer should be set only for the original line");

			var currVersion = this;
			var exit = false;
			while (!exit)
			{
				currVersion.realAnswer = answer;
				exit = currVersion.IsOriginalVersion();
				if (!exit) currVersion = (OverUnderLine)currVersion.PreviousVersion;
			}
		}

		internal override decimal Grade(Wager wager)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (this.realAnswer == null) throw new GameEngineException("Real answer of this line has not been set");

			decimal prize = 0;
			if (realAnswer == OverUnderAnswer.NO_ACTION)
			{
				wager.ChangeToNoAction();
			}
			else
			{
				var answer = (OverUnderAnswer)wager.ChosenAnswer;
				bool won = answer == realAnswer;

				if (won)
				{
					prize = wager.ToWin();
					wager.GradeAsWinner();
				}
				else
				{
					wager.GradeAsLoser();
				}
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal override string LineTypeAsString => LineType.OVER_UNDER_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			//TODO incluir el noAction
			if (!this.IsGraded() /*&& !this.IsNoAction()*/) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.OVER_UNDER_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(this.score);
			message.AddProperty(this.overReward);
			message.AddProperty(this.underReward);
			message.AddProperty(base.Text);
			message.AddProperty(this.realAnswer == OverUnderAnswer.OVER ? 'O' : 'U');
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		internal static new void ValidateReward(int overReward, int underReward)
		{
			if (overReward == 0 || underReward == 0) throw new GameEngineException($"Both rewards must be greater than zero {overReward}/{underReward}");
			if (Math.Abs(overReward) <= 100) throw new GameEngineException($"Over reward {overReward} must be greater than 100");
			if (Math.Abs(underReward) <= 100) throw new GameEngineException($"Under reward {underReward} must be greater than 100");
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedOverUnderLineEvent(timestamp, this);
			return result;
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedOverUnderLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		private void LogRewards()
		{
			Log.AppendAtTheEndOfTheLastEntry($"Rewards: over : {overReward}, under : {underReward}");
		}

		private void LogRewards(int previousOverReward, int previousUnderReward)
		{
			var log = OriginalVersion.Log;
			log.AppendAtTheEndOfTheLastEntry($"Previous rewards: over : {previousOverReward}, under : {previousUnderReward}. New rewards: over : {overReward}, under : {underReward}");
		}

		internal double Score
		{
			get
			{
				return this.score;
			}
		}

		internal int OverReward
		{
			get
			{
				return this.overReward;
			}
		}

		internal int UnderReward
		{
			get
			{
				return this.underReward;
			}
		}

		protected internal OverUnderAnswer RealAnswer
		{
			get
			{
				return this.realAnswer;
			}
		}
	}
}
