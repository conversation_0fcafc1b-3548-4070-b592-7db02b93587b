﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal struct Multiplier
    {
        private readonly bool isEnable;

        internal bool Enabled { get { return isEnable;  } }

        internal Multiplier(bool isEnable)
        {
            this.isEnable = isEnable;
        }

        public static bool operator ==(Multiplier obj1, Multiplier obj2)
        {
            return obj1.isEnable == obj2.isEnable;
        }

        public static bool operator !=(Multiplier obj1, Multiplier obj2)
        {
            return obj1.isEnable != obj2.isEnable;
        }

        public override string ToString()
        {
            return $"{ToInt()}";
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        internal int ToInt()
        {
            return (isEnable) ? 1 : 0;
        }
    }
}
