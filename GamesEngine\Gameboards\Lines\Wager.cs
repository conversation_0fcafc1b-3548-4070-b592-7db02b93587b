﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Games;
using GamesEngine.Games.Lines;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Gameboards.Lines
{
	internal class Wager : Gameboard, ISinglePool
	{
		private readonly Line line;
		private readonly WagerAnswer answer;
		private readonly decimal risk;
		private readonly int authorizationId;
		private const int DECIMAL_PLACES = 2;
		private double coefficientReward = double.MinValue;
		private decimal payout = 0;

		internal Wager(Line line, int authorizationId, Player player, WagerAnswer answer, decimal risk, DateTime creationDate) : base(player, line.Company.Gameboards, creationDate)
		{
			if (line == null) throw new ArgumentNullException(nameof(line));
			if (authorizationId <= 0) throw new GameEngineException($"Invalid authorization id {authorizationId}");
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (answer == null) throw new ArgumentNullException(nameof(answer));
			if (!IsValidMoney(risk)) throw new GameEngineException($"Amount {risk} is not a valid risk");

			this.line = line;
			this.authorizationId = authorizationId;
			this.answer = answer;
			this.risk = risk;
		}

		public override decimal Grade()
		{
			if (! base.IsUnprized()) throw new GameEngineException("Wager was already graded therefore it can not grade again");
			if (base.IsGraded()) throw new GameEngineException("Wager was already graded therefore it can not grade again");

			var result = line.Grade(this);
			this.payout = result;
			return result;
		}

		private bool IsValidMoney(decimal risk)
		{
			const double DECIMAL_PLACES_DOUBLE = DECIMAL_PLACES;
			decimal temp = risk * Convert.ToDecimal(Math.Pow(10.0, DECIMAL_PLACES_DOUBLE));
			bool result = temp - Math.Truncate(temp) == 0;
			return result;
		}

		public override BigInteger GradeToUntie()
		{
			throw new NotImplementedException();
		}

		internal void CalculateRisk(RiskAssestment toWin)
		{
			this.line.CalculateRisk(this, toWin);
		}

		internal DateTime nowChangeMeToBeOnWager;

		internal void NotifyRiskTag(DateTime now)
		{
			this.line.Company.Book.RiskTags.RecordActivity(this, now);
		}

		public class NotifyTagMessage : LinesInfoMessage
		{
			private int gameboardId;
			private string accountNumber;
			private DateTime now;

			public NotifyTagMessage(string message) : base(message)
			{

			}

			public NotifyTagMessage(string acountNumber, int gameboardId, DateTime now)
			{
				this.accountNumber = acountNumber;
				this.gameboardId = gameboardId;
				this.now = now;
			}

			protected override void Deserialize(string[] message, out int fieldOrder)
			{
				base.Deserialize(message, out fieldOrder);
				this.accountNumber = message[fieldOrder++];
				this.gameboardId = int.Parse(message[fieldOrder++]);
				this.now = new DateTime(
					int.Parse(message[fieldOrder++]),
					int.Parse(message[fieldOrder++]),
					int.Parse(message[fieldOrder++]),
					int.Parse(message[fieldOrder++]),
					int.Parse(message[fieldOrder++]),
					int.Parse(message[fieldOrder++])
				);
			}

            protected override void InternalSerialize()
            {
				base.InternalSerialize();
				AddProperty(this.accountNumber).
				AddProperty(this.gameboardId).
				AddProperty(this.now);
			}

            internal int GameboardId
			{
				get
				{
					return this.gameboardId;
				}
			}

			internal string AccountNumber
			{
				get
				{
					return this.accountNumber;
				}
			}

			internal DateTime Now
			{
				get
				{
					return this.now;
				}
			}
		}

		internal decimal ToWin()
		{
			if (this.coefficientReward <= 0) throw new GameEngineException($"Coefficient reward {this.coefficientReward} must be greater than zero");

			decimal result = Wager.ToWin(this.risk, this.coefficientReward);
			return result;
		}

		internal static decimal ToWin(decimal risk, double coefficientReward)
		{
			decimal result = Math.Round(risk * Convert.ToDecimal(coefficientReward), DECIMAL_PLACES);
			return result;
		}

		internal FragmentPaymentMessage GenerateNoActionFragmentMessage(DateTime now)
		{
			const int WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT = 1;
			return new FragmentPaymentMessage(
				this.Order.Coin,
				this.AccountNumber,
				this.AuthorizationId,
				WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT,
				WagerStatus.X,
				now,
				this.ToWin(),
				-this.ToWin(),
				(int)Order.Customer.Player.Agent,
				this.Order.Domain.Url
			);
		}

		internal FragmentPaymentMessage GenerateLoserFragmentMessage(DateTime now)
		{
			const int WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT = 1;
			return new FragmentPaymentMessage(
				this.Order.Coin,
				this.AccountNumber,
				this.AuthorizationId,
				WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT,
				WagerStatus.L,
				now,
				this.ToWin(),
				-this.ToWin(),
				(int)Order.Customer.Player.Agent,
                this.Order.Domain.Url
            );
		}

		internal FragmentPaymentMessage GenerateWinnerFragmentMessage(DateTime now)
		{
			const int WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT = 1;
			return new FragmentPaymentMessage(
				this.Order.Coin,
				this.AccountNumber,
				this.AuthorizationId,
				WAGER_GAMEBOARD_ONLY_HAS_ONE_FRAGMENT,
				WagerStatus.W,
				now,
				this.ToWin(),
				-this.ToWin(),
				(int)Order.Customer.Player.Agent,
                this.Order.Domain.Url
            );
		}

		internal WagerAnswer ChosenAnswer
		{
			get
			{
				return this.answer;
			}
		}

		internal decimal Risk
		{
			get
			{
				return this.risk;
			}
		}

		internal Showcase Showcase
		{
			get
			{
				return this.line.Showcase;
			}
		}

		internal Line Line
		{
			get
			{
				return this.line;
			}
		}

		internal double CoefficientReward
		{
			set
			{
				if (value <= 0) throw new GameEngineException($"Coefficient reward {value} must be greater than zero");
				if (! base.IsUnprized() && value != coefficientReward) throw new GameEngineException("Wager was already graded therefore coefficient can not be changed"); 

				this.coefficientReward = value;
			}
		}

		internal Betboard Betboard
		{
			get
			{
				return this.line.Showcase.Matchday.Betboard;
			}
		}

		public int AuthorizationId => this.authorizationId;

		public Bet Bet => this.Player.FindBet(this);
	}
}
