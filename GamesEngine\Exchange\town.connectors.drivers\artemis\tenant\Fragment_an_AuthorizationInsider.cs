﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.artemis;
using GamesEngine.Business;
using GamesEngine.Settings;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using town.connectors.drivers.fiero;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange.town.connectors.drivers.artemis.tenant
{
    public abstract class Fragment_an_AuthorizationInsider : Fragment_an_Authorization, IDriverUserProperties
    {
        public string CashierUrl { get; private set; }
        public const int FAKE_TICKET_NUMBER = -1;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public Fragment_an_AuthorizationInsider(string currencyCode) : base(currencyCode)
        {
        }

        public override void Prepare(DateTime now)
        {
            base.Prepare(now);
            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            // ARTEMIS REQUEST
            var artemisResponse = await base.ExecuteAsync<InsertWagersResponse>(now, recordSet);
            if (artemisResponse.Code != AuthorizationResponseCode.OK)
            {
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = artemisResponse.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(failResponse, typeof(T));
            }

            bool validTickets = artemisResponse.tickets != null && artemisResponse.tickets.Any(t => t.ticketId != null);
            if (!validTickets)
            {
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = "No ticket id were provide from artemis." + artemisResponse.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(FAIL_DEFAULT_RESPONSE, typeof(T));
            }

            //CASHIER REQUEST
            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = Authorization.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Authorization.CashierToken.access_token, now);

            bool needToChangeToken = (Authorization.CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) Authorization.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);
            var result = await AuthorizationAsync(now, recordSet, artemisResponse.tickets);
            if (result == null) throw new Exception($"Result is null. Driver {nameof(Fragment_an_AuthorizationInsider)} failed to execute.");
            if (result.AuthorizationId <= 0)
            {
                GradeFreeFormWagerCollectionChunked(artemisResponse.tickets);
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = result.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(failResponse, typeof(T));
            }

            return (T)Convert.ChangeType(artemisResponse, typeof(T));
        }

        private async Task<AuthorizationTransaction> AuthorizationAsync(DateTime now, CustomSettings.RecordSet recordSet, List<Finance.ToWinByDrawAndNumber> toWinByDrawAndNumbers)
        {
            var AtAddress = recordSet.Mappings["accountNumber"];
            var PurchaseTotalValue = recordSet.Mappings["amount"];
            var PurchaseTotalCurrencyCode = recordSet.Mappings["currencyCode"];
            var StoreId = recordSet.Mappings["storeId"];
            var Concept = recordSet.Mappings["description"];
            var fragmentInformation = recordSet.Mappings["fragmentInformation"];
            var ReferenceNumber = recordSet.Mappings["referenceNumber"];
            var Useless = recordSet.Mappings["useless"];

            string accountNumber = string.Empty;
            string atAddress = AtAddress.AsString;
            decimal purchaseTotal = PurchaseTotalValue.AsDecimal;

            var lockBalanceData = new Finance.ExternalMultiLockBalanceData();
            lockBalanceData.AtAddress = atAddress;
            lockBalanceData.UseLocalAuthorization = false;
            lockBalanceData.AccountNumber = accountNumber;
            lockBalanceData.PurchaseTotal = purchaseTotal;
            lockBalanceData.CurrencyCode = PurchaseTotalCurrencyCode.AsString;
            lockBalanceData.StoreId = StoreId.AsInt;
            lockBalanceData.Concept = Concept.AsString;
            if (recordSet.ContainsKeyName("fragmentInformation"))
                lockBalanceData.FragmentInformation = fragmentInformation.As<FragmentInformation>();
            if (recordSet.ContainsKeyName("referenceNumber"))
                lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            lockBalanceData.ToWinsByDrawAndNumber = toWinByDrawAndNumbers;

            AuthorizationTransaction auth = null;
            try
            {
                var url = $"{CashierUrl}api/customers/{atAddress}/balance/externalMultiLock";
                HttpClient client = new HttpClient();

                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured()) client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Authorization.CashierToken.access_token}");

                var jsonString = JsonConvert.SerializeObject(lockBalanceData);
                var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");
                HttpResponseMessage resultFromCashier = await client.PostAsync(url, httpContent);

                int authorizationId = FAKE_TICKET_NUMBER;
                string responseContent = await resultFromCashier.Content.ReadAsStringAsync();
                if (!resultFromCashier.IsSuccessStatusCode)
                {
                    switch (resultFromCashier.StatusCode)
                    {
                        case System.Net.HttpStatusCode.BadRequest:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail, url, responseContent);
                            break;
                        case System.Net.HttpStatusCode.UnprocessableContent:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.InsufficientFunds, url, responseContent);
                            break;
                        default:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.UnexpectedFormat, url, responseContent);
                            break;
                    }
                }
                else
                {
                    if (!int.TryParse(responseContent, out authorizationId)) throw new Exception($"Failed to parse authorizationId from response: {responseContent} for atAddress: {atAddress}");
                    auth = new AuthorizationTransaction(authorizationId, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.OK, url, responseContent);
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }

            return auth;
        }


        private RestClient _postUpdateWagersClient;

        void GradeFreeFormWagerCollectionChunked(List<Finance.ToWinByDrawAndNumber> wagers)
        {
            string url = "/GradingBet";
            if (_postUpdateWagersClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postUpdateWagersClient = new RestClient(ServicesUrl);
            }

            var gradeFreeFormWagers = new WagersUpdateBody();
            //gradeFreeFormWagers.AddWagers(wagers);
            foreach (var wager in wagers)
            {
                gradeFreeFormWagers.tickets.Add(new Finance.ToWinByDrawAndNumber()
                {
                    status = $"{WagerStatus.X}",
                    ticketId = wager.ticketId,
                    toWin = 0m
                });
            }

            var jsonString = Commons.ToJson(gradeFreeFormWagers);
            string responseString = string.Empty;
            int retryNumber = 0;
            const int MAX_RETRIES_WAGER_COLLECTION = 5;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Post);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        if (responseString.Contains("Lotto wager not found.")) break;
                        retryNumber++;
                        Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                        if (retryNumber == MAX_RETRIES_WAGER_COLLECTION) break;
                    }
                    else
                    {
                        //if (!string.IsNullOrWhiteSpace(responseString))
                        break;
                    }
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                    if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
                    {
                        InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
                    }
                }
            }

        }
    }
}
