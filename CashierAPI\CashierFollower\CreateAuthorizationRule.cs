﻿using GamesEngine.Preferences.Lotto;
using GamesEngine.PurchaseOrders;
using MySql.Data.MySqlClient;
using Puppeteer;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace CashierAPI.CashierFollower
{
    internal struct MovementDetailsInfo
    {
        internal int dairyIdForAuthorization { get; }
        internal string authorizationNumber { get; }
        internal List<int> dairyIdForCreateFragments { get; }
        internal List<int> dairyIdForFragmentPayment { get; }
        internal List<int> dairyIdForChangeFragmentsStatus { get; }

        List<bool> fragmentsSizeForAuthorization;

        internal bool canSkipThisAuthorization;

        internal DateTime maxAuthorizationDate;

        internal string currencyCode { get; }


        internal MovementDetailsInfo(int dairyIdForAuthorization, string authorizationNumber, string currencyCode)
        {
            this.dairyIdForAuthorization = dairyIdForAuthorization;
            this.authorizationNumber = authorizationNumber;
            this.dairyIdForChangeFragmentsStatus = new List<int> { };
            this.dairyIdForFragmentPayment = new List<int> { };
            this.fragmentsSizeForAuthorization = new List<bool> { };
            this.dairyIdForCreateFragments = new List<int> { };
            this.canSkipThisAuthorization = false;
            this.maxAuthorizationDate = new DateTime();
            this.currencyCode = currencyCode;
        }

        internal bool ContainsThisId(int id)
        {
            if (id <= 0) throw new ArgumentNullException(nameof(id));

            if (this.dairyIdForAuthorization == id) return true;

            if (this.dairyIdForCreateFragments != null && this.dairyIdForCreateFragments.Contains(id)) return true;
            if (this.dairyIdForFragmentPayment != null && this.dairyIdForFragmentPayment.Contains(id)) return true;
            if (this.dairyIdForChangeFragmentsStatus != null && this.dairyIdForChangeFragmentsStatus.Contains(id)) return true;

            return false;
        }

        internal DateTime ChangeMaxAuthorizationDate
        {
            set
            {
                var compareDates = DateTime.Compare(this.maxAuthorizationDate, value);

                if (compareDates < 0) this.maxAuthorizationDate = value;
            }

            get
            {
                return this.maxAuthorizationDate;
            }
        }

        internal List<bool> FragmentsForAuthorization
        {
            get
            {
                return fragmentsSizeForAuthorization;

            }
        }

        internal void AddFragmentForAuthorization(int size)
        {
            if (fragmentsSizeForAuthorization.Any()) fragmentsSizeForAuthorization.Clear();
            for (int i = 0; i < size; i++)
            {
                this.fragmentsSizeForAuthorization.Add(false);
            }
        }

        internal void AddDairyIdForCreateFragments(int id)
        {
            this.dairyIdForCreateFragments.Add(id);
        }

        internal void AddDairyIdForFragmentPayment(int id)
        {
            this.dairyIdForFragmentPayment.Add(id);
        }

        internal void AddDairyIdForChangeFragmentsStatus(int id)
        {
            this.dairyIdForChangeFragmentsStatus.Add(id);
        }


        private static HashSet<int> affectedIds = new HashSet<int>();
        private static string currentAffectedAccount = null;

        internal static void SkipDairy(string accountName, List<int> authorizationWithFinalSkip)
        {
            StringBuilder sql = new StringBuilder();

            if (accountName != currentAffectedAccount) 
            {
                currentAffectedAccount = accountName;
                affectedIds.Clear(); 
            }
            


            foreach (int id in authorizationWithFinalSkip)
            {
                if (!affectedIds.Contains(id))
                {
                    sql.Append($"INSERT INTO affectedIds(id) VALUES({id});");

                    affectedIds.Add(id);
                }
            }

            if(sql.Length != 0) CashierFollower.sqlQueue.Add(sql.ToString());
        }

        internal static void SkipDiaryForSpecificDairyId(DatabaseType dbType, int diaryId)
        {
            StringBuilder sql = new StringBuilder();

            sql.Append($"INSERT INTO affectedIds(id) VALUES({diaryId});");

            CashierFollower.sqlQueue.Add(sql.ToString());
        }

        internal static void CreateTableAffectedIds(DatabaseType dbType)
        {
            StringBuilder sql = new StringBuilder();
            if (dbType == DatabaseType.MySQL)
            {
                CashierFollower.sqlQueue.Add("DROP TABLE IF EXISTS affectedIds;");
                CashierFollower.sqlQueue.Add("CREATE TABLE IF NOT EXISTS affectedIds(id INT PRIMARY KEY);");
            }
            else if (dbType == DatabaseType.SQLServer)
            {
                sql.Append("IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.affectedIds') AND type = N'U')");
                sql.Append("BEGIN");
                sql.Append("DROP TABLE affectedIds;");
                sql.Append("END");
                sql.Append("IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'dbo.affectedIds') AND type = N'U')");
                sql.Append("BEGIN");
                sql.Append("CREATE TABLE affectedIds(id INT PRIMARY KEY);");
                sql.Append("END");

                CashierFollower.sqlQueue.Add(sql.ToString());
            }
        }

        internal static void UpdateSkipIds(string accountName, DatabaseType dbType)
        {
            StringBuilder sql = new StringBuilder();
            if (dbType == DatabaseType.MySQL)
            {
                CashierFollower.sqlQueue.Add(@$"
                        START TRANSACTION;

                        UPDATE {accountName} r
                        JOIN affectedIds tr ON r.Id = tr.Id
                        SET r.Skip = 1;

                        COMMIT;

                        DROP TABLE affectedIds;
                    ");
            }
            else if (dbType == DatabaseType.SQLServer)
            {
                CashierFollower.sqlQueue.Add(@$"
                        BEGIN TRANSACTION;

                        UPDATE r
                        SET r.Skip = 1
                        FROM {accountName} r
                        JOIN affectedIds tr ON r.Id = tr.Id;

                        COMMIT;

                        DROP TABLE affectedIds;
                    ");
            }
        }

        private const int MAX_RETRIES = 3;
        internal static void DB_Load_Accounts_LRU_SP(DatabaseType dbType, string connectionString)
        {
            int attempt = 0;
            bool success = false;

            if (dbType == DatabaseType.MySQL)
            {
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    while (attempt < MAX_RETRIES && !success)
                    {

                        try
                        {
                            connection.Open();
                            using (MySqlCommand command = new MySqlCommand("Load_Accounts_LRU_SP", connection))
                            {
                                command.CommandTimeout = 1800;
                                command.CommandType = CommandType.StoredProcedure;
                                command.ExecuteNonQuery();
                            }
                            success = true;
                        }
                        catch (MySqlException ex)
                        {
                            attempt++;
                            if (attempt >= MAX_RETRIES)
                            {
                                throw;
                            }
                            System.Threading.Thread.Sleep(2000);
                        }
                        catch (Exception ex)
                        {
                            throw;
                        }
                        finally
                        {
                            if (connection != null && connection.State == ConnectionState.Open)
                            {
                                connection.Close();
                            }
                        }
                    }
                }
            }
            else if (dbType == DatabaseType.SQLServer)
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    while (attempt < MAX_RETRIES && !success)
                    {

                        try
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand("Load_Accounts_LRU_SP", connection))
                            {
                                command.CommandTimeout = 1800;
                                command.CommandType = CommandType.StoredProcedure;
                                command.ExecuteNonQuery();
                            }
                            success = true;
                        }
                        catch (SqlException ex)
                        {
                            attempt++;
                            if (attempt >= MAX_RETRIES)
                            {
                                throw;
                            }
                            System.Threading.Thread.Sleep(2000);
                        }
                        catch (Exception ex)
                        {
                            throw;
                        }
                        finally
                        {
                            if (connection != null && connection.State == ConnectionState.Open)
                            {
                                connection.Close();
                            }
                        }
                    }
                }
            }
        }
    }

    public class CreateAuthorizationRule : RuleWithoutActor
    {
        protected const string START_TEXT_LINE = "atAddress.CreateAuthorization(";
        protected const string END_TEXT_LINE = ");";
        protected const string FREEPLAY_CURRENCY = "FreePlay(";
        protected const string USD_CURRENCY = "Dollar(";
        protected const string NEW_AUTHORIZATION = "Currency(";
        protected const string START_TEXT_LINEAuthorizationsNumbers = "auths = AuthorizationsNumbers(";
        protected const string START_TEXT_LINEAuthorizationsNumbersAdd = "auths.Add(";
        protected const string TEXT_SetInitialBalance = "balance.SetInitialBalance(";

        public override void Then(Script script)
        {
            var authorizationNumbers = AuthorizationNumbers(script.Text);
            bool isAuthorizationWithFP = AuthorizationWithFP(script.Text);
            var currencyCode = CurrencyCode(isAuthorizationWithFP);
            if (script.DairyId == InitialBalanceHandler.GetLastId(currencyCode) && script.Text.IndexOf(TEXT_SetInitialBalance) != -1)
            {
                InitialBalanceHandler.SetLastAuthorizationNumbers(currencyCode, authorizationNumbers);
                if (InitialBalanceHandler.ExistsBalance(currencyCode)) DBHelperCashierFollower.ProceedToMarkSkipsForSpecificDairyId(InitialBalanceHandler.GetCurrentId(currencyCode));
            }
            if (authorizationNumbers!=null)
            {
                DBHelperCashierFollower.AddNewAuthorization(authorizationNumbers, script.DairyId, currencyCode);
            }

        }

        public override void When(Script script)
        {
            if (script.Text.IndexOf(START_TEXT_LINE) != -1 && script.Text.IndexOf(NEW_AUTHORIZATION) == -1)
            {
                Then(script);
            }

        }

        public string[] AuthorizationNumbers(string script)
        {
            if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbers) != -1)
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbers, END_TEXT_LINE);
                var body = UtilStringRules.GetBodyArgText(script).Trim();

                string[] arguments = body.Split(',');
                int authorization = int.Parse(arguments[0]);
                var consecutive = arguments.Length > 1 ? int.Parse(arguments[1]) : 1;
                var authorizationNumbers = arguments.Length > 1 ? new AuthorizationsNumbers(authorization, consecutive) : new AuthorizationsNumbers(authorization);

                if (script.IndexOf(START_TEXT_LINEAuthorizationsNumbersAdd) != -1)
                {
                    UtilStringRules.SetHeaderText(START_TEXT_LINEAuthorizationsNumbersAdd, END_TEXT_LINE);
                    body = UtilStringRules.GetBodyArgText(script).Trim();
                    arguments = body.Split(").Add(");
                    foreach (var arg in arguments)
                    {
                        if (int.TryParse(arg, out authorization))
                        {
                            authorizationNumbers.Add(authorization);
                        }
                        else
                        {
                            var addArgs = arg.Split(',');
                            authorization = int.Parse(addArgs[0]);
                            consecutive = addArgs.Length > 1 ? int.Parse(addArgs[1]) : 1;
                            authorizationNumbers.Add(authorization, consecutive);
                        }
                    }
                }
                return authorizationNumbers.Numbers.Select(n => n.ToString()).ToArray();
            }
            else
            {
                UtilStringRules.SetHeaderText(START_TEXT_LINE, END_TEXT_LINE);
                var body = UtilStringRules.GetBodyArgText(script);
                var arguments = UtilStringRules.GetArguments(body);
                var authorizationsJoinedByComma = arguments[3].Trim();

                if (authorizationsJoinedByComma.StartsWith("{") && authorizationsJoinedByComma.EndsWith("}"))
                {
                    var authorizations = authorizationsJoinedByComma.TrimStart('{').TrimEnd('}').Split(',');
                    bool isValidAuthorization = true;
                    foreach (var authorizationNumber in authorizations)
                    {
                        if (!int.TryParse(authorizationNumber, out int authorizationNumberInt))
                        {
                            isValidAuthorization = false;
                            break;
                        }
                    }
                    if (!isValidAuthorization)
                    {
                        authorizationsJoinedByComma = arguments[4].Trim();
                        authorizations = authorizationsJoinedByComma.TrimStart('{').TrimEnd('}').Split(',');
                    }

                    return authorizations;
                }
                else
                {
                    return new string[] { authorizationsJoinedByComma };
                }
            }
        }

        public bool AuthorizationWithFP(string script)
        {
            UtilStringRules.SetHeaderText(START_TEXT_LINE, END_TEXT_LINE);
            string[] argumentos = UtilStringRules.GetBodyArgText(script).Split(", ");

            string authorizationCurrency = argumentos[2].Trim();

            if (authorizationCurrency.IndexOf(FREEPLAY_CURRENCY) != -1) return true;
            if (authorizationCurrency.IndexOf(USD_CURRENCY) != -1) return false;

            throw new Exception("Moneda de la autorizacion desconocida");
        }

        private string CurrencyCode(bool isFP)
        {
            return isFP ? "FP" : "USD";
        }

        public override void FinalizeRule()
        {
            return;
        }
    }
}
