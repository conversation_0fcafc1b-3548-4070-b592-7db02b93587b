﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal struct BulleyeDraw
    {
        private readonly int number;

        internal BulleyeDraw(int number)
        {
            this.number = number;
        }

        public static bool operator ==(BulleyeDraw obj1, BulleyeDraw obj2)
        {
            return obj1.number == obj2.number;
        }

        public static bool operator !=(BulleyeDraw obj1, BulleyeDraw obj2)
        {
            return obj1.number != obj2.number;
        }

        public override string ToString()
        {
            return $"0{number}";
        }

        public string AsStringForAccounting()
        {
            return ToString();
        }

        internal int ToInt()
        {
            return number;
        }
    }
}
