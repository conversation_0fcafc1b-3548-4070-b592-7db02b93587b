﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using static GamesEngine.Business.Liquidity.Containers.Tanker;

namespace GamesEngine.Business.Liquidity
{
    internal class Source : Objeto
    {
        private readonly List<Xpub> _xpubs = new List<Xpub>();
        
        private readonly Dictionary<int, Tanker> _tankers = new Dictionary<int, Tanker>();
        private readonly Dictionary<int, Tank> _tanks = new Dictionary<int, Tank>();

        internal Source(Liquid liquid)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));

            Kind = liquid.Kind;
            int vesion = NextJarVersion();
            Jar = new Jar(this, liquid.Kind, vesion);
            CurrentVesion = vesion;
            Liquid = liquid;
        }

        internal string Kind { get; private set; }
        internal decimal Amount => Jar.Amount;
        internal decimal AvailableAmount => Jar.AvailableAmount;
        internal Jar Jar { get; private set; }
        internal Liquid Liquid { get; private set; }
        internal IEnumerable<Tank> Tanks => _tanks.Values;
        internal IEnumerable<Tanker> Tankers => _tankers.Values;

        internal bool ExsitXpub(string xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));
            return _xpubs.Any(s => s.Value == xpub);
        }

        internal void AddXpub(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            _xpubs.Add(xpub);
        }

        internal IEnumerable<Xpub> Xpubs => _xpubs.AsReadOnly();

        private string GenerateNewAddress()
        {
            if (!_xpubs.Any())
                throw new InvalidOperationException("No XPUBs available to derive addresses.");

            // Ejemplo sencillo: usar el primer XPUB disponible para generar una dirección
            return _xpubs.First().GenerateAddress();
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, int depositId, string invoiceId, int authorizationId, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            return CreateDraftDeposit(
                itIsThePresent,
                createdAt,
                depositId,
                invoiceId,
                authorizationId,
                string.Empty, // externalAtAddress
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate,
                confirmedCurrency,
                confirmedAmount,
                totalConfirmations,
                storeId,
                domain
            );
        }

        internal Deposit CreateDraftDeposit(bool itIsThePresent, DateTime createdAt, int depositId, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId, Domain domain)
        {
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId), "Deposit ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (authorizationId <= 0) throw new ArgumentNullException(nameof(authorizationId), "Authorization ID must be greater than zero.");
            if (externalReference <= 0) throw new ArgumentNullException(nameof(externalReference), "External reference must be greater than zero.");
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination), "Destination cannot be null or empty.");
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (exchangeAmount <= 0) throw new ArgumentNullException(nameof(exchangeAmount), "Exchange amount must be greater than zero.");            
            if (exchangeRate <= 0) throw new ArgumentNullException(nameof(exchangeRate), "Rate must be greater than zero.");
            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new ArgumentNullException(nameof(confirmedCurrency), "Confirmed currency cannot be null or empty.");
            if (confirmedAmount <= 0) throw new ArgumentNullException(nameof(confirmedAmount), "Confirmed amount must be greater than zero.");
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations), "Total confirmations cannot be negative.");
            if (storeId <= 0) throw new ArgumentNullException(nameof(storeId), "Store ID must be greater than zero.");
            if (domain == null) throw new ArgumentNullException(nameof(domain), "Domain cannot be null.");

            var address = GenerateNewAddress();
            var draftConfirmDeposit = Jar.CreateDraftDeposit(
                itIsThePresent, 
                createdAt, 
                address,
                depositId,
                invoiceId,
                authorizationId,
                externalAtAddress,
                externalReference,
                destination,
                exchangeAmount,
                exchangeRate, 
                confirmedCurrency, 
                confirmedAmount,
                totalConfirmations,
                storeId,
                domain
            );
            return draftConfirmDeposit;
        }

        internal Deposit ConfirmDeposit(bool itIsThePresent, DateTime createdAt, Deposit draftConfirmDeposit)
        {
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit));
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt), "Date cannot be empty.");

            var confirmedDeposit = Jar.ConfirmDeposit(itIsThePresent, createdAt, draftConfirmDeposit);            
            return confirmedDeposit;
        }

        internal Deposit CancelDeposit(bool itIsThePresent, DateTime createdAt, Deposit draftConfirmDeposit)
        {
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit));
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt), "Date cannot be empty.");
            var canceledDeposit = Jar.CancelDeposit(itIsThePresent, createdAt, draftConfirmDeposit);
            return canceledDeposit;
        }

        private int tankConsecutive = 0;
        internal int NextTankId()
        {
            return tankConsecutive + 1;
        }

        private int tankerConsecutive = 0;
        internal int NextTankerId()
        {
            return tankerConsecutive + 1;
        }


        internal int CurrentVesion { get; private set; }
        internal int NextJarVersion()
        {
            return CurrentVesion + 1;
        }

        internal int DepositConsecutive { get; set; }
        internal int NextDepositId()
        {
            return DepositConsecutive + 1;
        }

        internal void DelegateJar(bool itIsThePresent, DateTime now, int vesion, LegacyJar previousLegacyJar, IEnumerable<Deposit> delegatedDeposits)
        {
            if (vesion <= 0) throw new ArgumentOutOfRangeException(nameof(vesion), "Version must be greater than zero.");
            if (previousLegacyJar == null) throw new ArgumentNullException(nameof(previousLegacyJar));
            if (delegatedDeposits == null) throw new ArgumentException("Delegated deposits cannot be null.", nameof(delegatedDeposits));

            if (vesion <= CurrentVesion) throw new InvalidOperationException($"Version {vesion} is not greater than the current version {CurrentVesion}.");

            var newJar = new Jar(this, Jar.Kind, vesion, previousLegacyJar, delegatedDeposits);
            Jar = newJar;
            CurrentVesion = vesion;

            decimal totalDelegatedAmount = delegatedDeposits.Sum(d => d.Amount);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    string description = $"Jar for {Kind} V{CurrentVesion}";
                    CreatedJarMessage jarMessage = new CreatedJarMessage(CurrentVesion, previousLegacyJar.Version, Jar.Kind, description, now);
                    buffer.Send(jarMessage);
                }
            }
        }

        internal bool ExistTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentOutOfRangeException(nameof(tankId), "Tank ID must be greater than zero.");

            return _tanks.ContainsKey(tankId);
        }

        internal void AddOrUpdateTank(Tank result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (_tanks.TryGetValue(result.Id, out Tank foundTank))
            {
                _tanks[result.Id] = result;
            }
            else
            {
                _tanks.Add(result.Id, result);
                tankConsecutive = result.Id;
            }
        }

        internal Tank FindTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));

            if (_tanks.TryGetValue(tankId, out Tank foundTank))
            {
                return foundTank;
            }
            throw new GameEngineException($"Tank with id: {tankId} was not found in source kind {Kind}");
        }


        internal TankerPending CreateTanker(bool itIsThePresent, DateTime createdAt, int tankerId, string name, string description, IEnumerable<int> tankIds)
        {
            if (createdAt == DateTime.MinValue) throw new ArgumentNullException(nameof(createdAt), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (tankIds == null || !tankIds.Any()) throw new ArgumentException("Tank IDs cannot be null or empty.", nameof(tankIds));

            if (tankIds.Any(tankId => !_tanks.ContainsKey(tankId))) throw new GameEngineException($"Some tanks with ids: {string.Join(", ", tankIds)} were not found in source kind {Kind}");

            List<TankReady> tankReadys = new List<TankReady>();

            foreach (var tankId in tankIds)
            {
                if (_tanks.TryGetValue(tankId, out Tank foundTank))
                {
                    if (foundTank is TankReady tankReady)
                    {
                        tankReadys.Add(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"Tank with id: {tankId} is not ready.");
                    }
                }
            }

            var tanker = new TankerPending(tankerId, name, description, createdAt, Kind, this, tankReadys);
            AddOrUpdateTanker(tanker);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankerMessage createdTankMessage = new CreatedTankerMessage(
                        tankerId: tanker.Id,
                        kind: Kind,
                        version:1,
                        description: string.IsNullOrEmpty(tanker.Description) ? $"Created Tanker {tanker.Name} - Jar version" : tanker.Description,
                        createdAt: createdAt
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankerEvent createdTankEvent = new CreatedTankerEvent(createdAt, tankerId, tanker.Name, tanker.Description);
                PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
            }
            return tanker;
        }

        internal void AddOrUpdateTanker(Tanker result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (_tankers.TryGetValue(result.Id, out Tanker foundTank))
            {
                _tankers[result.Id] = result;
            }
            else { 
                
                _tankers.Add(result.Id, result);
                tankerConsecutive = result.Id;
            }
        }

        internal bool ExistTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");

            return _tankers.ContainsKey(tankerId);
        }

        internal Tanker FindTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));

            if (_tankers.TryGetValue(tankerId, out Tanker foundTanker))
            {
                return foundTanker;
            }
            throw new GameEngineException($"Tanker with id: {tankerId} was not found in source kind {Kind}");
        }
    }

}
