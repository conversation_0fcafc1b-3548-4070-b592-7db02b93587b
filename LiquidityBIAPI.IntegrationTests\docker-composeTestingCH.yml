name: clickhouse-tests

services:
  clickhouse-server:
    image: clickhouse/clickhouse-server:latest
    container_name: clickhouse_for_tests
    ports:
      # Expose the HTTP port (for most clients) and the native TCP port
      - "8124:8123" # HTTP
      - "9001:9000" # Native TCP
    ulimits:
      nproc: 65535
      nofile:
        soft: 262144
        hard: 262144
    # This environment variable is for the container itself, not your tests.
    # It disables the default user password prompt on first run.
    environment:
      - CLICKHOUSE_USER=default
      - CLICKHOUSE_PASSWORD=admin123
      - CLICKHOUSE_DB=default

    # The healthcheck is crucial. It ensures that your tests don't start
    # running until the ClickHouse server is actually ready to accept connections.
    healthcheck:
      test: ["CMD", "clickhouse-client", "--query", "SELECT 1"]
      interval: 5s
      timeout: 5s
      retries: 10