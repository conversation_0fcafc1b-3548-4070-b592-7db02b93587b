﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.drivers.Result;

namespace GamesEngine.Custodian.Operations
{
	[Puppet]
	internal class DisbursementsChanges : Objeto
	{
		private DateTime date;
		private Guardian guardian;
		internal DisbursementsChanges(DateTime date, Guardian guardian)
		{
			this.date = date;
			this.guardian = guardian;
		}
		internal List<DisbursementChange> DisbursementsToBeAddded { get; } = new List<DisbursementChange>();
		internal List<int> DisbursementsToBeRemoved { get; } = new List<int>();
		internal void Add(bool itIsThePresent, int disbursementId, string accountNumber, DateTime scheduledDateSt, decimal amount)
		{
			AccountNumber account = guardian.Accounts().Search(accountNumber);
			DisbursementChange disbursement = new DisbursementChange(disbursementId, account, scheduledDateSt, amount);
			DisbursementsToBeAddded.Add(disbursement);
		}
		internal void Remove(int disbursementId)
		{
			DisbursementsToBeRemoved.Add(disbursementId);
		}
		internal bool AllIdsAreIncremental()
		{
			int index = 1;
			foreach (Disbursement disbursement in DisbursementsToBeAddded)
			{
				if (disbursement.Id != index) return false;

				index++;
			}

			return true;
		}
		internal bool AreAllDatesAfter(DateTime today)
		{
			foreach (Disbursement disbursement in DisbursementsToBeAddded)
			{
				if (disbursement.ScheduledDate < today.Date) return false;
			}

			return true;
		}
		internal bool AllAmountsWereDistributed(Disbursements disbursements, Currency disbursementAmount)
		{
			Disbursements disbursementsInMemory;
			if (disbursements == null)
			{
				disbursementsInMemory = new Disbursements();
			}
			else
			{
				disbursementsInMemory = disbursements.MakeACopyUnExecuted();
			}

			disbursementsInMemory.Update(this);


			decimal total = 0;
			foreach (Disbursement disbursementInMemory in disbursementsInMemory.List())
			{
				total += disbursementInMemory.Amount;
			}

			if (disbursements != null) total += disbursements.ExecutedAmount;

			return (disbursementAmount.Value == total);
		}
		internal int CountRemoved()
		{
			return DisbursementsToBeRemoved.Count;
		}
		internal int CountAdded()
		{
			return DisbursementsToBeAddded.Count;
		}
		internal List<Disbursement> ListToDisbursementUnpayed(WithDrawal withdrawal)
		{
			List<Disbursement> result = new List<Disbursement>();

			foreach (DisbursementChange disbursement in DisbursementsToBeAddded)
			{
				result.Add(new DisbursementUnpayed(withdrawal, disbursement));
			}

			return result;
		}
		internal void Generate(bool itIsThePresent, string accountNumber, DateTime scheculeddate, int rows, decimal amount)
		{
			for (int i = 0; i < rows; i++)
			{
				int id = i + 1;
				Add(itIsThePresent, id, accountNumber, scheculeddate, amount);
			}
		}
	}
	public enum DisbursmentStatusCode
	{
		UNPAYED = 0, CHANGES = 1, APPROVED = 2
	}
	[Puppet]
	internal class Disbursements : Objeto
	{
		private List<Disbursement> disbursements;
		internal delegate void AfterDisbursementEvent(DateTime previousDate, decimal previousAmount, Disbursement disbursement, AccountNumber account, DateTime scheduledDate, decimal amount);
		internal event AfterDisbursementEvent AfterDisbursementIsUpdated;
		internal delegate void BeforeDisbursementEvent(DateTime newDate, decimal newAmount, Disbursement disbursement, AccountNumber account, DateTime scheduledDate, decimal amount);
		internal event BeforeDisbursementEvent BeforeDisbursementIsUpdated;
		internal delegate void DisbursmentAddedEvent(Disbursement disbursement);
		internal event DisbursmentAddedEvent WhenDisbursmentHaveBeenAdd;
		internal delegate void DisbursmentRemovedEvent(DateTime scheduledDate, Disbursement disbursement);
		internal event DisbursmentRemovedEvent WhenDisbursmentHaveBeenRemoved;
		internal Disbursements()
		{
		}
		internal Disbursements(List<Disbursement> disbursementsToBeAddded)
		{
			disbursements = disbursementsToBeAddded;
		}
		public Disbursements(List<Disbursement> disbursementsToBeAddded,
			Action<DateTime, decimal, Disbursement, AccountNumber, DateTime, decimal> beforeDisbursementUpdateAction,
			Action<DateTime, decimal, Disbursement, AccountNumber, DateTime, decimal> afterDisbursementUpdateAction,
			Action<Disbursement> disbursementAddedAction,
			Action<DateTime, Disbursement> disbursementRemovedAction)
			: this(disbursementsToBeAddded)
		{
			if (beforeDisbursementUpdateAction == null) throw new ArgumentException(nameof(beforeDisbursementUpdateAction));
			if (afterDisbursementUpdateAction == null) throw new ArgumentException(nameof(afterDisbursementUpdateAction));
			if (disbursementAddedAction == null) throw new ArgumentException(nameof(disbursementAddedAction));
			if (disbursementRemovedAction == null) throw new ArgumentException(nameof(disbursementRemovedAction));

			this.BeforeDisbursementIsUpdated += (DateTime newDate, decimal newAmount, Disbursement disbursement, AccountNumber account, DateTime date, decimal amount) =>
			{
				beforeDisbursementUpdateAction(newDate, newAmount, disbursement, account, date, amount);
			};
			this.AfterDisbursementIsUpdated += (DateTime previousDate, decimal previousAmount, Disbursement disbursement, AccountNumber account, DateTime date, decimal amount) =>
			{
				afterDisbursementUpdateAction(previousDate, previousAmount, disbursement, account, date, amount);
			};
			this.WhenDisbursmentHaveBeenAdd += (Disbursement disbursement) =>
			{
				disbursementAddedAction(disbursement);
			};
			this.WhenDisbursmentHaveBeenRemoved += (DateTime scheduledDate, Disbursement disbursement) =>
			{
				disbursementRemovedAction(scheduledDate, disbursement);
			};

			foreach (Disbursement disbursementInMemory in this.disbursements)
			{
				WhenDisbursmentHaveBeenAdd.Invoke(disbursementInMemory);
			}
		}

		private Disbursements(BeforeDisbursementEvent beforeDisbursementEvent,
			AfterDisbursementEvent afterDisbursementEvent,
			DisbursmentAddedEvent whenDisbursmentHaveBeenAdd,
			DisbursmentRemovedEvent whenDisbursmentHaveBeenRemoved)
		{
			this.BeforeDisbursementIsUpdated += beforeDisbursementEvent;
			this.AfterDisbursementIsUpdated += afterDisbursementEvent;
			this.WhenDisbursmentHaveBeenAdd += whenDisbursmentHaveBeenAdd;
			this.WhenDisbursmentHaveBeenRemoved += whenDisbursmentHaveBeenRemoved;
		}

		internal void Add(Disbursement disbursement)
		{
			if (disbursement == null) throw new ArgumentException(nameof(disbursement));
			if (disbursements == null) disbursements = new List<Disbursement>();

			disbursements.Add(disbursement);

			WhenDisbursmentHaveBeenAdd?.Invoke(disbursement);
		}
		internal bool ValidateNoModificationsInTheExecuted(DisbursementsChanges disbursementsChanges)
		{
			foreach (Disbursement disbursementToBeApplyed in disbursementsChanges.DisbursementsToBeAddded)
			{
				Disbursement disbursementInMemory;

				if (TryToGet(disbursementToBeApplyed.Id, out disbursementInMemory)
					&& disbursementInMemory.ItsApproved)
				{
					return true;
				}
			}

			foreach (int disbursementId in disbursementsChanges.DisbursementsToBeRemoved)
			{
				Disbursement disbursementInMemory;

				if (TryToGet(disbursementId, out disbursementInMemory)
					&& disbursementInMemory.ItsApproved)
				{
					return true;
				}
			}

			return false;
		}
		internal int Count()
		{
			if (disbursements == null) return 0;

			return disbursements.Count;
		}
		internal Disbursement SearchById(int id)
		{
			if (disbursements == null) throw new GameEngineException($"There  is no {nameof(Disbursement)} with id {id}");

			foreach (Disbursement disbursement in disbursements)
			{
				if (disbursement.Id == id) return disbursement;
			}
			throw new GameEngineException($"There  is no {nameof(Disbursement)} with id {id}");
		}
		internal decimal ExecutedAmount
		{
			get
			{
				decimal result = 0;
				foreach (Disbursement disbursement in disbursements)
				{
					if (disbursement.ItsApproved) result += disbursement.Amount;
				}
				return result;
			}
		}
		internal bool TryToGet(int id, out Disbursement item)
		{
			item = null;
			if (disbursements == null) return false;

			foreach (Disbursement disbursement in disbursements)
			{
				if (disbursement.Id == id)
				{
					item = disbursement;
					return true;
				}
			}
			return false;
		}
		internal void Update(DisbursementsChanges disbursementsToBeApplyed)
		{
			foreach (int disbursementId in disbursementsToBeApplyed.DisbursementsToBeRemoved)
			{
				Disbursement disbursementInMemory = SearchById(disbursementId);
				Remove(disbursementInMemory);
			}

			foreach (DisbursementChange disbursementToBeApplied in disbursementsToBeApplyed.DisbursementsToBeAddded)
			{
				Disbursement disbursementInMemory;
				bool found = TryToGet(disbursementToBeApplied.Id, out disbursementInMemory);

				if (found)
				{
					DateTime previousDate = disbursementInMemory.ScheduledDate;
					DateTime newDate = disbursementToBeApplied.ScheduledDate;
					decimal previousAmount = disbursementInMemory.Amount;
					decimal newAmnount = disbursementToBeApplied.Amount;

					BeforeDisbursementIsUpdated?.Invoke(newDate, newAmnount, disbursementInMemory, disbursementInMemory.Account, disbursementInMemory.ScheduledDate, -disbursementInMemory.Amount);

					disbursementInMemory.Update(disbursementToBeApplied);

					AfterDisbursementIsUpdated?.Invoke(previousDate, previousAmount, disbursementInMemory, disbursementInMemory.Account, disbursementInMemory.ScheduledDate, disbursementInMemory.Amount);
				}
				else
				{
					Add(disbursementToBeApplied);
				}

			}
		}

		internal void Update(WithDrawal withDrawal, DisbursementsChanges disbursementsToBeApplyed)
		{
			foreach (int disbursementId in disbursementsToBeApplyed.DisbursementsToBeRemoved)
			{
				Disbursement disbursementInMemory = SearchById(disbursementId);
				Remove(disbursementInMemory);
			}

			foreach (DisbursementChange disbursementToBeApplied in disbursementsToBeApplyed.DisbursementsToBeAddded)
			{
				Disbursement disbursementInMemory;
				bool found = TryToGet(disbursementToBeApplied.Id, out disbursementInMemory);

				if (found)
				{
					DateTime previousDate = disbursementInMemory.ScheduledDate;
					decimal previousAmount = disbursementInMemory.Amount;

					BeforeDisbursementIsUpdated?.Invoke(previousDate, previousAmount, disbursementInMemory, disbursementInMemory.Account, disbursementInMemory.ScheduledDate, -disbursementInMemory.Amount);
					disbursementInMemory.Update(disbursementToBeApplied);
					AfterDisbursementIsUpdated?.Invoke(previousDate, previousAmount, disbursementInMemory, disbursementInMemory.Account, disbursementInMemory.ScheduledDate, disbursementInMemory.Amount);
				}
				else
				{
					Add(disbursementToBeApplied.ToUnpayed(withDrawal));
				}

			}
		}

		internal void ReplaceDisbursment(Disbursement disbursementUnpayed, Disbursement disbursementExecuted)
		{
			int index = disbursements.IndexOf(disbursementUnpayed);
			if (index < 0) throw new GameEngineException($"The {nameof(DisbursementUnpayed)} with id {disbursementUnpayed.Id} not belongs to this set.");
			disbursements[index] = disbursementExecuted;
		}

		internal IEnumerable<Disbursement> List()
		{
			if (disbursements == null) return new List<Disbursement>();
			return disbursements;
		}
		internal Disbursements MakeACopyUnExecuted()
		{
			Disbursements result = new Disbursements();
			if (disbursements != null)
			{
				foreach (Disbursement disbursement in disbursements)
				{
					if (!disbursement.ItsApproved) result.Add(disbursement.MakeACopy());
				}
			}
			return result;
		}
		internal static void ValidateChanges(Disbursements disbursements, DateTime today, DisbursementsChanges disbursementsToBeApplied, Currency disbursementAmount)
		{
			bool itsTheFirstApply = disbursements == null;
			bool theFirstTimeAllDisbursementsShouldBeOrdered = itsTheFirstApply && disbursementsToBeApplied.AllIdsAreIncremental();
			bool checkIfAreTryingToModifyAndExecutedDisbursement = !itsTheFirstApply && disbursements.ValidateNoModificationsInTheExecuted(disbursementsToBeApplied);

			if (disbursementsToBeApplied == null) throw new ArgumentNullException(nameof(disbursementsToBeApplied));
			if (!disbursementsToBeApplied.AreAllDatesAfter(today)) throw new GameEngineException($"There are some {nameof(Disbursements)} with a date before {today}.");
			if (itsTheFirstApply && !theFirstTimeAllDisbursementsShouldBeOrdered) throw new GameEngineException($"{nameof(Disbursements)} ids are not in sequential order.");
			if (theFirstTimeAllDisbursementsShouldBeOrdered && disbursementsToBeApplied.CountRemoved() > 0) throw new GameEngineException($"There are no {nameof(Disbursements)} to remove.");
			if (theFirstTimeAllDisbursementsShouldBeOrdered && disbursementsToBeApplied.CountAdded() < 1) throw new GameEngineException($"Should add at leadt one {nameof(Disbursement)}.");
			if (checkIfAreTryingToModifyAndExecutedDisbursement) throw new GameEngineException($"No modifications are allowed for {nameof(Disbursement)} already executed.");
			if (!disbursementsToBeApplied.AllAmountsWereDistributed(disbursements, disbursementAmount)) throw new GameEngineException($"Not all the {disbursementAmount.ToDisplayFormat()}  were scheduled.");
		}
		internal void Remove(Disbursement disbursementInMemory)
		{
			disbursements.Remove(disbursementInMemory);
			WhenDisbursmentHaveBeenRemoved?.Invoke(disbursementInMemory.ScheduledDate, disbursementInMemory);
		}
		internal Disbursement First()
		{
			return disbursements[0];
		}
		internal Disbursements Filter(int inicialIndex, int amountOfRows)
		{
			Disbursements result = new Disbursements();
			if (disbursements != null)
			{
				for (int i = inicialIndex;
				 i < inicialIndex + amountOfRows && i < disbursements.Count;
				i++)
				{
					result.Add(disbursements[i]);
				}
			}
			return result;
		}

		internal Disbursements Filter(string accountNumber, string currency, Operation.StatusCodes transactionStatus)
		{
			bool thereIsAccountNumberCriteria = !string.IsNullOrEmpty(accountNumber);
			bool thereIsCurrencyCriteria = !string.IsNullOrEmpty(currency);
			Coin currencyCode = thereIsCurrencyCriteria ? Coinage.Coin(currency) : Coinage.Coin(Currencies.CODES.USD);

			Disbursements result = new Disbursements();
			if (disbursements != null)
			{
				for (int index = 0; index < disbursements.Count; index++)
				{
					DisbursemenWithTransaction disbursement = (DisbursemenWithTransaction)disbursements[index];

					if (disbursement.TransactionStatus != transactionStatus) continue;

					bool achieveAccountNumberCriteria = !thereIsAccountNumberCriteria
							|| (thereIsAccountNumberCriteria
							&& thereIsAccountNumberCriteria && disbursement.Account.Number == accountNumber);
					bool achieveCurrencyCriteria = !thereIsCurrencyCriteria
							|| (thereIsCurrencyCriteria
							&& disbursement.Account.Coin == currencyCode);

					if (achieveAccountNumberCriteria
						&& achieveCurrencyCriteria)
					{
						result.Add(disbursement);
					}
				}
			}
			return result;
		}

		internal Disbursements Copy(WithDrawal newOwner)
		{
			Disbursements result = new Disbursements(BeforeDisbursementIsUpdated, AfterDisbursementIsUpdated, WhenDisbursmentHaveBeenAdd, WhenDisbursmentHaveBeenRemoved);

			foreach (Disbursement disbursement in disbursements)
			{
				Disbursement newDisbursement = ((DisbursemenWithTransaction)disbursement).Copy(newOwner);
				result.Add(newDisbursement);

				//WhenAccumulatedHaveChanged.Invoke(disbursement, disbursement.Account, disbursement.ScheduledDate, -disbursement.Amount);
				//WhenDisbursmentHaveBeenRemoved.Invoke(disbursement.ScheduledDate, disbursement);
			}

			return result;
		}
	}
	[Puppet]
	internal abstract class Disbursement : Objeto
	{

		internal bool ItsApproved { get;}
		internal int Id { get; }
		internal AccountNumber Account { get; private set; }
		internal DateTime ScheduledDate { get; private set; }
		internal decimal Amount { get; private set; }
		internal DisbursmentStatusCode Status { get;}

		internal Disbursement(DisbursmentStatusCode status, int id, AccountNumber account, DateTime scheculeddate, decimal amount, bool itsExecuted)
		{
			if (id <= 0) throw new GameEngineException($"The {nameof(id)} must be graeter than 0.");
			if (amount <= 0) throw new GameEngineException($"The {nameof(amount)} must be graeter than 0.");
			if (scheculeddate == default(DateTime)) throw new ArgumentNullException(nameof(scheculeddate));

			Id = id;
			Account = account;
			ScheduledDate = scheculeddate;
			Amount = amount;
			ItsApproved = status == DisbursmentStatusCode.APPROVED;
			Status = status;
		}

		internal abstract Disbursement MakeACopy();
		internal virtual void Update(Disbursement disbursementToBeApplied)
		{
			Account = disbursementToBeApplied.Account;
			Amount = disbursementToBeApplied.Amount;
			ScheduledDate = disbursementToBeApplied.ScheduledDate;
		}
	}
	[Puppet]
	internal abstract class DisbursemenWithTransaction : Disbursement
	{
		internal WithDrawal Withdrawal { get; }
		internal int TransacionId { get { return Withdrawal.TransactionId; } }
		internal Coin Coin{ get { return Withdrawal.DisbursementAmount.Coin; } }
		internal string CurrencyCode { get { return Withdrawal.DisbursementAmount.CurrencyCode; } }
		internal string CurrencyCodeAsText { get { return Withdrawal.DisbursementAmount.CurrencyCodeAsText; } }
		internal string FormatedAmount { get { return Currency.Factory(CurrencyCode, Amount).ToDisplayFormat(); } }
		public Operation.StatusCodes TransactionStatus { get { return Withdrawal.Status; } }
		internal DisbursementExecution LastExecution { get; private set; }
		internal DisbursemenWithTransaction(WithDrawal withdrawal, DisbursmentStatusCode status, int id, AccountNumber account, DateTime scheculeddate, decimal amount, bool itsExecuted)
			: base(status, id, account, scheculeddate, amount, itsExecuted)
		{
			if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

			Withdrawal = withdrawal;
		}
		internal abstract Disbursement Copy(WithDrawal newOwner);
		internal void SaveLastExecution(DisbursementExecution disbursementExecution1)
		{
			if (disbursementExecution1 == null) throw new ArgumentNullException(nameof(disbursementExecution1));

			LastExecution = disbursementExecution1;
		}
		internal bool HasExecutions()
		{
			return LastExecution != null;
		}
		internal string Description { get { return $"#{Id} {ScheduledDate.ToString("dddd, dd MMMM yyyy")} {FormatedAmount}"; } }
	}
	internal sealed class DisbursementExecuted : DisbursemenWithTransaction
	{
		internal DisbursementUnpayed DisbursementUnpayed { get; }
		internal DisbursementExecuted(DisbursementUnpayed disbursementUnpayed)
			: base(disbursementUnpayed.Withdrawal,
				  DisbursmentStatusCode.APPROVED,
				  disbursementUnpayed.Id,
				  disbursementUnpayed.Account,
				  disbursementUnpayed.ScheduledDate,
				  disbursementUnpayed.Amount,
				  true)
		{
			DisbursementUnpayed = disbursementUnpayed;
		}

		internal int TransactionId { get { return Withdrawal.TransactionId; } }
		internal string ProcessorId { get { return Withdrawal.Processor.Driver.Id; } }

		internal override Disbursement MakeACopy()
		{
			throw new GameEngineException($"It's not valid to {nameof(MakeACopy)} on {nameof(DisbursementExecuted)}");
		}

		internal override void Update(Disbursement disbursementToBeApplied)
		{
			throw new GameEngineException($"It's not valid to {nameof(Update)} on {nameof(DisbursementExecuted)}");
		}

		internal override Disbursement Copy(WithDrawal newOwner)
		{
			throw new GameEngineException($"A {nameof(DisbursementExecuted)} must not be in memory.");
		}
	}

	public class DisbursementExecutedResponse
	{
		public decimal Amount { get; set; }
		public List<DisbursementExecutionStored> DisbursementExecutionsStored { get; set; } = new List<DisbursementExecutionStored>();
		public List<ProfileStored> requiredProfilesApproval { get; set; } = new List<ProfileStored>();
		internal void Add(DisbursementExecutionStored disbursementExecutionStored)
		{
			DisbursementExecutionsStored.Add(disbursementExecutionStored);
			Amount += disbursementExecutionStored.DecimalAmount;
		}

		internal void Add(ProfileStored profileStored)
		{
			requiredProfilesApproval.Add(profileStored);
		}
	}
	public class DisbursementExecutionStored
	{
		private decimal amount;
		public DisbursementExecutionStored(DateTime date, TransactionStatus executionStatus, int transactionId, int id, decimal amount, Coin cODES, int disbursementId, string user, DateTime scheduledDate, AccountNumberStored accountNumber)
		{
			Date = date;
			StatusType = executionStatus;
			TransactionId = transactionId;
			Id = id;
			this.amount = amount;
			Coin = cODES;
			DisbursementId = disbursementId;
			Who = user;
			AccountNumber = accountNumber.Number;
			AccountNumberDetails = accountNumber;
			ScheduledDate = scheduledDate;
		}

		public DateTime Date { get; set; }
		public TransactionStatus StatusType { get; set; }
		public string Status { get { return StatusType.ToString(); } }
		public int TransactionId { get; set; }
		public int Id { get; set; }
		public string Amount { get { return $"{amount}"; } }
		public string AmountFormated { get { return Currency.Factory(CurrencyCode, amount).ToDisplayFormat(); } }
		public Coin Coin { get; set; }
		public string CurrencyCode { get { return Coin.Iso4217Code;  } }
		public string CurrencyCodeTxt { get { return CurrencyCode.ToString(); } }
		public int DisbursementId { get; set; }
		public string Who { get; set; }
		public string AccountNumber { get; }
		public AccountNumberStored AccountNumberDetails { get; }
		public DateTime ScheduledDate { get; }

		public decimal DecimalAmount { get { return amount; } }
	}
	internal class DisbursementExecutionMessage : KafkaMessage
	{
		internal DisbursementExecutionMessage(int authorizationNumber, DateTime date, DateTime scheduledDate, TransactionStatus status, int transactionId, int id, decimal amount, Coin currencyCode, string approver, AccountNumber accountNumber, Coin accountCurrencyCode)
		{
			AuthorizationNumber = authorizationNumber;
			Date = date;
			Status = status;
			TransactionId = transactionId;
			Id = id;
			Amount = amount;
			CurrencyCode = currencyCode;
			User = approver;
			AccountNumberId = accountNumber.Id;
			AccountCurrencyCode = accountCurrencyCode;
			ScheduledDate = scheduledDate;
		}

		public DisbursementExecutionMessage(string message) : base(message)
		{

		}

		internal int AuthorizationNumber { get; private set; }
		public DateTime ScheduledDate { get; internal set; }
		internal DateTime Date { get; private set; }
		internal TransactionStatus Status { get; private set; }
		internal int TransactionId { get; private set; }
		internal int Id { get; private set; }
		internal decimal Amount { get; private set; }
		internal Coin CurrencyCode { get; private set; }
		internal Coin AccountCurrencyCode { get; private set; }
		internal string User { get; private set; }
		internal int AccountNumberId { get; private set; }

		protected override void Deserialize(string[] message, out int fieldOrder)
		{
			base.Deserialize(message, out fieldOrder);
			int year = int.Parse(message[fieldOrder++]);
			int month = int.Parse(message[fieldOrder++]);
			int day = int.Parse(message[fieldOrder++]);
			int hour = int.Parse(message[fieldOrder++]);
			int minute = int.Parse(message[fieldOrder++]);
			int second = int.Parse(message[fieldOrder++]);
			int miliseconds = int.Parse(message[fieldOrder++]);
			this.Date = new DateTime(year, month, day, hour, minute, second, miliseconds);

			this.Status = (TransactionStatus)int.Parse(message[fieldOrder++]);
			this.TransactionId = int.Parse(message[fieldOrder++]);
			this.Id = int.Parse(message[fieldOrder++]);
			this.Amount = decimal.Parse(message[fieldOrder++]);
			this.CurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			this.User = message[fieldOrder++];
			this.AccountNumberId = int.Parse(message[fieldOrder++]);
			this.AccountCurrencyCode = Coinage.KafkaProperty2Coin(message[fieldOrder++]);
			this.AuthorizationNumber = int.Parse(message[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(Date).
			AddProperty(Date.Millisecond).
			AddProperty((int)Status).
			AddProperty(TransactionId).
			AddProperty(Id).
			AddProperty(Amount).
			AddProperty(CurrencyCode.Iso4217Code).
			AddProperty(User).
			AddProperty(AccountNumberId).
			AddProperty(AccountCurrencyCode.Iso4217Code).
			AddProperty(AuthorizationNumber);
		}
    }
	[Puppet]
	internal class DisbursementExecution : Objeto
	{
		internal DisbursementExecution(DateTime date, TransactionStatus status, string message)
		{
			Date = date;
			Status = status;
			Message = message;
		}

		internal DateTime Date { get; }
		internal TransactionStatus Status { get; }
		internal string Message { get; }

		internal string StatusTxt { get { return Status.ToString(); } }
	}
	public class DisbursementExecutionResponse
	{
		internal DisbursementExecutionResponse(int authorizationNumber, TransactionStatus status, DisbursementExecuted disbursementExecuted, string message)
		{
			Status = status;
			DisbursementExecuted = disbursementExecuted;
			Message = message;
			AuthorizationNumber = authorizationNumber;
		}

		internal int AuthorizationNumber { get; }
		internal TransactionStatus Status { get; }
		internal DisbursementExecuted DisbursementExecuted { get; }
		internal string Message { get; }

		internal DisbursementExecution Execution(DateTime date)
		{
			return new DisbursementExecution(date, Status, Message);
		}

		internal KafkaMessage GenerateMessage(DateTime date, string approver)
		{
			return new DisbursementExecutionMessage(
				AuthorizationNumber, 
				date,
				DisbursementExecuted.ScheduledDate,
				Status,
				DisbursementExecuted.TransactionId,
				DisbursementExecuted.Id,
				DisbursementExecuted.Amount,
				DisbursementExecuted.Coin,
				approver,
				DisbursementExecuted.Account,
				DisbursementExecuted.Account.Coin
				);
		}

	}
	internal sealed class DisbursementUnpayed : DisbursemenWithTransaction
	{
		public DisbursementUnpayed(WithDrawal withdrawal, DisbursementChange disbursement)
			: base(withdrawal, DisbursmentStatusCode.UNPAYED, disbursement.Id, disbursement.Account, disbursement.ScheduledDate, disbursement.Amount, false)
		{
		}

		public DisbursementUnpayed(WithDrawal withdrawal, int id, AccountNumber account, DateTime scheculeddate, decimal amount)
			: base(withdrawal, DisbursmentStatusCode.UNPAYED, id, account, scheculeddate, amount, false)
		{
			if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
		}

		internal override Disbursement Copy(WithDrawal newOwner)
		{
			return new DisbursementUnpayed(newOwner, Id, Account, ScheduledDate, Amount);
		}

		internal override Disbursement MakeACopy()
		{
			return new DisbursementUnpayed(Withdrawal, Id, Account, ScheduledDate, Amount);
		}

		internal DisbursementExecutionResponse Pay(Company company, InprocessWithDrawal inprocessWithDrawal, bool itIsThePresent, DateTime today)
		{
			if (!inprocessWithDrawal.WasCreatedFrom(Withdrawal)) throw new GameEngineException($"It's not possible to pay a {nameof(DisbursementUnpayed)} with a diferente transaction.");

			const int EntityId = 0; //TODO it must change
			const int StoreId = 0;
            WithdrawReponse withdrawReponse = PaymentChannels.WithDraw(
				itIsThePresent,
				inprocessWithDrawal.Processor.Driver.Id, 
				new WithdrawBody(
					Withdrawal.Identifier,
					Amount,
					Withdrawal.Description,
					today,
					string.Empty,
					Withdrawal.Domain.Id, 
					Withdrawal.Domain.Url, 
					PaymentMethod.ThirdParty,
					EntityId,
					StoreId,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
                    new Domain(false, Withdrawal.Domain.Id, Withdrawal.Domain.Url, Agents.TEST_BOOK),
                    string.Empty,
                    string.Empty
                    )
				);

			DisbursementExecuted disbursementExecuted = new DisbursementExecuted(this);
#if DEBUG
			if (inprocessWithDrawal.TransactionId == 10 && (this.Id == 7 || this.Id == 8 || this.Id == 9))
			{
				return new DisbursementExecutionResponse(withdrawReponse.AuthorizationId, Result.TransactionStatus.DENIED, disbursementExecuted, "");
			}
#endif

			return new DisbursementExecutionResponse(withdrawReponse.AuthorizationId, withdrawReponse.Status, disbursementExecuted, "");
		}
	}
	internal class DisbursementChange : Disbursement
	{
		internal DisbursementChange(int id, AccountNumber account, DateTime scheculeddate, decimal amount) : base(DisbursmentStatusCode.CHANGES, id, account, scheculeddate, amount, false)
		{
		}

		internal override Disbursement MakeACopy()
		{
			return new DisbursementChange(Id, Account, ScheduledDate, Amount);
		}

		internal DisbursementUnpayed ToUnpayed(WithDrawal pendingWithDrawal)
		{
			return new DisbursementUnpayed(pendingWithDrawal, this);
		}
	}
	internal class DisbursementsModel
	{
		public List<DisbursementModel> Disbursements { get; set; }
		public int Amount { get; set; }

		internal List<DisbursementRequest> ToBeAddedOrChanged(List<DisbursementRequest> disbursementsRequested)
		{
			List<DisbursementRequest> result = new List<DisbursementRequest>();

			foreach (DisbursementRequest disbursementRequested in disbursementsRequested)
			{
				DisbursementModel item;

				bool itsAlreadyStored = Exists(disbursementRequested.Id, out item);
				bool wasUpdated = itsAlreadyStored
					&& (item.AccountNumber != disbursementRequested.AccountNumber
					|| item.ScheduledDate.ToString() != disbursementRequested.ScheduledDate.ToString()
					|| item.Amount != disbursementRequested.Amount);

				if ( !itsAlreadyStored || wasUpdated)
				{
					result.Add(disbursementRequested);
				}
			}
			return result;
		}

		private bool Exists(int id, out DisbursementModel item)
		{
			item = null;
			if (Disbursements == null) return false;

			item = Disbursements.FirstOrDefault(itemStored => itemStored.Id == id);
			return (item != null);
		}

		internal List<int> ToBeRemoved(List<DisbursementRequest> requested)
		{
			List<int> result = new List<int>();

			if (Disbursements != null && Disbursements.Count() > 0) 
			{
				foreach (DisbursementModel disbursementsStored in Disbursements)
				{
					bool wasDeleted = !requested.Any(item => item.Id == disbursementsStored.Id);

					if (wasDeleted) result.Add(disbursementsStored.Id);
				}
			}
				
			return result;
		}
	}
	public class DisbursementModel
	{
		public bool ItsExecuted { get; set; }
		public int Id { get; set; }
		public string AccountNumber { get; set; }
		public string ScheduledDate { get; set; }
		public decimal Amount { get; set; }
	}
	public class DisbursementRequest
	{
		public int Id { get; set; }
		public string AccountNumber { get; set; }
		public string ScheduledDate { get; set; }
		public decimal Amount { get; set; }
	}
	
	internal sealed class DisbursmentsByDate : Objeto
	{
		private Dictionary<long, Disbursements> disbusmentsByDate = new Dictionary<long, Disbursements>();

		internal void Remove(DateTime scheduledDate, Disbursement disbursement)
		{
			Disbursements disbursements;
			bool found = disbusmentsByDate.TryGetValue(scheduledDate.Date.Ticks, out disbursements);

			if ( ! found)
			{
				throw new GameEngineException($"There are no {nameof(Disbursements)} for {scheduledDate.Date}.");
			}
			else
			{
				disbursements.Remove(disbursement);
				if (disbursements.Count() == 0)
				{
					disbusmentsByDate.Remove(scheduledDate.Date.Ticks);
				}
			}
		}
		internal void Add(Disbursement disbursement)
		{
			Disbursements disbursements;
			bool found = disbusmentsByDate.TryGetValue(disbursement.ScheduledDate.Date.Ticks, out disbursements);

			if (found)
			{
				disbursements.Add(disbursement);
			}
			else
			{
				disbursements = new Disbursements();
				disbursements.Add(disbursement);
				disbusmentsByDate.Add(disbursement.ScheduledDate.Date.Ticks, disbursements);
			}
		}

		internal int Count()
		{
			return disbusmentsByDate.Count();
		}

		internal Disbursements List(DateTime date)
		{
			Disbursements disbursements;
			bool found = disbusmentsByDate.TryGetValue(date.Date.Ticks, out disbursements);

			if ( ! found)
			{
				disbursements = new Disbursements();		
			}
			return disbursements;
		}
	}
}
