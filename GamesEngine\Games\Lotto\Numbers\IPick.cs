﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
	public interface IPick
	{
        IEnumerable<SubTicket<IPick>> SubTickets();

        IEnumerable<IPick> Split();

        string AsString();

        long ToInt64();

        string AsStringForAccounting();

        int Count { get; }

        int Length { get; }

        Pattern this[int i] { get; }

        IEnumerable<IPick> Permute();
    }
}
