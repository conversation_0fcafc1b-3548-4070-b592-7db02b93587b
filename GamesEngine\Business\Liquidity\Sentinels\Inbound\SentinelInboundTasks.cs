﻿using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;


namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class SentinelInboundTasks
    {
        internal static int DELAY_SECONDS_PER_CONFIRMATION = 3;

        private readonly ConcurrentDictionary<string, InboundTask> inboundTasks = new();

        internal SentinelInboundTasks() { }

        internal IEnumerable<ConfirmationsInboundTask> Tasks
        {
            get
            {
                List<ConfirmationsInboundTask> results = new List<ConfirmationsInboundTask>();
                foreach (var taskInbound in inboundTasks.Values)
                {
                    results.Add(taskInbound as ConfirmationsInboundTask);
                }
                return results;
            }
        }
        internal InboundTask FindInboundTask(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentException("Invoice ID cannot be null or empty.", nameof(invoiceId));
            if (inboundTasks.TryGetValue(invoiceId, out var inboundTask))
            {
                return inboundTask;
            }
            throw new KeyNotFoundException($"No inbound task found for invoice ID: {invoiceId}.");
        }

        internal ConfirmationsInboundTask AddConfirmationsWatcher(Deposit deposit, int totalConfirmations)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit), "Deposit cannot be null.");
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations), "Total confirmations must be greater than or equal to zero.");

            if (!inboundTasks.TryGetValue(deposit.InvoiceId, out InboundTask confirmsInboundTask))
            {
                confirmsInboundTask = new ConfirmationsInboundTask(this, deposit, totalConfirmations);
                inboundTasks.TryAdd(deposit.InvoiceId, confirmsInboundTask);
            }
            return confirmsInboundTask as ConfirmationsInboundTask;
        }

        internal void Detach(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentException("Invoice ID cannot be null or empty.", nameof(invoiceId));
            if (!inboundTasks.TryRemove(invoiceId, out _))
            {
                throw new GameEngineException($"No inbound task found for invoice ID: {invoiceId}.");
            }
        }

        internal void Detach(InboundTask inboundTask)
        {
            if (inboundTask is ConfirmationsInboundTask confirmationsInbound)
            {
                Detach(confirmationsInbound.Deposit.InvoiceId);
            }
            else
            {
                throw new GameEngineException("Invalid inbound task type. Only ConfirmationsInboundTask can be detached.");
            }
        }
    }
}
