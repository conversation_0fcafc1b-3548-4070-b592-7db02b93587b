﻿using GamesEngine.Games.Tournaments;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
    class Lines
    {
        private readonly List<Line> lines;
        private readonly Game game;
        Lines(Game game)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            this.game = game;
        }

    }
}
