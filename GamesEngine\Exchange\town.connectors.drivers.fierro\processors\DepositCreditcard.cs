﻿using Connectors.town.connectors.driver.transactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
	internal abstract class DepositCreditcard : Deposit
	{
		private int authorizationNumber = 0;
        //public DepositCreditcard()
        //	: base("Fiero", PaymentMethod.Creditcard, "USD", TransactionType.Deposit, 1)
        //{
        //}

        public DepositCreditcard(string currencyIsoCode) : base(currencyIsoCode, PaymentMethod.Creditcard)
        {
        }

        public override string Description => "Fiero driver";
		public override string Fabricator => "Ncubo";

		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var result = new DepositTransaction(++authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			base.Execute<T>(now, recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public override void Prepare(DateTime now)
		{
			base.Prepare(now);
        }
	}
}
