﻿using Puppeteer.EventSourcing.Libraries;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System;
using GamesEngine.Gameboards.Lotto;

namespace GamesEngine.Games.Lotto
{
    public class SubTicket<Pick> where Pick : IPick
    {
        private Pick number;

        internal int this[int i]
        {
            get
            {
                return number[i].SingleDigit;
            }
        }

        public SubTicket(int digit1, int digit2)
        {
            ValidateDigit(1, digit1);
            ValidateDigit(2, digit2);
            number = (Pick)(IPick)new Pick2(digit1, digit2);
        }

        internal SubTicket(IPick number)
        {
            if (number.Count != 1) throw new GameEngineException("Pick subticket must be only one");
            this.number = (Pick)number;
        }

        public SubTicket(int digit1, int digit2, int digit3)
        {
            ValidateDigit(1, digit1);
            ValidateDigit(2, digit2);
            ValidateDigit(3, digit3);
            number = (Pick)(IPick)new Pick3(digit1, digit2, digit3);
        }

        public SubTicket(int digit1, int digit2, int digit3, int digit4)
        {
            ValidateDigit(1, digit1);
            ValidateDigit(2, digit2);
            ValidateDigit(3, digit3);
            ValidateDigit(4, digit4);
            number = (Pick)(IPick)new Pick4(digit1, digit2, digit3, digit4);
        }

        public SubTicket(int digit1, int digit2, int digit3, int digit4, int digit5)
        {
            ValidateDigit(1, digit1);
            ValidateDigit(2, digit2);
            ValidateDigit(3, digit3);
            ValidateDigit(4, digit4);
            ValidateDigit(5, digit5);
            number = (Pick)(IPick)new Pick5(digit1, digit2, digit3, digit4, digit5);
        }

        internal SubTicket(PowerBall powerBall)
        {
            number = (Pick)(IPick)powerBall;
        }

        protected void ValidateDigit(int order, int digit)
        {
            if (digit < 0 || digit > 9) throw new GameEngineException($"Digit {order} has value {digit} which is not valid. It must be 0..9");
        }

        public string AsString() => number.AsString();

        internal long ToInt64() => number.ToInt64();

        internal int Length
        {
            get
            {
                return number.Length;
            }
        }

        internal Pick Number
        {
            get
            {
                return this.number;
            }
        }

        internal IEnumerable<IPick> Permute() => number.Permute();

        public string AsStringForAccounting() => number.AsStringForAccounting();

        internal bool IsTheSameNumberWithDifferentOrder(int digit1, int digit2, int digit3, int digit4, int digit5)
        {
            var pick = (Pick5)(IPick)this.number;
            var result = pick.IsBoxed(digit1, digit2, digit3, digit4, digit5);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrderForFireball(int digit1, int digit2, int digit3, int digit4, int digit5, int digitFireball)
        {
            var result = IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3, digit4, digit5) || 
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3, digit4, digitFireball) || 
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3, digitFireball, digit5) || 
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digitFireball, digit4, digit5) || 
                IsTheSameNumberWithDifferentOrder(digit1, digitFireball, digit3, digit4, digit5) || 
                IsTheSameNumberWithDifferentOrder(digitFireball, digit2, digit3, digit4, digit5);
            return result;
        }
            
        internal bool IsTheSameNumberWithTheSameOrder(int digit1, int digit2, int digit3, int digit4, int digit5)
        {
            var result = this.number[1].SingleDigit == digit1 && this.number[2].SingleDigit == digit2 && this.number[3].SingleDigit == digit3 && this.number[4].SingleDigit == digit4 && this.number[5].SingleDigit == digit5;
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrderForFireball(int digit1, int digit2, int digit3, int digit4, int digit5, int digitFireball)
        {
            var result = IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3, digit4, digit5) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3, digit4, digitFireball) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3, digitFireball, digit5) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digitFireball, digit4, digit5) || 
                IsTheSameNumberWithTheSameOrder(digit1, digitFireball, digit3, digit4, digit5) || 
                IsTheSameNumberWithTheSameOrder(digitFireball, digit2, digit3, digit4, digit5);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrder(int digit1, int digit2, int digit3, int digit4)
        {
            var pick = (Pick4)(IPick)this.number;
            var result = pick.IsMarked(digit1, digit2, digit3, digit4) ||
                pick.IsMarked(digit1, digit2, digit4, digit3) ||
                pick.IsMarked(digit1, digit3, digit2, digit4) ||
                pick.IsMarked(digit1, digit3, digit4, digit2) ||
                pick.IsMarked(digit1, digit4, digit2, digit3) ||
                pick.IsMarked(digit1, digit4, digit3, digit2) ||

                pick.IsMarked(digit2, digit1, digit3, digit4) ||
                pick.IsMarked(digit2, digit1, digit4, digit3) ||
                pick.IsMarked(digit2, digit3, digit1, digit4) ||
                pick.IsMarked(digit2, digit3, digit4, digit1) ||
                pick.IsMarked(digit2, digit4, digit1, digit3) ||
                pick.IsMarked(digit2, digit4, digit3, digit1) ||

                pick.IsMarked(digit3, digit1, digit2, digit4) ||
                pick.IsMarked(digit3, digit1, digit4, digit2) ||
                pick.IsMarked(digit3, digit2, digit1, digit4) ||
                pick.IsMarked(digit3, digit2, digit4, digit1) ||
                pick.IsMarked(digit3, digit4, digit1, digit2) ||
                pick.IsMarked(digit3, digit4, digit2, digit1) ||

                pick.IsMarked(digit4, digit1, digit2, digit3) ||
                pick.IsMarked(digit4, digit1, digit3, digit2) ||
                pick.IsMarked(digit4, digit2, digit1, digit3) ||
                pick.IsMarked(digit4, digit2, digit3, digit1) ||
                pick.IsMarked(digit4, digit3, digit1, digit2) ||
                pick.IsMarked(digit4, digit3, digit2, digit1);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrderForFireball(int digit1, int digit2, int digit3, int digit4, int digitFireball)
        {
            var result = IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3, digit4) ||
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3, digitFireball) ||
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digitFireball, digit4) ||
                IsTheSameNumberWithDifferentOrder(digit1, digitFireball, digit3, digit4) ||
                IsTheSameNumberWithDifferentOrder(digitFireball, digit2, digit3, digit4);
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrder(int digit1, int digit2, int digit3, int digit4)
        {
            var result = this.number[1].SingleDigit == digit1 && this.number[2].SingleDigit == digit2 && this.number[3].SingleDigit == digit3 && this.number[4].SingleDigit == digit4;
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrderForFireball(int digit1, int digit2, int digit3, int digit4, int digitFireball)
        {
            var result = IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3, digit4) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3, digitFireball) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digitFireball, digit4) || 
                IsTheSameNumberWithTheSameOrder(digit1, digitFireball, digit3, digit4) || 
                IsTheSameNumberWithTheSameOrder(digitFireball, digit2, digit3, digit4);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrder(int digit1, int digit2, int digit3)
        {
            var pick = (Pick3)(IPick)this.number;
            var result = pick.IsMarked(digit1, digit2, digit3) ||
                pick.IsMarked(digit1, digit3, digit2) ||
                pick.IsMarked(digit3, digit1, digit2) ||
                pick.IsMarked(digit3, digit2, digit1) ||
                pick.IsMarked(digit2, digit3, digit1) ||
                pick.IsMarked(digit2, digit1, digit3);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrderForFireball(int digit1, int digit2, int digit3, int digitFireball)
        {
            var result = IsTheSameNumberWithDifferentOrder(digit1, digit2, digit3) ||
                IsTheSameNumberWithDifferentOrder(digit1, digit2, digitFireball) ||
                IsTheSameNumberWithDifferentOrder(digit1, digitFireball, digit3) ||
                IsTheSameNumberWithDifferentOrder(digitFireball, digit2, digit3);
            return result;
        }
            
        internal bool IsTheSameNumberWithTheSameOrder(int digit1, int digit2, int digit3)
        {
            var result = this.number[1].SingleDigit == digit1 && this.number[2].SingleDigit == digit2 && this.number[3].SingleDigit == digit3;
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrderForFireball(int digit1, int digit2, int digit3, int digitFireball)
        {
            var result = IsTheSameNumberWithTheSameOrder(digit1, digit2, digit3) || 
                IsTheSameNumberWithTheSameOrder(digit1, digit2, digitFireball) || 
                IsTheSameNumberWithTheSameOrder(digit1, digitFireball, digit3) || 
                IsTheSameNumberWithTheSameOrder(digitFireball, digit2, digit3);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrder(int digit1, int digit2)
        {
            var pick = (Pick2)(IPick)this.number;
            var result = pick.IsMarked(digit1, digit2) || pick.IsMarked(digit2, digit1);
            return result;
        }

        internal bool IsTheSameNumberWithDifferentOrderForFireball(int digit1, int digit2, int digitFireball)
        {
            var result = IsTheSameNumberWithDifferentOrder(digit1, digit2) || IsTheSameNumberWithDifferentOrder(digit1, digitFireball) || IsTheSameNumberWithDifferentOrder(digitFireball, digit2);
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrder(int digit1, int digit2)
        {
            var result = this.number[1].SingleDigit == digit1 && this.number[2].SingleDigit == digit2;
            return result;
        }

        internal bool IsTheSameNumberWithTheSameOrderForFireball(int digit1, int digit2, int digitFireball)
        {
            var result = IsTheSameNumberWithTheSameOrder(digit1, digit2) || IsTheSameNumberWithTheSameOrder(digit1, digitFireball) || IsTheSameNumberWithTheSameOrder(digitFireball, digit2);
            return result;
        }

        internal bool BelongTo(IdOfLottery idOfLottery)
        {
            switch (idOfLottery)
            {
                case IdOfLottery.P2:
                    return number is Pick2;
                case IdOfLottery.P3:
                    return number is Pick3;
                case IdOfLottery.P4:
                    return number is Pick4;
                case IdOfLottery.P5:
                    return number is Pick5;
                default:
                    throw new GameEngineException($"{nameof(idOfLottery)} {idOfLottery} is no valid");
            }
        }

        static internal SubTicket<IPick> SubticketFromNumber(string number)
        {
            if (string.IsNullOrWhiteSpace(number)) throw new ArgumentNullException(nameof(number));

            IPick pick;
            switch (number.Length)
            {
                case 2:
                    pick = new Pick2(number);
                    return new SubTicket<IPick>(pick[1].SingleDigit, pick[2].SingleDigit);
                case 3:
                    pick = new Pick3(number);
                    return new SubTicket<IPick>(pick[1].SingleDigit, pick[2].SingleDigit, pick[3].SingleDigit);
                case 4:
                    pick = new Pick4(number);
                    return new SubTicket<IPick>(pick[1].SingleDigit, pick[2].SingleDigit, pick[3].SingleDigit, pick[4].SingleDigit);
                case 5:
                    pick = new Pick5(number);
                    return new SubTicket<IPick>(pick[1].SingleDigit, pick[2].SingleDigit, pick[3].SingleDigit, pick[4].SingleDigit, pick[5].SingleDigit);
                default:
                    throw new GameEngineException($"There is no valid pick for {nameof(number)} {number}");
            }
        }

        static internal IEnumerable<SubTicket<IPick>> GenerateSubticketsByEnding(IPick pick)
        {
            if (pick == null) throw new ArgumentNullException(nameof(number));

            var result = Enumerable.Empty<SubTicket<IPick>>();
            switch (pick.Length)
            {
                case 3:
                    result = result.Append(new SubTicket<IPick>(pick[2].SingleDigit, pick[3].SingleDigit));
                    break;
                case 4:
                    result = result.Append(new SubTicket<IPick>(pick[3].SingleDigit, pick[4].SingleDigit));
                    result = result.Append(new SubTicket<IPick>(pick[2].SingleDigit, pick[3].SingleDigit, pick[4].SingleDigit));
                    break;
                case 5:
                    result = result.Append(new SubTicket<IPick>(pick[4].SingleDigit, pick[5].SingleDigit));
                    result = result.Append(new SubTicket<IPick>(pick[3].SingleDigit, pick[4].SingleDigit, pick[5].SingleDigit));
                    result = result.Append(new SubTicket<IPick>(pick[2].SingleDigit, pick[3].SingleDigit, pick[4].SingleDigit, pick[5].SingleDigit));
                    break;
            }
            return result;
        }
    }
}
