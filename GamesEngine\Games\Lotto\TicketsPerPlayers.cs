﻿using GamesEngine.Bets;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal abstract class TicketsPerPlayers : Objeto
    {
        protected readonly Dictionary<DrawPerPlayerKey, TicketsPerPlayerInPendingDraw> ticketsPerPlayers = new Dictionary<DrawPerPlayerKey, TicketsPerPlayerInPendingDraw>();

        internal IEnumerable<TicketsPerPlayerInPendingDraw> GetAll
        {
            get
            {
                return ticketsPerPlayers.Values.ToList();
            }
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var ticketsPerPlayer in ticketsPerPlayers.Values)
            {
                total += ticketsPerPlayer.TotalTicketAmount();
            }
            return total;
        }

        internal decimal ExpectedProfit()
        {
            var total = 0m;
            foreach (var ticketsPerPlayer in ticketsPerPlayers.Values)
            {
                total += ticketsPerPlayer.ExpectedProfit();
            }
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var ticketsPerPlayer in ticketsPerPlayers.Values)
            {
                total += ticketsPerPlayer.TotalWagers();
            }
            return total;
        }

        internal List<TicketWager> Wagers()
        {
            var wagers = new List<TicketWager>();
            foreach (var ticketsPerPlayer in ticketsPerPlayers.Values)
            {
                foreach (var wager in ticketsPerPlayer.Wagers())
                {
                    wagers.Add(wager);
                }
            }
            return wagers;
        }
    }

    struct DrawPerPlayerKey
    {
        private readonly string accountNumber;
        private readonly string gameType;
        private readonly string state;
        private readonly int drawingId;
        private readonly int domainId;
        private readonly int affiliateId;
        private readonly int drawYear;
        private readonly int drawMonth;
        private readonly int drawDay;
        private readonly int drawHour;
        private readonly int drawMinute;

        internal DrawPerPlayerKey(string accountNumber, string state, string gameType, int drawingId, int affiliateId, int domainId, int drawYear, int drawMonth, int drawDay, int drawHour, int drawMinute)
        {
            this.accountNumber = accountNumber;
            this.gameType = gameType;
            this.state = state;
            this.drawingId = drawingId;
            this.domainId = domainId;
            this.affiliateId = affiliateId;
            this.drawYear = drawYear;
            this.drawMonth = drawMonth;
            this.drawDay = drawDay;
            this.drawHour = drawHour;
            this.drawMinute = drawMinute;
        }
    }

    internal class TicketsPerPlayersInPendingDraws : TicketsPerPlayers
    {
        internal TicketsPerPlayersInPendingDraws(IEnumerable<TicketsPerPlayerInPendingDraw> pendingDraws)
        {
            foreach (var pendingDraw in pendingDraws)
            {
                foreach (var ticket in pendingDraw.Tickets)
                {
                    var key = new DrawPerPlayerKey(pendingDraw.AccountNumber, ticket.LotteryState().Abbreviation, pendingDraw.PendingDraw.GameTypeForReports, pendingDraw.PendingDraw.Schedule.Id, pendingDraw.PendingDraw.AffiliateId, pendingDraw.PendingDraw.DomainId, ticket.DrawDate.Year, ticket.DrawDate.Month, ticket.DrawDate.Day, ticket.DrawDate.Hour, ticket.DrawDate.Minute);
                    var ticketsPerPlayerInPendingDraw = ticketsPerPlayers.GetValueOrDefault(key);
                    if (ticketsPerPlayerInPendingDraw == null)
                    {
                        ticketsPerPlayers.Add(key, new TicketsPerPlayerInPendingDraw(ticket.Player, pendingDraw.PendingDraw, ticket));
                    }
                    else
                    {
                        ticketsPerPlayerInPendingDraw.Add(ticket);
                    }
                }
            }
        }
        
        internal TicketsPerPlayersInPendingDraws(IEnumerable<PendingDraw> pendingDraws, string accountNumber = "", long ticketNumber = 0)
        {
            if (! pendingDraws.Any()) throw new GameEngineException($"{nameof(pendingDraws)} is empty");

            var areAllAccountNumbersValid = String.IsNullOrWhiteSpace(accountNumber);
            var areAllTicketNumbersValid = ticketNumber == 0;
            foreach (var pendingDraw in pendingDraws)
            {
                foreach (var ticket in pendingDraw.Tickets)
                {
                    var owner = ticket.Player;
                    if ((areAllAccountNumbersValid || owner.AccountNumber == accountNumber)&&(areAllTicketNumbersValid || ticket.TicketNumber == ticketNumber))
                    {
                        int scheduleId = ticket.BelongsToFireBallDraw ? pendingDraw.Schedule.FireballId : pendingDraw.Schedule.Id;
                        var key = new DrawPerPlayerKey(owner.AccountNumber, ticket.LotteryState().Abbreviation, pendingDraw.GameTypeForReports, scheduleId, pendingDraw.AffiliateId, pendingDraw.DomainId, ticket.DrawDate.Year, ticket.DrawDate.Month, ticket.DrawDate.Day, ticket.DrawDate.Hour, ticket.DrawDate.Minute);
                        var ticketsPerPlayerInPendingDraw = ticketsPerPlayers.GetValueOrDefault(key);
                        if (ticketsPerPlayerInPendingDraw == null)
                        {
                            ticketsPerPlayers.Add(key, new TicketsPerPlayerInPendingDraw(owner, pendingDraw, ticket));
                        }
                        else
                        {
                            ticketsPerPlayerInPendingDraw.Add(ticket);
                        }
                    }
                }
            }
        }
    }

    internal class TicketsPerPlayersEmpty : TicketsPerPlayers
    {
    }

    internal class TicketsPerPlayerInPendingDraw : Objeto
    {
        private readonly List<Ticket> tickets = new List<Ticket>();
        private readonly Player owner;
        private readonly PendingDraw pendingDraw;

        internal IEnumerable<Ticket> Tickets
        {
            get
            {
                return tickets;
            }
        }

        internal string AccountNumber
        {
            get
            {
                return owner.AccountNumber;
            }
        }

        internal PendingDraw PendingDraw
        {
            get
            {
                return pendingDraw;
            }
        }

        internal TicketsPerPlayerInPendingDraw(Player owner, PendingDraw pendingDraw, Ticket ticket)
        {
            if (owner == null) throw new ArgumentNullException(nameof(owner));
            if (pendingDraw == null) throw new ArgumentNullException(nameof(pendingDraw));
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));

            this.owner = owner;
            this.pendingDraw = pendingDraw;
            tickets.Add(ticket);
        }

        internal void Add(Ticket ticket)
        {
            if (owner != ticket.Player) throw new GameEngineException($"{nameof(owner)} does not belong to these group of tickets");

            tickets.Add(ticket);
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var ticket in tickets)
            {
                total += ticket.TicketAmount();
            }
            return total;
        }

        internal decimal ExpectedProfit()
        {
            var total = 0m;
            foreach (var ticket in tickets)
            {
                if (! ticket.WasPurchasedForFree)
                {
                    total += ticket.TicketAmount();
                }
            }
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var ticket in tickets)
            {
                total += ticket.Wagers.Count();
            }
            return total;
        }

        internal List<TicketWager> Wagers()
        {
            var wagers = new List<TicketWager>();
            foreach (var ticket in tickets)
            {
                foreach (var wager in ticket.Wagers)
                {
                    wagers.Add(wager);
                }
            }
            return wagers;
        }
    }

    internal class WagersPerPlayerInPendingDraw : Objeto
    {
        protected readonly List<TicketWager> wagers = new List<TicketWager>();

        internal WagersPerPlayerInPendingDraw(IEnumerable<TicketWager> wagers)
        {
            foreach (var wager in wagers)
            {
                this.wagers.Add(wager);
            }
        }

        internal WagersPerPlayerInPendingDraw(TicketsPerPlayers ticketsPerPlayers, string ticketNumber)
        {
            if (ticketsPerPlayers == null) throw new ArgumentNullException(nameof(ticketsPerPlayers));

            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            if (! areAllTicketNumbersValid && ! isTicketNumberWithWagerNumber)
            {
                long.TryParse(ticketNumber, out numericTicketNumber);
            }
            
            foreach (var wager in ticketsPerPlayers.Wagers())
            {
                if (areAllTicketNumbersValid || wager.Ticket.TicketNumber == numericTicketNumber)
                {
                    wagers.Add(wager);
                }
                else if (isTicketNumberWithWagerNumber && wager.FullWagerNumber() == ticketNumber)
                {
                    wagers.Add(wager);
                    break;
                }
            }
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var ticket in wagers)
            {
                total += ticket.Risk;
            }
            return total;
        }

        internal decimal ExpectedProfit()
        {
            var total = 0m;
            foreach (var ticket in wagers)
            {
                total += ticket.ExpectedProfit;
            }
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var ticket in wagers)
            {
                total += 1;
            }
            return total;
        }

        internal List<TicketWager> Wagers()
        {
            return wagers;
        }
    }

    abstract class TicketsPerPlayersInCompletedDraws : Objeto
    {
        abstract internal IEnumerable<TicketsPerPlayerInCompletedDraw> GetAll { get; }

        private HashSet<string> AccountNumbers()
        {
            var players = new HashSet<string>();
            foreach (var ticketPerPlayer in GetAll)
            {
                players.Add(ticketPerPlayer.AccountNumber);
            }
            return players;
        }

        internal int TotalPlayers()
        {
            return AccountNumbers().Count;
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var ticketPerPlayer in GetAll)
            {
                total += ticketPerPlayer.TotalTicketAmount();
            }
            return total;
        }

        internal decimal TotalTicketAmountForFree()
        {
            var total = TotalTicketAmount() - TotalPrize() - TotalProfit();
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var ticketPerPlayer in GetAll)
            {
                total += ticketPerPlayer.TotalWagers();
            }
            return total;
        }

        internal decimal TotalPrize()
        {
            var total = 0m;
            foreach (var ticketPerPlayer in GetAll)
            {
                total += ticketPerPlayer.TotalPrize();
            }
            return total;
        }

        internal decimal TotalProfit()
        {
            var total = 0m;
            foreach (var ticketPerPlayer in GetAll)
            {
                total += ticketPerPlayer.Profit();
            }
            return total;
        }
    }

    internal class TicketsPerPlayersInCompletedPicksDraws : TicketsPerPlayersInCompletedDraws
    {
        private readonly Dictionary<DrawPerPlayerKey, TicketsPerPlayerInCompletedPicksDraw> ticketsPerPlayers = new Dictionary<DrawPerPlayerKey, TicketsPerPlayerInCompletedPicksDraw>();

        internal override IEnumerable<TicketsPerPlayerInCompletedDraw> GetAll
        {
            get
            {
                return ticketsPerPlayers.Values.ToList();
            }
        }

        internal TicketsPerPlayersInCompletedPicksDraws(CompletedPicksDraws completedDraws)
        {
            foreach (CompletedPicksDraw completedDraw in completedDraws.GetAll)
            {
                foreach (CompletedPicksTicket ticket in completedDraw.Tickets)
                {
                    var key = new DrawPerPlayerKey(ticket.AccountNumber, ticket.State, ticket.GameTypeForReports, ticket.DrawingId, ticket.AffiliateId, ticket.DomainId, ticket.DrawDate.Year, ticket.DrawDate.Month, ticket.DrawDate.Day, ticket.DrawDate.Hour, ticket.DrawDate.Minute);
                    var ticketsPerPlayer = ticketsPerPlayers.GetValueOrDefault(key);
                    if (ticketsPerPlayer == null)
                    {
                        ticketsPerPlayer = new TicketsPerPlayerInCompletedPicksDraw(ticket.AccountNumber, completedDraw, ticket);
                        ticketsPerPlayers.Add(key, ticketsPerPlayer);
                    }
                    else
                    {
                        ticketsPerPlayer.Add(ticket);
                    }
                }
            }
        }

        internal IEnumerable<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            foreach (var ticketPerPlayer in ticketsPerPlayers.Values)
            {
                foreach (var wager in ticketPerPlayer.Wagers())
                {
                    wagers.Add(wager);
                }
            }
            return wagers;
        }

    }

    internal class TicketsPerPlayersInCompletedKenoDraws : TicketsPerPlayersInCompletedDraws
    {
        private readonly Dictionary<string, TicketsPerPlayerInCompletedKenoDraw> ticketsPerPlayers = new Dictionary<string, TicketsPerPlayerInCompletedKenoDraw>();

        internal override IEnumerable<TicketsPerPlayerInCompletedDraw> GetAll
        {
            get
            {
                return ticketsPerPlayers.Values.ToList();
            }
        }

        internal IEnumerable<CompletedTicket> Tickets
        {
            get
            {
                return ticketsPerPlayers.Values.SelectMany(tickets => tickets.Tickets).ToList();
            }
        }

        string GetUniqueKey(CompletedKenoTicket ticket)
        {
            return $"{ticket.AccountNumber}_{ticket.DomainId}_{ticket.DrawDate.ToString("MM/dd/yyyy HH:mm")}";
        }

        internal TicketsPerPlayersInCompletedKenoDraws(CompletedKenoDraws completedDraws)
        {
            foreach (CompletedKenoDraw completedDraw in completedDraws.GetAll)
            {
                foreach (CompletedKenoTicket ticket in completedDraw.Tickets)
                {
                    var key = GetUniqueKey(ticket);
                    var ticketsPerPlayer = ticketsPerPlayers.GetValueOrDefault(key);
                    if (ticketsPerPlayer == null)
                    {
                        ticketsPerPlayer = new TicketsPerPlayerInCompletedKenoDraw(ticket.AccountNumber, completedDraw);
                        ticketsPerPlayers.Add(key, ticketsPerPlayer);
                    }
                    ticketsPerPlayer.Add(ticket);
                }
            }
        }
    }

    abstract class TicketsPerPlayerInCompletedDraw : Objeto
    {
        internal IEnumerable<CompletedTicket> Tickets { get; private protected set; } = new List<CompletedTicket>();
        internal string AccountNumber { get; private protected set; }
        internal CompletedDraw CompletedDraw { get; private protected set; }

        public TicketsPerPlayerInCompletedDraw(string accountNumber, CompletedDraw completedDraw)
        {
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (completedDraw == null) throw new ArgumentNullException(nameof(completedDraw));

            AccountNumber = accountNumber;
            CompletedDraw = completedDraw;
        }

        internal void Add(CompletedTicket ticket)
        {
            if (AccountNumber != ticket.AccountNumber) throw new GameEngineException($"{nameof(AccountNumber)} {AccountNumber} does not belong to these group of tickets");

            Tickets = Tickets.Concat(new[] { ticket });
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var ticket in Tickets)
            {
                if (ticket.IsNoAction) continue;
                total += ticket.TicketAmount;
            }
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var ticket in Tickets)
            {
                total += ticket.CountWagers;
            }
            return total;
        }

        internal decimal TotalPrize()
        {
            var total = 0m;
            foreach (var ticket in Tickets)
            {
                total += ticket.Prize;
            }
            return total;
        }

        internal decimal Profit()
        {
            var total = 0m;
            foreach (var ticket in Tickets)
            {
                total += ticket.Profit;
            }
            return total;
        }
    }

    internal class TicketsPerPlayerInCompletedPicksDraw : TicketsPerPlayerInCompletedDraw
    {
        internal TicketsPerPlayerInCompletedPicksDraw(string accountNumber, CompletedPicksDraw completedDraw, CompletedPicksTicket ticket) : base(accountNumber, completedDraw)
        {
            if (ticket == null) throw new ArgumentNullException(nameof(ticket));

            Tickets = Tickets.Concat(new[] { ticket });
        }

        internal List<CompletedWager> Wagers()
        {
            var wagers = new List<CompletedWager>();
            foreach (var ticket in Tickets)
            {
                foreach (var wager in ((CompletedPicksTicket)ticket).Wagers)
                {
                    wagers.Add(wager);
                }
            }
            return wagers;
        }
    }

    internal class TicketsPerPlayerInCompletedKenoDraw : TicketsPerPlayerInCompletedDraw
    {
        internal TicketsPerPlayerInCompletedKenoDraw(string accountNumber, CompletedKenoDraw completedDraw) : base(accountNumber, completedDraw)
        {
        }
    }

    internal class WagersPerPlayerInCompletedDraw : Objeto
    {
        private readonly List<CompletedWager> wagers = new List<CompletedWager>();

        internal WagersPerPlayerInCompletedDraw(TicketsPerPlayersInCompletedPicksDraws ticketsPerPlayersInCompletedDraws, string ticketNumber)
        {
            if (ticketsPerPlayersInCompletedDraws == null) throw new ArgumentNullException(nameof(ticketsPerPlayersInCompletedDraws));

            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            if (!areAllTicketNumbersValid && !isTicketNumberWithWagerNumber)
            {
                long.TryParse(ticketNumber, out numericTicketNumber);
            }
            
            foreach (var wager in ticketsPerPlayersInCompletedDraws.Wagers())
            {
                if (areAllTicketNumbersValid || wager.Ticket.TicketNumber == numericTicketNumber)
                {
                    wagers.Add(wager);
                }
                else if (isTicketNumberWithWagerNumber && wager.FullWagerNumber() == ticketNumber)
                {
                    wagers.Add(wager);
                    break;
                }
            }
        }

        internal decimal TotalTicketAmount()
        {
            var total = 0m;
            foreach (var wager in wagers)
            {
                total += wager.Risk();
            }
            return total;
        }

        internal int TotalWagers()
        {
            var total = 0;
            foreach (var wager in wagers)
            {
                total += 1;
            }
            return total;
        }

        internal decimal TotalPrize()
        {
            var total = 0m;
            foreach (var wager in wagers)
            {
                total += wager.Prize();
            }
            return total;
        }

        internal decimal TotalProfit()
        {
            var total = 0m;
            foreach (var wager in wagers)
            {
                total += wager.Profit();
            }
            return total;
        }

        internal List<CompletedWager> Wagers()
        {
            return wagers;
        }
    }
}