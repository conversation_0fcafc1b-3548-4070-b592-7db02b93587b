﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
	[Puppet]
	class GameFavorite : Objeto
	{
		private readonly Game game;
		private Favorite favorite;
		internal readonly static GameFavorite NONE = new GameFavorite();

		internal enum Favorite { A=1, B=2, Both=3, None=4};

		private GameFavorite(Game game)
		{
			if (game == null) throw new ArgumentNullException(nameof(game));

			this.game = game;
		}

		private GameFavorite()
		{
			this.favorite = Favorite.None;
		}

		internal static GameFavorite Both(Game game)
		{
			var result = new GameFavorite(game);
			result.favorite = Favorite.Both;

			return result;
		}

		internal static GameFavorite Team(Game game, Team favorite)
		{
			if (game.TeamA != favorite && game.TeamB != favorite) throw new GameEngineException($"Favorite team does not belong to match {game.ToString()}");

			var result = new GameFavorite(game);
			result.favorite = game.TeamA == favorite ? Favorite.A : Favorite.B;

			return result;
		}

		internal bool IsTeamA()
		{
			return this.favorite == Favorite.A;
		}

		internal bool IsTeamB()
		{
			return this.favorite == Favorite.B;
		}

		internal bool AreBoth()
		{
			return this.favorite == Favorite.Both;
		}

		internal Favorite ToFavorite()
		{
			return this.favorite;
		}
	}
}
