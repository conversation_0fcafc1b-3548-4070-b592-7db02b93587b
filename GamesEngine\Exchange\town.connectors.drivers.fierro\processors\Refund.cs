﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero;

namespace GamesEngine.Exchange.town.connectors.drivers.fierro.processors
{
    internal abstract class Refund : FierroProcessorDriver, IDriverUserProperties
    {
        public static TokenDriver CashierToken { get; set; }

        private const float VERSION = 1.0F;
        private HttpClient _postTransactionClient;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private int storeId;
        private string domainUrl;
        private string who;
        private string concept;
        private List<FragmentPaymentBody> payFragments;
        private string account;
        private int processorId;

        public Refund(string currencyCode) : base(TransactionType.Refund, VERSION, currencyCode)
        {
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            storeId = recordSet.Mappings["storeId"].AsInt;
            domainUrl = recordSet.Mappings["domainUrl"].AsString;
            who = recordSet.Mappings["who"].AsString;
            concept = recordSet.Mappings["concept"].AsString;
            payFragments = recordSet.Mappings["payFragments"].As<List<FragmentPaymentBody>>();
            account = recordSet.Mappings["account"].As<string>();
            processorId = recordSet.Mappings["processorId"].As<int>();

            string cahiserUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            if (_postTransactionClient == null || _postTransactionClient.BaseAddress.ToString() != cahiserUrl)
            {
                _postTransactionClient = new HttpClient();
                _postTransactionClient.BaseAddress = new Uri(cahiserUrl);
            }

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            var result = await PayRefundFragmentsAsync(now);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("domainUrl");
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("payFragments");
            CustomSettings.AddVariableParameter("account");
            CustomSettings.AddVariableParameter("processorId");

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        private async Task<RefundResponse> PayRefundFragmentsAsync(DateTime now)
        {
            string url = "/api/refund";
            var result = PayRefundAsync(url);
            return await result;
        }

        protected const int MAX_RETRIES = 5;

        private RefundResponse failedRefundResponse;
        private RefundResponse FAILED_REFUND_RESPONSE
        {
            get
            {
                if (failedRefundResponse == null)
                {
                    failedRefundResponse = new RefundResponse(this.Entity, this.PaymentMethod, this.CurrencyIsoCodes)
                    {
                        Code = RefundResponse.RefundResponseCode.RefundFail,
                        FragmentsWithProblems = new List<AuthorizationAndFragmentPair>()
                    };
                }
                return failedRefundResponse;
            }
        }

        private async Task<RefundResponse> PayRefundAsync(string url)
        {
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));

            if (string.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));
            if (string.IsNullOrWhiteSpace(concept)) throw new ArgumentNullException(nameof(concept));
            if (payFragments == null || payFragments.Count == 0) throw new ArgumentNullException(nameof(payFragments));

            var values = new RefundBody
            {
                StoreId = storeId,
                DomainUrl = domainUrl,
                Who = who,
                Concept = concept,
                FragmentPayments = payFragments,
                Account = account,
                ProcessorId = processorId
            };

            RefundResponse result = null;
            var jsonString = Commons.ToJson(values);

            int retryNumber = 0;
            string responseString = string.Empty;
            while (true)
            {
                try
                {
                    if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                    {
                        _postTransactionClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", CashierToken.access_token);
                    }

                    var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                    using (HttpResponseMessage response = await _postTransactionClient.PostAsync(url, content))
                    {
                        responseString = await response.Content.ReadAsStringAsync();
                        if ((int)response.StatusCode == 200)
                        {
                            var requesResult = JsonConvert.DeserializeObject<FragmentsWithProblemsResponse>(responseString);
                            RefundResponse.RefundResponseCode code = requesResult.FragmentsWithProblems.Any() ? RefundResponse.RefundResponseCode.OkWithProblems : RefundResponse.RefundResponseCode.OK;
                            result = new RefundResponse(this.Entity, this.PaymentMethod, this.CurrencyIsoCodes)
                            {
                                Code = code,
                                FragmentsWithProblems = requesResult.FragmentsWithProblems
                            };
                        }
                        else
                        {
                            result = FAILED_REFUND_RESPONSE;
                        }
                    }
                    break;
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    InternalOnError(nameof(PayRefundAsync), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}");

                    Thread.Sleep(5000);
                    if (retryNumber == MAX_RETRIES)
                    {
                        result = FAILED_REFUND_RESPONSE;
                        break;
                    }
                }
            }

            return result;
        }

    }
}
