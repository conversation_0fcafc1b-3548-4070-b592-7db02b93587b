﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;

namespace Connectors.town.connectors.drivers.hades
{
	public class RemoveTransaction : ASITenantDriver
	{
		private HttpClient _removeTransactionClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;
        public string CompanyBaseUrlServices { get; private set; }

		public RemoveTransaction() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("ticketNumber");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
			if (_removeTransactionClient==null)
			{
				_removeTransactionClient = new HttpClient
				{
					BaseAddress = new Uri(CompanyBaseUrlServices)
				};
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_removeTransactionClient = new HttpClient
					{
						BaseAddress = new Uri(CompanyBaseUrlServices)
					};
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			var TicketNumber = recordSet.Mappings["ticketNumber"];
			string ticketNumber = TicketNumber.AsString;
			Debug.WriteLine($"Accounting service {nameof(RemoveTransaction)} received {nameof(ticketNumber)}:{ticketNumber}");

			var result = await DeleteTransactionAsync(now, ticketNumber);
			return (T)Convert.ChangeType(result, typeof(T));

		}

		private async Task<bool> DeleteTransactionAsync(DateTime now, string documentNumber)
		{
			if (String.IsNullOrWhiteSpace(documentNumber)) throw new ArgumentNullException(nameof(documentNumber));

			var values = new RemoveTransactionBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				DocumentNumber = documentNumber
			};

			const string url = "/v1/5dimesAPI/RemoveTransaction";
			var jsonString = await ASIJsonUtils.ToJsonAsync(values);
			var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");

			string responseString = "";
			int retryNumber = 0;

			string valuesWithHiddenFields = await ASIJsonUtils.HideSensitiveDataAsync(values, Loggers.GetIntance().AccountingServicesASIRemoveTransaction);

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIRemoveTransaction.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var response = await _removeTransactionClient.PostAsync(url, httpContent);
					responseString = await response.Content.ReadAsStringAsync();

					Loggers.GetIntance().AccountingServicesASIRemoveTransaction.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIRemoveTransaction.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
					InternalOnError(nameof(DeleteTransactionAsync), retryNumber, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}");

					await Task.Delay(500);
					if (retryNumber == MAX_RETRIES) return false;
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(DeleteTransactionAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return false;
			}
			else
			{
				var errorResponse = Commons.FromJson<JsonErrorResponse>(responseString);
				if (errorResponse != null)
				{
					NotifyWarn(nameof(DeleteTransactionAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");
					return false;
				}
				else
				{
					var isSuccessful = Convert.ToBoolean(responseString);
					if (!isSuccessful) NotifyWarn(nameof(DeleteTransactionAsync), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(DeleteTransactionAsync)} failed for {nameof(documentNumber)} {documentNumber}");
					return isSuccessful;
				}
			}
		}
	}
}
