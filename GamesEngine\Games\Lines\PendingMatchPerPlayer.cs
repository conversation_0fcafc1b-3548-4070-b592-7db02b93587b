﻿using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class PendingMatchPerPlayer : Objeto
    {
        internal Game Game { get; }
        internal Domain Domain { get; }
        internal string AccountNumber { get; }
        internal int GameId => Game.Number;
        internal int TournamentId => Game.Tournament.Id;
        internal string TeamAName => Game.TeamA.Name;
        internal string TeamBName => Game.TeamB.Name;
        internal string SportName => Game.Sport.Name;
        internal string LeagueName => Game.Tournament.League.Name;
        internal DateTime StartDate => Game.ScheduledDate;
        internal int DomainId => Domain.Id;
        internal string DomainUrl => Domain.Url;
        Dictionary<int, Wager> wagers = new Dictionary<int, Wager>();
        PendingMatchesPerPlayers pendingMatchesPerPlayers;

        public PendingMatchPerPlayer(PendingMatchesPerPlayers pendingMatchesPerPlayers, Game game, Domain domain, string accountNumber)
        {
            if (pendingMatchesPerPlayers == null) throw new ArgumentNullException(nameof(pendingMatchesPerPlayers));
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            this.pendingMatchesPerPlayers = pendingMatchesPerPlayers;
            Game = game;
            Domain = domain;
            AccountNumber = accountNumber;
        }

        internal void Add(IEnumerable<Wager> paramWagers)
        {
            if (!paramWagers.Any()) throw new GameEngineException($"{nameof(paramWagers)} cannot be empty");
            if (paramWagers.Any(wager => wager.Line.Game != Game || wager.AccountNumber != AccountNumber || wager.Order.Domain != Domain)) throw new GameEngineException($"Any {nameof(Wager)} belong to different {nameof(AccountNumber)} '{AccountNumber}' and {nameof(Domain)} '{Domain.Url}'");

            foreach (var wager in paramWagers)
            {
                Add(wager);
            }
        }

        internal void Add(Wager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (!this.wagers.ContainsKey(wager.Id)) this.wagers.Add(wager.Id, wager);

            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin() : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
            pendingMatchesPerPlayers.UpdateTotals(wager);
        }

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
