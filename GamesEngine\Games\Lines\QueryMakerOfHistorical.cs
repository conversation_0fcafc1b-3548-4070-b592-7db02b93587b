﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using GamesEngine.Business;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;

namespace GamesEngine.Games.Lines
{
    class QueryMakerOfHistorical
    {
        private readonly LinesDBHandler linesDBHandler;

        internal QueryMakerOfHistorical()
        {
            if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
            {
                linesDBHandler = new LinesDBHandlerMySQL(Integration.Db.MySQL);
            }
            else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
            {
                linesDBHandler = new LinesDBHandlerSQLServer(Integration.Db.SQLServer);
            }
            else
            {
#if DEBUG
                linesDBHandler = new LinesDBHandlerInMemory();
#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
            }
        }

        internal CompletedMatches LastPlayedGamesOfPlayer(string accountNumber)
        {
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.LastPlayedGamesOfPlayer(accountNumber);
            return result;
        }

        internal CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, 
            string accountNumber, string authorizationId, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedMatchesReport(startDate, endDate, sport, league, tournamentId, gameId, lineType, gradingStatus, accountNumber, authorizationId, domainIds);
            return result;
        }

        internal CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, 
            char gradingStatus, string accountNumber, string authorizationId, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedMatchesPerPlayersReport(startDate, endDate, sport, league, tournamentId, gameId, lineType, gradingStatus, accountNumber, authorizationId, domainIds);
            return result;
        }

        internal CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, 
            string accountNumber, string authorizationId, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedWagersPerMatchesReport(startDate, endDate, sport, league, tournamentId, gameId, lineType, gradingStatus, accountNumber, authorizationId, domainIds);
            return result;
        }

        internal CompletedWagers GenerateCompletedWinnerWagersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, string accountNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedWagersReport(startDate, endDate, sport, league, tournamentId, gameId, accountNumber, domainIds);
            return result;
        }

        internal CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedLinesReport(startDate, endDate, sport, league, tournamentId, gameId);
            return result;
        }

        internal CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = linesDBHandler.GenerateCompletedMatchesPerLineTypesReport(startDate, endDate, sport, league, tournamentId, gameId, lineType, domainIds);
            return result;
        }

        internal CompletedWagers WinnerWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.WinnerWagersOfPlayerBetween(startedDate, endedDate, accountNumber);
            return result;
        }

        internal CompletedWagers LoserWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.LoserWagersOfPlayerBetween(startedDate, endedDate, accountNumber);
            return result;
        }

        internal CompletedWagers NoActionWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
        {
            if (startedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endedDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.NoActionWagersOfPlayerBetween(startedDate, endedDate, accountNumber);
            return result;
        }

        internal CompletedWagers WinnerWagersOfGame(int gameId, string accountNumber)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.WinnerWagersOfGame(gameId, accountNumber);
            return result;
        }

        internal CompletedWagers LoserWagersOfGame(int gameId, string accountNumber)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.LoserWagersOfGame(gameId, accountNumber);
            return result;
        }

        internal CompletedWagers NoActionWagersOfGame(int gameId, string accountNumber)
        {
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = linesDBHandler.NoActionWagersOfGame(gameId, accountNumber);
            return result;
        }

        abstract class LinesDBHandler
        {
            protected readonly string connectionString;
            protected const string TABLE_LOSERS = Games.Lines.ReceiverOfHistorical.TABLE_WAGERS_LOSERS;
            protected const string TABLE_WINNERS = Games.Lines.ReceiverOfHistorical.TABLE_WAGERS_WINNERS;
            protected const string TABLE_NOACTIONS = Games.Lines.ReceiverOfHistorical.TABLE_WAGERS_NOACTIONS;
            protected const string TABLE_LINES_SCORES = Games.Lines.ReceiverOfHistorical.TABLE_LINES_SCORES;
            protected const string TABLE_LINES_VERSIONS = Games.Lines.ReceiverOfHistorical.TABLE_LINES_VERSIONS;

            protected const int MAXIMUM_NUMBER_OF_ROWS_TO_INSERT = 1000;
            protected const int MINIMUM_SET_OF_ROWS_TO_INSERT = 1;

            protected const string COMMON_SELECT_FOR_WAGERS_QUERIES = @"SELECT WW.tournament, WW.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport, 
linetype, LV.line, text, teamareward, teambreward, tiereward, spread, abanswer, yesreward, noreward, yesnoanswer, overreward, underreward, overunderanswer, fixedoptions, fixedrewards, fixedanswer,
authorizationid, accountnumber, towin, risk, WW.gradingstatus, chosenanswer, fixedchosenanswer";
            protected const string COMMON_SELECT_FOR_LINES_QUERIES = @"SELECT LS.tournament, LS.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport, gradedby, gradedate,
linetype, LV.line, text, teamareward, teambreward, tiereward, spread, abanswer, yesreward, noreward, yesnoanswer, overreward, underreward, overunderanswer, fixedoptions, fixedrewards, fixedanswer";
            protected const string COMMON_SELECT_FOR_MATCHES_QUERIES = @"SELECT WW.tournament, WW.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport, teamashortname, teambshortname";
            private readonly Company company;

            protected LinesDBHandler(string connectionString)
            {
                if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
                this.company = null;
                this.connectionString = connectionString;
            }

            protected Company Company
            {
                get
                {
                    return this.company;
                }
            }

            protected string DateToString(DateTime date)
            {
                string result = date.ToString("yyyy-MM-dd");
                return result;
            }

            protected abstract string DateTimeToString(DateTime date);
            internal abstract CompletedMatches LastPlayedGamesOfPlayer(string accountNumber);
            internal abstract CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds);
            internal abstract CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds);
            internal abstract CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds);
            internal abstract CompletedWagers GenerateCompletedWagersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, string accountNumber, string domainIds);
            internal abstract CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId);
            internal abstract CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds);
            internal abstract CompletedWagers WinnerWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);
            internal abstract CompletedWagers LoserWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);
            internal abstract CompletedWagers NoActionWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber);
            internal abstract CompletedWagers WinnerWagersOfGame(int gameId, string accountNumber);
            internal abstract CompletedWagers LoserWagersOfGame(int gameId, string accountNumber);
            internal abstract CompletedWagers NoActionWagersOfGame(int gameId, string accountNumber);
            protected abstract CompletedWagers GetWagers(string command);
            protected abstract CompletedLines GetLines(string command);
            protected abstract CompletedWagers GetWinnerWagers(string command);
            protected abstract CompletedMatches GetMatches(string command);
            protected abstract CompletedMatches GetMatchesFromWagers(string command);
            protected abstract CompletedMatchesPerPlayers GetMatchesPerPlayersFromWagers(string command);
            protected abstract CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command);
        }

        private class LinesDBHandlerMySQL : LinesDBHandler
        {
            internal LinesDBHandlerMySQL(string connectionString) : base(connectionString)
            {
                if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LINES_SCORES}.");
            }

            private bool ExistTable()
            {
                bool exists = true;
                string sql = "SELECT 1 FROM " + TABLE_LINES_SCORES + " LIMIT 1";
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new MySqlCommand(sql, connection))
                    {
                        try
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                        catch
                        {
                            exists = false;
                        }
                    }
                    connection.Close();
                }
                return exists;
            }

            protected override string DateTimeToString(DateTime date)
            {
                string result = date.ToString("yyyy-MM-dd HH:mm:ss");
                return result;
            }

            internal override CompletedMatches LastPlayedGamesOfPlayer(string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                UNION
                                ({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                UNION
                                ({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                ORDER BY startgamedate DESC
                                LIMIT 10 
                                ";
                var result = GetMatches(command);
                return result;
            }

            internal override CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";
                
                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'X'
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                ";
                var result = GetMatchesFromWagers(command);
                return result;
            }

            internal override CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'X'
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                ";
                var result = GetMatchesPerPlayersFromWagers(command);
                return result;
            }

            internal override CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'X'
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers GenerateCompletedWagersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, string accountNumber, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWinnerWagers(command);
                return result;
            }

            internal override CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND LS.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND LS.game = {gameId}";

                var command = $@"{COMMON_SELECT_FOR_LINES_QUERIES}
                                FROM {TABLE_LINES_SCORES} LS 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON LS.tournament = LV.tournament AND LS.game = LV.game 
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND LS.timestamp = 0 AND LV.timestamp = 0 
                                {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter}
                                ORDER BY startgamedate
                ";
                var result = GetLines(command);
                return result;
            }

            internal override CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} 
                                )
                                ";
                var result = GetMatchesPerLineTypesFromWagers(command);
                return result;
            }

            internal override CompletedWagers WinnerWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers LoserWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers NoActionWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'X'
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers WinnerWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers LoserWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers NoActionWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}, 'X'
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            protected override CompletedWagers GetWagers(string command)
            {
                var wagers = new CompletedWagers();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            CompletedWager wager;
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (! games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }
                                
                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (! lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                wagers.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return wagers;
            }

            protected override CompletedWagers GetWinnerWagers(string command)
            {
                var wagers = new CompletedWagers();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            CompletedWager wager;
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                wagers.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return wagers;
            }

            protected override CompletedLines GetLines(string command)
            {
                var result = new CompletedLines();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    scoreGame.GradedBy = reader.GetString(9);
                                    scoreGame.GradedDate = reader.GetDateTime(10);
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(11);
                                var lineId = reader.GetInt32(12);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18),
                                            spread: reader.GetDecimal(17)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(21);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(13),
                                                yesReward: reader.GetInt16(19),
                                                noReward: reader.GetInt16(20),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(24);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(13),
                                                overReward: reader.GetInt16(22),
                                                underReward: reader.GetInt16(23),
                                                score: reader.GetDecimal(17),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(13),
                                            options: reader.GetString(25),
                                            rewards: reader.GetString(26),
                                            realAnswer: reader.GetString(27)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(24);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(22),
                                                underReward: reader.GetInt16(23),
                                                score: reader.GetDecimal(17),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18),
                                            tieReward: reader.GetInt16(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }
                                result.Add(line);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatches GetMatchesFromWagers(string command)
            {
                var matches = new CompletedMatches();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                    matches.Add(scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                line.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return matches;
            }

            protected override CompletedMatchesPerPlayers GetMatchesPerPlayersFromWagers(string command)
            {
                CompletedMatchesPerPlayers result = new CompletedMatchesPerPlayers();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerPlayer matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, wager.AccountNumber, wager.DomainId)) 
                                {
                                    matchPerPlayer = new CompletedMatchPerPlayer(result, scoreGame, wager.AccountNumber, wager.DomainId);
                                    result.Add(matchPerPlayer); 
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, wager.AccountNumber, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command)
            {
                var result = new CompletedMatchesPerLineTypes();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerLineType matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, line.LineType, wager.DomainId))
                                {
                                    matchPerPlayer = new CompletedMatchPerLineType(result, scoreGame, line.LineType, wager.DomainId);
                                    result.Add(matchPerPlayer);
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, line.LineType, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatches GetMatches(string command)
            {
                var matches = new CompletedMatches();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: reader.GetInt32(0),
                                        gameId: reader.GetInt32(1),
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                scoreGame.TeamAShortName = reader.GetString(9);
                                scoreGame.TeamBShortName = reader.GetString(10);
                                matches.Add(scoreGame);
                            }
                        }
                    }
                    connection.Close();
                }
                return matches;
            }
        }

        private class LinesDBHandlerSQLServer : LinesDBHandler
        {
            internal LinesDBHandlerSQLServer(string connectionString) : base(connectionString)
            {
                if (!ExistTable()) throw new GameEngineException($"There is no table {TABLE_LINES_SCORES}.");
            }

            private bool ExistTable()
            {
                bool exists = false;
                StringBuilder statement = new StringBuilder();

                statement.Append("IF EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '" + TABLE_LINES_SCORES + "')")
                    .Append("SELECT 1 ELSE SELECT 0;");
                string sql = statement.ToString();

                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(sql, connection))
                    {
                        try
                        {
                            var result = (int)command.ExecuteScalar();
                            exists = result == 1;
                        }
                        catch
                        {
                            exists = false;
                        }
                    }
                    connection.Close();
                }
                return exists;
            }

            protected override string DateTimeToString(DateTime date)
            {
                string result = date.ToString("yyyy-MM-dd HH:mm:ss.fff", Integration.CultureInfoEnUS);
                return result;
            }

            internal override CompletedMatches LastPlayedGamesOfPlayer(string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"SELECT TOP 10 
                                ({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                UNION
                                ({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                UNION
                                ({COMMON_SELECT_FOR_MATCHES_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.timestamp = 0 {accountNumberFilter})
                                ORDER BY startgamedate DESC
                                ";
                var result = GetMatches(command);
                return result;
            }

            internal override CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )";
                var result = GetMatchesFromWagers(command);
                return result;
            }

            internal override CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                ";
                var result = GetMatchesPerPlayersFromWagers(command);
                return result;
            }

            internal override CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var gradingStatusFilter = gradingStatus == char.MinValue ? string.Empty : $" AND LV.gradingstatus = '{gradingStatus}'";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var authorizationIdFilter = string.IsNullOrWhiteSpace(authorizationId) ? string.Empty : $" AND authorizationId ='{authorizationId}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} {gradingStatusFilter} {accountNumberFilter} {authorizationIdFilter} 
                                )
                                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers GenerateCompletedWagersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, string accountNumber, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWinnerWagers(command);
                return result;
            }

            internal override CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND LS.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND LS.game = {gameId}";

                var command = $@"{COMMON_SELECT_FOR_LINES_QUERIES}
                                FROM {TABLE_LINES_SCORES} LS 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON LS.tournament = LV.tournament AND LS.game = LV.game 
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND LS.timestamp = 0 AND LV.timestamp = 0 
                                {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter}
                                ORDER BY startgamedate
                ";
                var result = GetLines(command);
                return result;
            }

            internal override CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line
                                WHERE DATE(startgamedate)>='{DateToString(startDate)}' AND DATE(startgamedate)<='{DateToString(endDate)}' AND WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter} 
                                )
                                ";
                var result = GetMatchesPerLineTypesFromWagers(command);
                return result;
            }

            internal override CompletedWagers WinnerWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers LoserWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers NoActionWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 
                                {accountNumberFilter}
                                ORDER BY startgamedate
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers WinnerWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_WINNERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers LoserWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_LOSERS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            internal override CompletedWagers NoActionWagersOfGame(int gameId, string accountNumber)
            {
                var accountNumberFilter = string.IsNullOrWhiteSpace(accountNumber) ? string.Empty : $" AND accountnumber ='{accountNumber.ToUpper()}'";

                var command = $@"{COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {TABLE_NOACTIONS} WW
                                INNER JOIN {TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion
                                WHERE LS.timestamp = 0 AND LV.timestamp = 0 AND WW.timestamp = 0 AND WW.game = {gameId}
                                {accountNumberFilter}
                                ORDER BY WW.tournament, WW.game, WW.line
                ";
                var result = GetWagers(command);
                return result;
            }

            protected override CompletedWagers GetWagers(string command)
            {
                var wagers = new CompletedWagers();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                wagers.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return wagers;
            }

            protected override CompletedWagers GetWinnerWagers(string command)
            {
                var wagers = new CompletedWagers();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            CompletedWager wager;
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                wagers.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return wagers;
            }

            protected override CompletedLines GetLines(string command)
            {
                var result = new CompletedLines();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    scoreGame.GradedBy = reader.GetString(9);
                                    scoreGame.GradedDate = reader.GetDateTime(10);
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(11);
                                var lineId = reader.GetInt32(12);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18),
                                            spread: reader.GetDecimal(17)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(21);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(13),
                                                yesReward: reader.GetInt16(19),
                                                noReward: reader.GetInt16(20),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(24);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(13),
                                                overReward: reader.GetInt16(22),
                                                underReward: reader.GetInt16(23),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(13),
                                            options: reader.GetString(25),
                                            rewards: reader.GetString(26),
                                            realAnswer: reader.GetString(27)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(24);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(22),
                                                underReward: reader.GetInt16(23),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(14),
                                            teamBReward: reader.GetInt16(15),
                                            realAnswer: reader.GetChar(18),
                                            tieReward: reader.GetInt16(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }
                                result.Add(line);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatches GetMatchesFromWagers(string command)
            {
                var matches = new CompletedMatches();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                    matches.Add(scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                            scoreGame.Add(line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                line.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return matches;
            }

            protected override CompletedMatchesPerPlayers GetMatchesPerPlayersFromWagers(string command)
            {
                CompletedMatchesPerPlayers result = new CompletedMatchesPerPlayers();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerPlayer matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, wager.AccountNumber, wager.DomainId))
                                {
                                    matchPerPlayer = new CompletedMatchPerPlayer(result, scoreGame, wager.AccountNumber, wager.DomainId);
                                    result.Add(matchPerPlayer);
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, wager.AccountNumber, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command)
            {
                var result = new CompletedMatchesPerLineTypes();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: reader.GetDecimal(15),
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) || reader.GetChar(31)=='-' ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerLineType matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, line.LineType, wager.DomainId))
                                {
                                    matchPerPlayer = new CompletedMatchPerLineType(result, scoreGame, line.LineType, wager.DomainId);
                                    result.Add(matchPerPlayer);
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, line.LineType, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override CompletedMatches GetMatches(string command)
            {
                var matches = new CompletedMatches();
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new SqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: reader.GetInt32(0),
                                        gameId: reader.GetInt32(1),
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                scoreGame.TeamAShortName = reader.GetString(9);
                                scoreGame.TeamBShortName = reader.GetString(10);
                                matches.Add(scoreGame);
                            }
                        }
                    }
                    connection.Close();
                }
                return matches;
            }
        }

        private class LinesDBHandlerInMemory : LinesDBHandler
        {
            internal LinesDBHandlerInMemory() : base("-")
            {
            }

            protected override string DateTimeToString(DateTime date)
            {
                throw new NotImplementedException();
            }

            internal override CompletedMatches LastPlayedGamesOfPlayer(string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedMatches GenerateCompletedMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                throw new NotImplementedException();
            }

            internal override CompletedMatchesPerPlayers GenerateCompletedMatchesPerPlayersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers GenerateCompletedWagersPerMatchesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, char gradingStatus, string accountNumber, string authorizationId, string domainIds)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers GenerateCompletedWagersReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, string accountNumber, string domainIds)
            {
                throw new NotImplementedException();
            }

            internal override CompletedLines GenerateCompletedLinesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId)
            {
                throw new NotImplementedException();
            }

            internal override CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypesReport(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
            {
                throw new NotImplementedException();
            }

            protected override CompletedWagers GetWagers(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedWagers GetWinnerWagers(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedLines GetLines(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedMatches GetMatches(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedMatches GetMatchesFromWagers(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedMatchesPerPlayers GetMatchesPerPlayersFromWagers(string command)
            {
                throw new NotImplementedException();
            }

            protected override CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers WinnerWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers LoserWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers NoActionWagersOfPlayerBetween(DateTime startedDate, DateTime endedDate, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers WinnerWagersOfGame(int gameId, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers LoserWagersOfGame(int gameId, string accountNumber)
            {
                throw new NotImplementedException();
            }

            internal override CompletedWagers NoActionWagersOfGame(int gameId, string accountNumber)
            {
                throw new NotImplementedException();
            }
        }
    }
}
