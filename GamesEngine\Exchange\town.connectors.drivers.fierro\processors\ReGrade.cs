﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.fiero;

namespace GamesEngine.Exchange.town.connectors.drivers.fierro.processors
{
    public abstract class ReGrade : FierroProcessorDriver,IDriverUserProperties
    {
        public static TokenDriver CashierToken { get; set; }
        private RestClient _postClient;
        public string CashierUrl { get; private set; }
        private const float VERSION = 1.0F;


        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public ReGrade(string currencyCode) : base(TransactionType.UpdatePayPrize, VERSION, currencyCode)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {


            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            if (_postClient == null)
            {
                _postClient = new RestClient(CashierUrl);
            }

            
            var result = PayFragments(now, recordSet);//Cashier

			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("processorId");
            CustomSettings.AddVariableParameter("processorKey");
            CustomSettings.AddVariableParameter("updateFragments");
            CustomSettings.AddVariableParameter("gradeFreeFormWagers");
            //CustomSettings.Prepare();


            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }


        public GradeFreeFormWagersResponse PayFragments(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var who = recordSet.Mappings["who"].AsString;
            var concept = recordSet.Mappings["concept"].AsString;
            var storeId = recordSet.Mappings["storeId"].AsInt;
            var processorId = recordSet.Mappings["processorId"].AsInt;
            var gradeFreeFormWagers = recordSet.Mappings["gradeFreeFormWagers"].As<List<PayFragmentsWithAtAddressMessage>>();
            var processorKey = recordSet.Mappings["processorKey"].AsString;

            var payBody = new PayBody()
            {
                StoreId = storeId,
                Concept = concept,
                Who = who,
                GradeFreeFormWagers = gradeFreeFormWagers,
                ProcessorKey = processorKey,
                ProcessorId = processorId,

            };

            var result = Pay(payBody);
            return result;
        }


        private GradeFreeFormWagersResponse Pay(PayBody frgamentBody)
        {
            GradeFreeFormWagersResponse payResponse;
            try
            {
                var url = $"api/pay";
                var jsonString = Commons.ToJson(frgamentBody);

                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                }
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
                string responseString = "";
                var resultFromCashier = _postClient.Execute(request);
                responseString = resultFromCashier.Content;
                string body = string.Empty;
                if (resultFromCashier.StatusCode == HttpStatusCode.OK)
                {
                    payResponse = JsonConvert.DeserializeObject<GradeFreeFormWagersResponse>(responseString);

                }
                else
                {
                    payResponse = new GradeFreeFormWagersResponse();
                    payResponse.Error = new ErrorResponse() { Code = resultFromCashier.StatusCode.ToString(), Message = responseString };
                    ErrorsSender.Send(body, $@"Pay fragment {responseString} fails.");
                }

            }
            catch (Exception e)
            {
                payResponse = new GradeFreeFormWagersResponse();
                payResponse.Error = new ErrorResponse() { Code = String.Empty, Message = e.ToString() };
                ErrorsSender.Send(e);

            }

            return payResponse;
        }
    }
    
}
