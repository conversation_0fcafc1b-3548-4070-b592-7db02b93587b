﻿using GamesEngine.Business.Liquidity.Sentinels;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity
{
    internal class LiquidFlow : Objeto
    {
        private static LiquidFlow instance;

        private readonly Dictionary<string, Liquid> _liquids = new();

        internal RiskProfilesDeposits RiskProfiles { get; } 

        private LiquidFlow(string kind) => Kind = kind;

        private LiquidFlow(IEnumerable<Domain> allDomains) 
        {
            RiskProfiles = new RiskProfilesDeposits(allDomains);
        }

#if DEBUG
        internal static void ClearInstance()
        {
            instance = null;
        }
#endif
        internal static LiquidFlow Instance(IEnumerable<Domain> allDomains)
        {
            if (instance == null) instance = new LiquidFlow(allDomains);
            return instance;
        }

        internal bool ExistInstance(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new GameEngineException(nameof(kind));
            return _liquids.ContainsKey(kind);
        }

        internal string Kind { get; }

        internal Decimal Amount { get; }
        internal decimal AmountTo(DateTime date)
        {
            throw new NotImplementedException();
        }

        internal void LiquidityPlan(DateTime from, DateTime to)
        {
            throw new NotImplementedException();
        }

        internal IEnumerable<Source> Sources => throw new NotImplementedException(); //_liquids.Values.SelectMany(l => l.Sources).ToList();

        internal bool ExistXpub(string xpub)
        {
            if(string.IsNullOrWhiteSpace(xpub)) throw new GameEngineException(nameof(xpub));

            foreach (var liquid in _liquids.Values)
            {
                if (liquid.ExistXpub(xpub)) return true;
            }
            return false;
        }

        internal Liquid GetOrCreateLiquid(bool itIsThePresent, DateTime now, string sourceName)
        {
            if (now == DateTime.MinValue) throw new GameEngineException(nameof(now));
            if (string.IsNullOrWhiteSpace(sourceName)) throw new GameEngineException(nameof(sourceName));

            bool hasKindCoin = Coinage.All.Any(c => c.Iso4217Code == sourceName);
            if (!hasKindCoin) throw new GameEngineException($"The source name: {sourceName} is not a valid coin kind.");

            if (!_liquids.TryGetValue(sourceName, out var liquid))
            {
                var sentinelForLiquid = IngressSentinel.SentinelByKind(sourceName);
                liquid = new Liquid(sourceName, this, sentinelForLiquid);
                _liquids[sourceName] = liquid;

                if (Integration.UseKafka)
                {

                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        CreatedLiquidMessage tankMergedMessage = new CreatedLiquidMessage(sourceName, now);
                        buffer.Send(tankMergedMessage);
                    }
                }

                if (itIsThePresent)
                {
                    CreatedLiquidEvent createdTankEvent = new CreatedLiquidEvent(sourceName, now);
                    PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
                }

            }
            return liquid;
        }
    }
}
