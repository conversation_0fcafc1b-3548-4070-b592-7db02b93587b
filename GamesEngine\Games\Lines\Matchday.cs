﻿using GamesEngine.Games.Tournaments;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lines
{
    using Games = GamesEngine.Games.Tournaments.Games;
    [Puppet]
    class Matchday : Objeto, IDay
    {
        private readonly DateTime day;
        private readonly Betboard betBoard;
        private readonly Games games;
        private readonly Dictionary<Game, Showcase> showcase;

        internal Matchday(Betboard betBoard, DateTime day)
        {
            if (day == default(DateTime)) throw new ArgumentNullException(nameof(day));
            if (betBoard == null) throw new ArgumentNullException(nameof(betBoard));
            this.day = day;
            this.betBoard = betBoard;
            this.games = new FirstGames();
            this.showcase = new Dictionary<Game, Showcase>();
        }

        internal DateTime Day
        {
            get
            {
                return this.day;
            }
        }

        DateTime IDay.Day => Day;

        internal bool Contains(Game game)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (game.HasScheduledDate() && this.day != game.ScheduledDate.Date) return false;
            var result = games.Contains(game);
            return result;
        }

        private void AddGame(Game game, Team home, Showcase newShowcase)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (game.Sport != this.betBoard.Tournament.Sport) throw new GameEngineException($"Tournament sport is {this.betBoard.Tournament.Sport.Name} that does not match with sport {game.Sport.Name} that is the game been included.");
            if (games != null && games.Contains(game)) throw new GameEngineException($"MatchDay {this.day.ToShortDateString()} already contains this game {game.ToString()}");
            if (home == null) throw new ArgumentNullException(nameof(home));
            if (newShowcase == null) throw new ArgumentNullException(nameof(newShowcase));
            //TODO Revisar el uso de fecha calendarizada y fecha de inicio para ver si lo metemos adentro de un objeto llamado como HoraDelEvento

            foreach (var g in games.List())
            {
                if (g.TeamA == game.TeamA || g.TeamA == game.TeamB) throw new GameEngineException($"{g.TeamA.Name} has already another game scheduled on {this.day.ToShortDateString()}");
                if (g.TeamB == game.TeamA || g.TeamB == game.TeamB) throw new GameEngineException($"{g.TeamB.Name} has already another game scheduled on {this.day.ToShortDateString()}");
            }

            games.Add(game);
            games.SortByDateAndHour();
            //TODO definir del juego quien es casa y quien es visita, ademas quien es favorito, puede tener o no tener favorito, a veces los 2 pueden ser favoritos
            showcase.Add(game, newShowcase);
        }

        internal void AddGame(Game game, Team home, Team favorite)
		{
            //TODO El new Showcase seguro hay que guardarlo, pero todavia no ha habido necesidad
            AddGame(game, home, new Showcase(this, game, home, favorite));
        }

        internal void AddGame(Game game, Team home, Team favorite1, Team favorite2)
		{
            if (favorite1 == favorite2) throw new GameEngineException($"Both teams are favorite, they can not be the same {favorite1.Name}");

            AddGame(game, home, new Showcase(this, game, home, favorite1, favorite2));
        }

        internal IEnumerable<Game> Games
        { 
            get
            {
                return games.List();
            }
        }

        internal Betboard Betboard
		{
			get
			{
                return this.betBoard;
			}
		}

        internal Showcase GetShowcase(Game game)
		{
            return this[game];
		}

        internal Showcase this[Game game]
        {
			get
			{
                if (game == null) throw new ArgumentNullException(nameof(game));
                if (! games.Contains(game)) throw new GameEngineException($"Tournament {this.betBoard.Tournament.Description} does not have a game {game.ToString()}");

                Showcase result;
                if (!showcase.TryGetValue(game, out result)) throw new GameEngineException($"The game has not be considered as part of the journey");
                
                return result;
            }
        }

        internal IEnumerable<Line> AllLines()
        {
            var result = Enumerable.Empty<Line>();
            foreach (var showcase in showcase.Values)
            {
                result = result.Concat(showcase.LastVersionLines());
            }
            return result.ToList();
        }

        internal IEnumerable<Line> LinesBasedOn(IEnumerable<Question> questions)
        {
            var result = Enumerable.Empty<Line>(); 
            foreach (var showcase in showcase.Values)
            {
                result = result.Concat(showcase.LinesBasedOn(questions));
            }
            return result.ToList();
        }

        internal IEnumerable<Line> LinesBasedOn(Visibility visibility)
        {
            var result = Enumerable.Empty<Line>();
            foreach (var showcase in showcase.Values)
            {
                switch (visibility)
                {
                    case Visibility.DRAFT:
                        result = result.Concat(showcase.DraftLinesOffered());
                        break;
                    case Visibility.SUSPENDED:
                        result = result.Concat(showcase.SuspendedLinesOffered());
                        break;
                    case Visibility.PUBLISHED:
                        result = result.Concat(showcase.PublishedLinesOffered());
                        break;
                    case Visibility.CANCELED:
                        result = result.Concat(showcase.CanceledLines());
                        break;
                    default:
                        throw new GameEngineException($"{nameof(visibility)} '{visibility}' is not valid.");
                }
                
            }
            return result.ToList();
        }
    }
}
