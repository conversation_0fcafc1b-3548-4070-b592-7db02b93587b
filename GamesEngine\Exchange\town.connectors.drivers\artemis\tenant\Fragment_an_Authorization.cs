﻿using Connectors.town.connectors.commons;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Settings;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using static town.connectors.drivers.Result;


namespace Connectors.town.connectors.drivers.artemis
{
    public abstract class Fragment_an_Authorization : DGSTenantDriver, IFragmentDriver
    {
        private RestClient _postInsertWagersClient;

        private int _amountOfWagerPerChunk = 500;
        public int AmountOfWagersPerChunk
        {
            get
            {
                return _amountOfWagerPerChunk;
            }
            set
            {
                if (value < 1)
                {
                    throw new Exception($@"_amountOfWagerPerChunk must be greater than 1. ");
                }
                _amountOfWagerPerChunk = value;
            }
        }

        public Fragment_an_Authorization(string currencyCode)
            : base(Tenant_Actions.Fragment, TransactionType.Authorization, currencyCode)
        {
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

            CustomSettings.AddVariableParameter("currencyCode");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("fragmentInformation");
            CustomSettings.AddVariableParameter("referenceNumber");
            CustomSettings.AddVariableParameter("useless");
            CustomSettings.AddVariableParameter("context");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;

            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
            {
                _ = Task.Run(async () =>
                {
                    await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                });
            }
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (_postInsertWagersClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postInsertWagersClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        _ = Task.Run(async () =>
                        {
                            await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                        });
                    }

                    _postInsertWagersClient = new RestClient(ServicesUrl);
                }
            }

            var result = await RegisterWagersAsync(now, recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        public Task<InsertWagersResponse> RegisterWagersAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var CustomerId = recordSet.Mappings["accountNumber"];
            var Amount = recordSet.Mappings["amount"];
            var Description = recordSet.Mappings["description"];
            var currencyCode = recordSet.Mappings["currencyCode"].AsString;
            var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

            string customerId = CustomerId.AsString;
            decimal amount = Amount.AsDecimal;
            string description = Description.AsString;
            var toWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ToWinByDrawAndNumber>>();

            var artemisTickets = new List<ArtemisToWinByDrawAndNumber>();
            foreach (var ticket in toWinsByDrawAndNumber)
            {
                artemisTickets.Add(new ArtemisToWinByDrawAndNumber()
                {
                    description = ticket.description,
                    draw = ticket.draw,
                    drawDate = ticket.drawDate,
                    drawHour = ticket.drawHour,
                    number = ticket.number,
                    pick = ticket.pick,
                    risk = ticket.risk,
                    status = ticket.status,
                    ticketId = ticket.ticketId,
                    toWin = ticket.toWin,
                    type = ticket.type,
                    freePlay = currencyCode == "FP"
                });
            }

            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(RegisterWagersAsync)}");
            if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));

            var result = InsertWagersAsync(customerId, amount, description, artemisTickets);
            return result;
        }

        protected static string URL = "/InsertWager";
        private async Task<InsertWagersResponse> InsertWagersAsync(string customerId, decimal amount, string description, List<ArtemisToWinByDrawAndNumber> toWinsByDrawAndNumber)
        {
            if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
            if (String.IsNullOrWhiteSpace(description)) throw new ArgumentNullException(nameof(description));
            if (amount <= 0) throw new Exception($"{nameof(amount)} {amount} is not valid to send request {nameof(InsertWagersAsync)}");
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            var values = new DebitTransactionBody()
            {
                idPlayer = customerId,
                amount = amount,
                description = description,
                tickets = toWinsByDrawAndNumber
            };
            
            var jsonString = Commons.ToJson(values);

            string responseString = "";
            string valuesWithHiddenFields = $@"idPlayer:{customerId}, description:{description}, amount:{amount}";
            RestResponse response;
            var responseObj = new InsertWagersResponse();
            responseObj.idTransaction = FAKE_DOCUMENT_NUMBER;
            responseObj.Url = URL;
            try
            {
                var request = new RestRequest(URL, Method.Post);
                request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                response = await _postInsertWagersClient.ExecuteAsync(request);

                responseString = response.Content;
                responseObj.Response = responseString;
            }
            catch (Exception e)
            {
                var extraErrorMessage = string.Empty;
                if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";
                InternalOnError(nameof(InsertWagersAsync), 1, e, $"Url:{URL}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);
                return responseObj;
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                responseObj.Code = AuthorizationResponseCode.AuthorizationFail;
            }
            else if (response.StatusCode == HttpStatusCode.InternalServerError || response.StatusCode == HttpStatusCode.RequestTimeout)
            {
                responseObj.Code = AuthorizationResponseCode.AuthorizationFail;
            }
            else if (response.StatusCode == HttpStatusCode.BadRequest)
            {
                try
                {
                    var deserializedResponseObj = Commons.FromJson<InsertWagersResponse>(responseString);
                    if (deserializedResponseObj == null) responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                    else
                    {
                        responseObj = deserializedResponseObj;
                        responseObj.idTransaction = FAKE_DOCUMENT_NUMBER;
                        var code = responseObj.code.Equals("INVALID_FUNDS") ?
                            responseObj.Code = AuthorizationResponseCode.InsufficientFunds :
                            responseObj.Code = AuthorizationResponseCode.AuthorizationFail;
                    }
                }
                catch (Exception e)
                {
                    APMHelper.CaptureException(e);
                    responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                }
            }
            else
            {
                try
                {
                    var deserializedResponseObj = Commons.FromJson<InsertWagersResponse>(responseString);
                    if (deserializedResponseObj == null) responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                    else
                    {
                        responseObj = deserializedResponseObj;
                        responseObj.Code = AuthorizationResponseCode.OK;
                    }
                }
                catch (Exception e)
                {
                    APMHelper.CaptureException(e);
                    responseObj.Code = AuthorizationResponseCode.UnexpectedFormat;
                }
            }

            return responseObj;
        }

        protected static InsertWagersResponse FAIL_DEFAULT_RESPONSE = new InsertWagersResponse()
        {
            idTransaction = FAKE_DOCUMENT_NUMBER,
            Code = AuthorizationResponseCode.AuthorizationFail,
            Response = "Failed to send request",
            Url = URL
        };


        bool IsValidDateWithoutTime(string strDate)
        {
            DateTime date;
            var result = DateTime.TryParseExact(strDate, "M/d/yyyy", new CultureInfo("en-US"), DateTimeStyles.None, out date) && date.Hour == 0 && date.Minute == 0 && date.Second == 0;
            return result;
        }

        public struct FragmentResponse : IDepositResponse
        {
            public int AuthorizationId { get; }
            public string ProcessorKey { get; }
            public int EntityId { get; }
            public int PaymentMethodId { get; }
            public TransactionStatus Status { get; }
            public InsertWagersResponse Response { get; set; }
            public string Message { get; }
            public AuthorizationResponseCode Code { get; }

            public FragmentResponse(int authorizationId, TransactionStatus status, AuthorizationResponseCode code, string message)
            {
                AuthorizationId = authorizationId;
                Status = status;
                Response = null;
                ProcessorKey = null;
                EntityId = 0;
                PaymentMethodId = 0;
                Message = message;
                Code = code;
            }
            public FragmentResponse(int authorizationId, TransactionStatus status, InsertWagersResponse response, string processorKey, int entityId, int paymentMethodId)
            {
                AuthorizationId = authorizationId;
                Status = status;
                Response = response;
                ProcessorKey = processorKey;
                EntityId = entityId;
                PaymentMethodId = paymentMethodId;
                Message = string.Empty;
                Code = AuthorizationResponseCode.OK;
            }
        }

        public class InsertWagersResponse : IPaymentChannelResponse
        {
            public int idTransaction { get; set; }
            public string idPlayer { get; set; }
            public string description { get; set; }
            public decimal amount { get; set; }
            public string referenceId { get; set; }
            public int toWin { get; set; }
            public List<string> date { get; set; }
            public string code { get; set; }
            public string message { get; set; }
            public List<ToWinByDrawAndNumber> tickets { get; set; }
            public List<string> details { get; set; }
            public AuthorizationResponseCode Code { get; set; }
            public string Response { get; set; }
            public string Url { get; set; }
        }

        public class ToWinByDrawAndNumberResponse
        {
            public string date { get; set; }
            public string drawHour { get; set; }
            public string number { get; set; }
            public string draw { get; set; }
            public int maxPayout { get; set; }
            public int volumeByNumber { get; set; }
        }
    }
}
