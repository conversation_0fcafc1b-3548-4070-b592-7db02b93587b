﻿using GamesEngine.Business;
using GamesEngine.Gameboards.Lines;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class DailyProfitReport: Objeto
    {
        List<CompletedDailyTotalProfitRecord> records = new List<CompletedDailyTotalProfitRecord>();

        internal IEnumerable<CompletedDailyTotalProfitRecord> GetAll => records;
        static ReportDBHandler lottoDBHandler;

        public DailyProfitReport()
        {
            if (lottoDBHandler == null)
            {
                if (Integration.Db?.DBSelected == HistoricalDatabaseType.MySQL.ToString())
                {
                    lottoDBHandler = new ReportDBHandlerMySQL(Integration.Db.MySQL);
                    if (!lottoDBHandler.ExistsStorage()) lottoDBHandler.CreateStorage();
                }
                else if (Integration.Db?.DBSelected == HistoricalDatabaseType.SQLServer.ToString())
                {
                    lottoDBHandler = new ReportDBHandlerSQLServer(Integration.Db.SQLServer);
                    if (!lottoDBHandler.ExistsStorage()) lottoDBHandler.CreateStorage();
                }
                else
                {
#if DEBUG

#else
                throw new Exception($"There is no connection for {Integration.Db.DBSelected}");
#endif
                }
            }
            
        }

        internal CompletedMatchesPerLineTypes Accumulate(DateTime day, int tournamentId, int gameId)
        {
            if (day.Hour != 0 || day.Minute != 0 || day.Second != 0 || day.Millisecond != 0) throw new GameEngineException($"{nameof(day)} '{day}' must be an exact day");
            if (tournamentId < 0) throw new GameEngineException($"{nameof(tournamentId)} must be greater or equal than zero");
            if (gameId < 0) throw new GameEngineException($"{nameof(gameId)} must be greater or equal than zero");

            var dailyTotalProfitRecords = lottoDBHandler.Accumulate(day, tournamentId, gameId);
            return dailyTotalProfitRecords;
        }

        internal void Generate(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0 || startDate.Millisecond != 0) throw new GameEngineException($"{nameof(startDate)} '{startDate}' must be an exact day");
            if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0 || endDate.Millisecond != 0) throw new GameEngineException($"{nameof(endDate)} '{endDate}' must be an exact day");
            if (tournamentId < 0) throw new GameEngineException($"{nameof(tournamentId)} must be greater or equal than zero");
            if (gameId < 0) throw new GameEngineException($"{nameof(gameId)} must be greater or equal than zero");
            if (lineType < 0) throw new GameEngineException($"{nameof(lineType)} must be greater or equal than zero");

            records = lottoDBHandler.GenerateDailyTotalProfits(startDate, endDate, sport, league, tournamentId, gameId, lineType, domainIds);
            foreach (var record in records)
            {
                Add(record);
            }
        }

        void Add(CompletedDailyTotalProfitRecord record)
        {
            if (record == null) throw new ArgumentNullException(nameof(record));

            TotalWinners += record.TotalWinners;
            TotalWagers += record.TotalWagers;
            TotalTicketAmount += record.TotalTicketAmount;
            TotalPrize += record.TotalPrize;
            TotalProfit += record.TotalProfit;
        }

        internal int TotalWinners { get; private set; }

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }

        [Puppet]
        internal class CompletedDailyTotalProfitRecord : Objeto
        {
            internal LineType LineType { get; }
            internal int DomainId { get; }
            internal int GameId => game.GameId;
            internal int TournamentId => game.TournamentId;
            internal string TeamAName => game.TeamAName;
            internal string TeamBName => game.TeamBName;
            internal string SportName => game.SportName;
            internal string LeagueName => game.LeagueName;
            internal DateTime StartDate => game.StartDate;
            readonly CompletedScoreGame game;

            public CompletedDailyTotalProfitRecord(CompletedScoreGame game, LineType lineType, int domainId, int ticketsCount, int winnersCount, decimal sold, decimal prizes, decimal profits)
            {
                if (game == null) throw new ArgumentNullException(nameof(game));
                if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater or equal than zero");
                if (ticketsCount < 0) throw new GameEngineException($"{nameof(ticketsCount)} must be greater or equalthan zero");
                if (winnersCount < 0) throw new GameEngineException($"{nameof(winnersCount)} must be greater or equal than zero");
                if (sold < 0) throw new GameEngineException($"{nameof(sold)} must be greater or equal than zero");
                if (prizes < 0) throw new GameEngineException($"{nameof(prizes)} must be greater or equal than zero");

                this.game = game;
                LineType = lineType;
                DomainId = domainId;
                TotalWagers = ticketsCount;
                TotalWinners = winnersCount;
                TotalTicketAmount = sold;
                TotalPrize = prizes;
                TotalProfit = profits;
            }

            internal int TotalWinners { get; private set; }

            internal int TotalWagers { get; private set; }

            internal decimal TotalTicketAmount { get; private set; }

            internal decimal TotalPrize { get; private set; }

            internal decimal TotalProfit { get; private set; }
        }

        abstract class ReportDBHandler
        {
            protected const string COMMON_SELECT_FOR_WAGERS_QUERIES = @"SELECT WW.tournament, WW.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport, 
linetype, LV.line, text, teamareward, teambreward, tiereward, spread, abanswer, yesreward, noreward, yesnoanswer, overreward, underreward, overunderanswer, fixedoptions, fixedrewards, fixedanswer,
authorizationid, accountnumber, towin, risk, WW.gradingstatus, chosenanswer, fixedchosenanswer";
            internal const string TABLE_DAILY_TOTAL_PROFIT = "linesdailytotalprofit";
            protected readonly string connectionString;

            protected ReportDBHandler(string connectionString)
            {
                if (string.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));

                this.connectionString = connectionString;
            }

            internal CompletedMatchesPerLineTypes Accumulate(DateTime day, int tournamentId, int gameId)
            {
                if (day.Hour != 0 || day.Minute != 0 || day.Second != 0 || day.Millisecond != 0) throw new GameEngineException($"{nameof(day)} '{day}' must be an exact day");

                var dailyTotalProfitRecords = GenerateCompletedMatchesPerLineTypes(tournamentId, gameId);
                var existsDailyRecord = ExistsDailyRecord(day, tournamentId, gameId);
                if (existsDailyRecord)
                {
                    RemoveInStorage(day, tournamentId, gameId);
                }
                SaveInStorage(dailyTotalProfitRecords);

                return dailyTotalProfitRecords;
            }

            internal abstract void CreateStorage();
            internal abstract bool ExistsStorage();
            internal abstract bool ExistsDailyRecord(DateTime dateWithoutTime, int tournamentId, int gameId);
            internal abstract DateTime LastDateRegistered();
            internal abstract CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypes(int tournamentId, int gameId);
            protected abstract CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command);
            protected abstract void SaveInStorage(CompletedMatchesPerLineTypes dailyTotalProfitRecords);
            protected abstract void RemoveInStorage(DateTime dateWithoutTime, int tournamentId, int gameId);
            internal abstract List<CompletedDailyTotalProfitRecord> GenerateDailyTotalProfits(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds);
            protected abstract List<CompletedDailyTotalProfitRecord> DailyTotalProfitRecords(string command);

            protected string DateTimeToDateString(DateTime date)
            {
                string result = date.ToString("yyyy-MM-dd");
                return result;
            }
        }

        class ReportDBHandlerMySQL : ReportDBHandler
        {
            internal ReportDBHandlerMySQL(string connectionString) : base(connectionString)
            {
            }

            internal override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append(@$"CREATE TABLE IF NOT EXISTS {TABLE_DAILY_TOTAL_PROFIT}
                    (
                    DATE DATETIME NOT NULL,
                    TOURNAMENT INT UNSIGNED NOT NULL,
                    GAME INT UNSIGNED NOT NULL,
                    LINETYPE SMALLINT UNSIGNED NOT NULL,
                    TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,
                    WINNERSCOUNT SMALLINT UNSIGNED NOT NULL,
                    SOLD DECIMAL(10,2) UNSIGNED NOT NULL,
                    PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,
                    PROFITS DECIMAL(10,2) NOT NULL,
                    DOMAINID SMALLINT UNSIGNED NOT NULL DEFAULT 0,
                    PRIMARY KEY (DATE, TOURNAMENT, GAME, LINETYPE, DOMAINID)
                    )");

                string sql = statement.ToString();
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistsStorage()
            {
                bool exists = true;
                string sql = $"SELECT 1 FROM {TABLE_DAILY_TOTAL_PROFIT} LIMIT 1";
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sql, connection))
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override bool ExistsDailyRecord(DateTime dateWithoutTime, int tournamentId, int gameId)
            {
                bool exists = false;
                string sql = $@"SELECT EXISTS(SELECT * FROM {TABLE_DAILY_TOTAL_PROFIT} 
                                WHERE date = '{DateTimeToDateString(dateWithoutTime)}' AND tournament = {tournamentId} AND game = {gameId})";
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            reader.Read();
                            var amountOfRows = reader.GetInt32(0);
                            exists = amountOfRows >= 1;
                            reader.Close();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override DateTime LastDateRegistered()
            {
                var lastDate = new DateTime();
                var sql = $"SELECT DATE FROM {TABLE_DAILY_TOTAL_PROFIT} ORDER BY DATE DESC LIMIT 1;";
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                lastDate = reader.GetDateTime(0);
                            }
                            reader.Close();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return lastDate;
            }

            internal override CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypes(int tournamentId, int gameId)
            {
                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'L'
                                FROM {ReceiverOfHistorical.TABLE_WAGERS_LOSERS} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion 
                                WHERE WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}, 'W'
                                FROM {ReceiverOfHistorical.TABLE_WAGERS_WINNERS} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion 
                                WHERE WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId} 
                                )
                                ";
                var result = GetMatchesPerLineTypesFromWagers(command);
                return result;
            }

            protected override CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command)
            {
                var result = new CompletedMatchesPerLineTypes();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: 0m,
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: 0m,
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerLineType matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, line.LineType, wager.DomainId))
                                {
                                    matchPerPlayer = new CompletedMatchPerLineType(result, scoreGame, line.LineType, wager.DomainId);
                                    result.Add(matchPerPlayer);
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, line.LineType, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override void SaveInStorage(CompletedMatchesPerLineTypes dailyTotalProfitRecords)
            {
                StringBuilder sql = new StringBuilder();

                if (dailyTotalProfitRecords.Any())
                {
                    var commandToInsert = $"INSERT INTO {TABLE_DAILY_TOTAL_PROFIT}(DATE, TOURNAMENT, GAME, LINETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID) VALUES ";
                    sql.Append(commandToInsert);
                    var dailyTotalProfitValues = new List<string>();
                    foreach (var record in dailyTotalProfitRecords)
                    {
                        string recordToInsert = $@"('{ DateTimeToDateString(record.StartDate)}',
                        '{ record.TournamentId}',
                        '{ record.GameId}',
						'{ (int)record.LineType}',
						'{ record.TotalWagers}',
						'{ record.TotalWinners}',
						'{ record.TotalTicketAmount}',
						'{ record.TotalPrize}',
						'{ record.TotalProfit}',
						'{ record.DomainId}'
						)";

                        dailyTotalProfitValues.Add(recordToInsert);
                    }

                    sql.Append(string.Join(",", dailyTotalProfitValues));
                    sql.Append(';');

                    using (var connection = new MySqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new MySqlCommand(sql.ToString(), connection))
                            {
                                command.CommandType = CommandType.Text;
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
            }

            protected override void RemoveInStorage(DateTime dateWithoutTime, int tournamentId, int gameId)
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT} 
                                        WHERE DATE=@date AND TOURNAMENT=@tournament AND GAME=@game ";
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(cmdText, connection))
                        {
                            command.Parameters.AddWithValue("@date", DateTimeToDateString(dateWithoutTime));
                            command.Parameters.AddWithValue("@tournament", tournamentId);
                            command.Parameters.AddWithValue("@game", gameId);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error: {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override List<CompletedDailyTotalProfitRecord> GenerateDailyTotalProfits(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"SELECT WW.tournament, WW.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport,
                                    date, linetype, ticketscount, winnerscount, sold, prizes, profits, domainid
                                FROM {TABLE_DAILY_TOTAL_PROFIT} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId}
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter}
                                ";
                var result = DailyTotalProfitRecords(command);
                return result;
            }

            protected override List<CompletedDailyTotalProfitRecord> DailyTotalProfitRecords(string command)
            {
                var records = new List<CompletedDailyTotalProfitRecord>();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (MySqlCommand cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            CompletedDailyTotalProfitRecord info;
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                var gameDay = reader.GetDateTime(9);
                                if (gameDay.Date != scoreGame.StartDate.Date) throw new GameEngineException($"Game day accumulated is different than registered game");
                                var lineType = (LineType)reader.GetInt16(10);
                                info = new CompletedDailyTotalProfitRecord(
                                    game: scoreGame,
                                    lineType: lineType,
                                    domainId: reader.GetInt16(16),
                                    ticketsCount: reader.GetInt32(11),
                                    winnersCount: reader.GetInt16(12),
                                    sold: reader.GetDecimal(13),
                                    prizes: reader.GetDecimal(14),
                                    profits: reader.GetDecimal(15)
                                );
                                records.Add(info);
                            }
                        }
                    }
                    connection.Close();
                }
                return records;
            }
        }

        class ReportDBHandlerSQLServer : ReportDBHandler
        {
            internal ReportDBHandlerSQLServer(string connectionString) : base(connectionString)
            {
            }

            internal override void CreateStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement
                    .Append(@$"CREATE TABLE IF NOT EXISTS {TABLE_DAILY_TOTAL_PROFIT}
                    (
                    DATE DATETIME NOT NULL,
                    TOURNAMENT INT UNSIGNED NOT NULL,
                    GAME INT UNSIGNED NOT NULL,
                    LINETYPE SMALLINT UNSIGNED NOT NULL,
                    TICKETSCOUNT MEDIUMINT UNSIGNED NOT NULL,
                    WINNERSCOUNT SMALLINT UNSIGNED NOT NULL,
                    SOLD DECIMAL(10,2) UNSIGNED NOT NULL,
                    PRIZES DECIMAL(10,2) UNSIGNED NOT NULL,
                    PROFITS DECIMAL(10,2) NOT NULL,
                    DOMAINID SMALLINT UNSIGNED NOT NULL DEFAULT 0,
                    PRIMARY KEY (DATE, LINETYPE, DOMAINID)
                    )");

                string sql = statement.ToString();
                using (var connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new SqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"SQL Error [{ sql.ToString() }]. {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistsStorage()
            {
                bool exists = true;
                string sql = $"SELECT 1 FROM {TABLE_DAILY_TOTAL_PROFIT} LIMIT 1";
                
                using (var connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new SqlCommand(sql, connection))
                        {
                            var dataReader = command.ExecuteReader();
                            dataReader.Close();
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override bool ExistsDailyRecord(DateTime dateWithoutTime, int tournamentId, int gameId)
            {
                bool exists = true;
                string sql = $"SELECT EXISTS(SELECT * FROM {TABLE_DAILY_TOTAL_PROFIT} WHERE DATE={DateTimeToDateString(dateWithoutTime)} AND tournament = {tournamentId} AND game = {gameId})";
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new MySqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            reader.Read();
                            var amountOfRows = reader.GetByte(0);
                            exists = amountOfRows >= 1;
                            reader.Close();
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override DateTime LastDateRegistered()
            {
                var lastDate = new DateTime();
                var sql = $"SELECT DATE FROM {TABLE_DAILY_TOTAL_PROFIT} ORDER BY DATE DESC LIMIT 1;";
                using (var connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                lastDate = reader.GetDateTime(0);
                            }
                            reader.Close();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"SQL Error [{ sql.ToString() }]. {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return lastDate;
            }

            internal override CompletedMatchesPerLineTypes GenerateCompletedMatchesPerLineTypes(int tournamentId, int gameId)
            {
                var command = $@"({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {ReceiverOfHistorical.TABLE_WAGERS_LOSERS} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion 
                                WHERE WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId} 
                                )
                                UNION
                                ({COMMON_SELECT_FOR_WAGERS_QUERIES}
                                FROM {ReceiverOfHistorical.TABLE_WAGERS_WINNERS} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_VERSIONS} LV ON WW.tournament = LV.tournament AND WW.game = LV.game AND WW.line = LV.line AND WW.lineversion = LV.lineversion 
                                WHERE WW.timestamp = 0 AND LS.timestamp = 0 AND LV.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId} 
                                )
                                ";
                var result = GetMatchesPerLineTypesFromWagers(command);
                return result;
            }

            protected override CompletedMatchesPerLineTypes GetMatchesPerLineTypesFromWagers(string command)
            {
                var result = new CompletedMatchesPerLineTypes();
                using (var connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (var cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            var lines = new Dictionary<int, CompletedLine>();
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                CompletedLine line;
                                var lineType = (LineType)reader.GetInt16(9);
                                var lineId = reader.GetInt32(10);
                                switch (lineType)
                                {
                                    case LineType.MONEY_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.SPREAD_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedSpreadLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            spread: reader.GetDecimal(15)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.YES_NO_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(19);
                                            var yesNoAnswer = answerAsCode == 'Y' ? YesNoAnswer.YES : YesNoAnswer.NO;
                                            line = new CompletedYesNoLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                yesReward: reader.GetInt16(17),
                                                noReward: reader.GetInt16(18),
                                                realAnswer: yesNoAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.OVER_UNDER_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedOverUnderLine(
                                                scoreGame,
                                                lineId: lineId,
                                                text: reader.GetString(11),
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: 0m,
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.FIXED_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedFixedLine(
                                            scoreGame,
                                            lineId: lineId,
                                            text: reader.GetString(11),
                                            options: reader.GetString(23),
                                            rewards: reader.GetString(24),
                                            realAnswer: reader.GetString(25)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.TOTAL_POINTS_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            var answerAsCode = reader.GetChar(22);
                                            var overUnderAnswer = answerAsCode == 'O' ? OverUnderAnswer.OVER : OverUnderAnswer.UNDER;
                                            line = new CompletedTotalPointsLine(
                                                scoreGame,
                                                lineId: lineId,
                                                overReward: reader.GetInt16(20),
                                                underReward: reader.GetInt16(21),
                                                score: 0m,
                                                realAnswer: overUnderAnswer
                                                );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    case LineType.MONEYDRAW_LINE:
                                        if (!lines.TryGetValue(lineId, out line))
                                        {
                                            line = new CompletedMoneyDrawLine(
                                            scoreGame,
                                            lineId: lineId,
                                            teamAReward: reader.GetInt16(12),
                                            teamBReward: reader.GetInt16(13),
                                            realAnswer: reader.GetChar(16),
                                            tieReward: reader.GetInt16(14)
                                            );
                                            lines.Add(line.LineId, line);
                                        }
                                        break;
                                    default:
                                        throw new GameEngineException($"{nameof(lineType)} {lineType} is invalid");
                                }

                                GradingStatus gradingStatus = GradingStatus.PENDING;
                                var gradingStatusAsCode = reader.GetChar(30);
                                gradingStatus.StatusFrom(gradingStatusAsCode);
                                var wagerStatusAsCode = reader.GetChar(33);
                                var wagerStatus = WagerStatus.L;
                                wagerStatus.StatusFrom(wagerStatusAsCode);
                                var chosenAnswer = reader.IsDBNull(31) ? reader.GetString(32) : reader.GetChar(31).ToString();
                                var wager = new CompletedWager(
                                    line,
                                    authorizationId: reader.GetInt32(26),
                                    accountNumber: reader.GetString(27),
                                    toWin: reader.GetDecimal(28),
                                    risk: reader.GetDecimal(29),
                                    gradingStatus: gradingStatus,
                                    status: wagerStatus,
                                    chosenAnswer: chosenAnswer
                                );

                                CompletedMatchPerLineType matchPerPlayer;
                                if (!result.Exists(scoreGame.GameId, line.LineType, wager.DomainId))
                                {
                                    matchPerPlayer = new CompletedMatchPerLineType(result, scoreGame, line.LineType, wager.DomainId);
                                    result.Add(matchPerPlayer);
                                }
                                else
                                {
                                    matchPerPlayer = result.Find(scoreGame.GameId, line.LineType, wager.DomainId);
                                }
                                matchPerPlayer.Add(wager);
                            }
                        }
                    }
                    connection.Close();
                }
                return result;
            }

            protected override void SaveInStorage(CompletedMatchesPerLineTypes dailyTotalProfitRecords)
            {
                StringBuilder sql = new StringBuilder();

                if (dailyTotalProfitRecords.Any())
                {
                    var commandToInsert = $"INSERT INTO {TABLE_DAILY_TOTAL_PROFIT}(DATE, TOURNAMENT, GAME, LINETYPE, TICKETSCOUNT, WINNERSCOUNT, SOLD, PRIZES, PROFITS, DOMAINID) VALUES ";
                    sql.Append(commandToInsert);
                    var dailyTotalProfitValues = new List<string>();
                    foreach (var record in dailyTotalProfitRecords)
                    {
                        string recordToInsert = $@"('{ DateTimeToDateString(record.StartDate)}',
                        '{ record.TournamentId}',
                        '{ record.GameId}',
						'{ record.LineType}',
						'{ record.TotalWagers}',
						'{ record.TotalWinners}',
						'{ record.TotalTicketAmount}',
						'{ record.TotalPrize}',
						'{ record.TotalProfit}',
						'{ record.DomainId}'
						)";

                        dailyTotalProfitValues.Add(recordToInsert);
                    }

                    sql.Append(string.Join(",", dailyTotalProfitValues));
                    sql.Append(';');

                    using (var connection = new MySqlConnection(connectionString))
                    {
                        try
                        {
                            connection.Open();
                            using (var command = new MySqlCommand(sql.ToString(), connection))
                            {
                                command.CommandType = CommandType.Text;
                                command.ExecuteNonQuery();
                            }
                        }
                        catch (Exception e)
                        {
                            throw new GameEngineException($"MySQL Error [{ sql.ToString() }]. {e.Message}");
                        }
                        finally
                        {
                            connection.Close();
                        }
                    }
                }
            }

            protected override void RemoveInStorage(DateTime dateWithoutTime, int tournamentId, int gameId)
            {
                using (var connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        var cmdText = $@"DELETE FROM {TABLE_DAILY_TOTAL_PROFIT} 
                                        WHERE DATE=@date AND TOURNAMENT=@tournament AND GAME=@game ";
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(cmdText, connection))
                        {
                            command.Parameters.AddWithValue("@date", DateTimeToDateString(dateWithoutTime));
                            command.Parameters.AddWithValue("@tournament", tournamentId);
                            command.Parameters.AddWithValue("@game", gameId);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException($"MySQL Error: {e.Message}");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override List<CompletedDailyTotalProfitRecord> GenerateDailyTotalProfits(DateTime startDate, DateTime endDate, string sport, string league, int tournamentId, int gameId, int lineType, string domainIds)
            {
                var sportFilter = string.IsNullOrWhiteSpace(sport) ? string.Empty : $" AND sport ='{sport}'";
                var leagueFilter = string.IsNullOrWhiteSpace(league) ? string.Empty : $" AND league ='{league}'";
                var tournamentIdFilter = tournamentId == 0 ? string.Empty : $" AND WW.tournament = {tournamentId}";
                var gameIdFilter = gameId == 0 ? string.Empty : $" AND WW.game = {gameId}";
                var lineTypeFilter = lineType == 0 ? string.Empty : $" AND linetype = {lineType}";
                var domainIdFilter = string.IsNullOrWhiteSpace(domainIds) ? string.Empty : $" AND domainid in ({domainIds})";

                var command = $@"SELECT WW.tournament, WW.game, teamaname, teambname, scoreteama, scoreteamb, startgamedate, league, sport,
                                    date, linetype, ticketscount, winnerscount, sold, prizes, profits, domainid
                                FROM {TABLE_DAILY_TOTAL_PROFIT} WW
                                INNER JOIN {ReceiverOfHistorical.TABLE_LINES_SCORES} LS ON WW.tournament = LS.tournament AND WW.game = LS.game 
                                WHERE LS.timestamp = 0 AND WW.tournament = {tournamentId} AND WW.game = {gameId}
                                    {sportFilter} {leagueFilter} {tournamentIdFilter} {gameIdFilter} {lineTypeFilter}
                                ";
                var result = DailyTotalProfitRecords(command);
                return result;
            }

            protected override List<CompletedDailyTotalProfitRecord> DailyTotalProfitRecords(string command)
            {
                var records = new List<CompletedDailyTotalProfitRecord>();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    connection.Open();
                    using (MySqlCommand cmd = new MySqlCommand(command, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var matches = new CompletedMatches();
                            var games = new Dictionary<int, CompletedScoreGame>();
                            CompletedDailyTotalProfitRecord info;
                            while (reader.Read())
                            {
                                var tournamentId = reader.GetInt32(0);
                                var gameId = reader.GetInt32(1);
                                CompletedScoreGame scoreGame;
                                if (!games.TryGetValue(gameId, out scoreGame))
                                {
                                    scoreGame = new CompletedScoreGame(matches,
                                        tournamentId: tournamentId,
                                        gameId: gameId,
                                        teamAName: reader.GetString(2),
                                        teamBName: reader.GetString(3),
                                        scoreTeamA: reader.GetInt16(4),
                                        scoreTeamB: reader.GetInt16(5),
                                        leagueName: reader.GetString(7),
                                        sportName: reader.GetString(8),
                                        startDate: reader.GetDateTime(6)
                                    );
                                    games.Add(scoreGame.GameId, scoreGame);
                                }

                                var gameDay = reader.GetDateTime(9);
                                if (gameDay.Date != scoreGame.StartDate.Date) throw new GameEngineException($"Game day accumulated is different than registered game");
                                var lineType = (LineType)reader.GetInt16(10);
                                info = new CompletedDailyTotalProfitRecord(
                                    game: scoreGame,
                                    lineType: lineType,
                                    domainId: reader.GetInt16(16),
                                    ticketsCount: reader.GetInt32(11),
                                    winnersCount: reader.GetInt16(12),
                                    sold: reader.GetDecimal(13),
                                    prizes: reader.GetDecimal(14),
                                    profits: reader.GetDecimal(15)
                                );
                                records.Add(info);
                            }
                        }
                    }
                    connection.Close();
                }
                return records;
            }
        }
    }

}
