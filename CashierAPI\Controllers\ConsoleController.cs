using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Finance.PaymentChannels;

namespace CashierAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
		[HttpPost("console/{account}/command/")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> processCommandAsync(string account)
		{
			string body = "";

			using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
			{
				body = await reader.ReadToEndAsync();
			}

			if (body == null) return NotFound("Body is required");
			if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

			var result = await  CashierAPI.Cashier.PerformCmdAsync(account, HttpContext, body);
			return result;
		}

		[HttpPost("console/{account}/query")]
		[Authorize(Roles = "devops")]
		public async Task<IActionResult> processQueryAsync(string account)
		{
			string body = "";

			using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
			{
				body = await reader.ReadToEndAsync();
			}

			if (body == null) return NotFound("Body is required");
			if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

			var result = await CashierAPI.Cashier.PerformQryAsync(account, HttpContext, body);
			return result;
		}

        [HttpGet("console/{account}/archive")]
        [Authorize(Roles = "devops")]
        public IActionResult AchiveActor(string account, string startDate, string endDate)
        {
            if (!Validator.IsValidDate(startDate)) return NotFound($"Parameter {nameof(startDate)} is empty or invaid: {startDate}");
            if (!Validator.IsValidDate(endDate)) return NotFound($"Parameter {nameof(endDate)} is empty or invaid: {endDate}");

            DateTime sDate = DateTime.Parse(startDate);
            DateTime eDate = DateTime.Parse(endDate);

            var result = CashierAPI.Cashier.PerformArchive(account, HttpContext, sDate, eDate);

            return result;
        }

        [HttpGet("console/{account}/trim")]
        [Authorize(Roles = "devops")]
        public IActionResult TrimActor(string account, string trimmedDown)
        {
            if (!Validator.IsValidDate(trimmedDown)) return NotFound($"Parameter {nameof(trimmedDown)} is empty or invaid: {trimmedDown}");

            DateTime tDown = DateTime.Parse(trimmedDown);

            var result = CashierAPI.Cashier.PerformTrim(account, HttpContext, tDown);

            return result;
        }

        [HttpPost("console/{account}/removeExpiredFragments")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> RemoveExpiredFragmentsAsync(string account)
        {
            var result = await CashierAPI.Cashier.PerformChkThenCmdAsync(account, HttpContext, $@"
                {{
					needsToRemoveExpiredFragments = atAddress.NeedsToRemoveExpiredFragments(now);
					Check(needsToRemoveExpiredFragments) Information 'This account does not need to remove fragments';
				}}
					", $@"
				{{
					atAddress.RemoveExpiredFragments(itIsThePresent, now);
				}}
            ");

            return result;
        }

        [HttpGet("console/startFollower")]
        [Authorize(Roles = "devops")]
        public IActionResult StartFollower(string daysRange)
        {
            _ = Task.Run(() =>
            {
                CashierFollower.CashierFollower.MainFollower(daysRange);
            });

            return Ok($"Runnig Lotto Follower...");
        }

        [HttpDelete("console/delete/fragmentsAndMovements")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> DeleteFragmentsAndMovementsAsync()
        {
			var allAccount = Movements.Storage.AllCashierAccounts();
			/*
			var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
            {{
                for (currencies:company.System.Coins)
                {{
                    currency = currencies;
					print currency.Iso4217Code currency;
                }}
            }}");
			if (!(result is OkObjectResult))
			{
				throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
			}
			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			CurrenciesPrototype allCoins = JsonConvert.DeserializeObject<CurrenciesPrototype>(json);
			*/

			foreach (var account in allAccount)
			{
				Movements.Storage.DeleteAuthFragments(account);
				foreach (Currencies.CODES code in Enum.GetValues(typeof(Currencies.CODES)))
				{
					Movements.Storage.DeleteMovements(account, code);
				}
			}

			return Ok($"{Enumerable.Count(allAccount)}...");
		}

        [HttpGet("load/lruactors")]
        [AllowAnonymous]
        public async Task<IActionResult> LoadLRUActorAsync()
        {
            var actorsToLoad = CashierAPI.Cashier.ListActorsToLoad();

            _ = Task.Run(() =>
            {
                foreach (var actor in actorsToLoad)
                {
					try
					{
						var loadedActor = CashierAPI.Cashier.PerformQryAsync(actor.Substring(1), HttpContext, $"print '{actor.Substring(1)}' Name;");
						loadedActor.Wait();
                    }
                    catch
                    {
                    }
                }
            });

            return Ok($"Loading {Enumerable.Count(actorsToLoad)} actors...");
        }

        [HttpPut("console/actors")]
		[Authorize(Roles = "devops")]
		public IEnumerable<ActorsReleaseReponse> ReleaseActors(double timeWithoutActivity )
		{
			return CashierAPI.Cashier.Release(timeWithoutActivity);
		}

        [HttpPut("console/release/actor")]
        [Authorize(Roles = "devops")]
        public bool ReleaseActor(string atAddressNumber)
        {
            if (string.IsNullOrWhiteSpace(atAddressNumber)) return false;

            return CashierAPI.Cashier.ReleaseActor(atAddressNumber);
        }

        [HttpGet("console/actors")]
		[Authorize(Roles = "devops")]
		public IEnumerable<ActorsReponse> Actors()
		{
			return CashierAPI.Cashier.ListActors();
		}

		[HttpGet("producer/stop")]
		[AllowAnonymous]
		public async Task<IActionResult> StopProducerAsync()
		{
			await Consumer.StopAllConsumerActiveInMemoryAsync();

			IProducer kafka = Integration.Kafka;
			if (kafka != null)
			{
				await Integration.Kafka.StopProducerAsync();
				return Ok();
			}
			return BadRequest("Kafka is not configured.");
		}

		[HttpGet("consumer/stop")]
		[AllowAnonymous]
		public async Task<IActionResult> StopConsumerAsync()
		{
			await Consumer.StopAllConsumerActiveInMemoryAsync();
			return Ok("All consumers are stopped!");
		}

		[HttpPost("consumer/reset")]
		[Authorize(Roles = "devops")]
		public async Task<IEnumerable<int>> ResetAsync(int[] consumerIds)
		{
			return await Consumer.ResetAsync(consumerIds);
		}

		[HttpGet("consumers")]
		[Authorize(Roles = "devops")]
		public List<string> List()
		{
			return Consumer.ConsumersInfo();
		}

		[HttpGet("consumer/start")]
		[AllowAnonymous]
		public IActionResult StartConsumer()
		{
			Integration.UseKafka = true;
			if (Integration.UseKafka) new ApiController().CreateConsumerForTopics();
			return Ok("Consumers restarted!");
		}

		[HttpGet("load/actor")]
		[AllowAnonymous]
		public async Task<IActionResult> LoadAllActorAsync(string accountNumber)
		{
			var result = await CashierAPI.Cashier.PerformQryAsync(accountNumber, HttpContext, $"print '{accountNumber}' Name;");

			return Ok(result);
		}

        internal async Task<CurrenciesPrototype> AllCurrenciesAsync()
        {
            var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
            {{
                for (currencies:company.System.Coins)
                {{
                    currency = currencies;
					print currency.Iso4217Code currency;
                }}
            }}");
            if (!(result is OkObjectResult))
            {
                throw new GameEngineException($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();

            var allCoins = JsonConvert.DeserializeObject<CurrenciesPrototype>(json);
            return allCoins;
        }

        [HttpGet("console/balance")]
		[AllowAnonymous]
		public async Task<IActionResult> PingAsync(string accountNumber, [FromHeader(Name = "domain-url")] string domain)
        {
			if (string.IsNullOrWhiteSpace(accountNumber)) return NotFound($"Parameter {nameof(accountNumber)} is empty or invaid: {accountNumber}");
            if (string.IsNullOrWhiteSpace(domain)) return NotFound($"Parameter {nameof(domain)} is empty or invaid: {domain}");
            
            //Get Agent from User
            Agents agent = Security.Agent(User);

            //Get All Currencies from actor
            var currencies = await AllCurrenciesAsync();
            List<string> currenciesList = currencies.currencies.Select(x => x.currency).ToList();

            GamesEngine.Finance.BalancesResponse response = await PaymentChannels.CustomerBalancesAsync(domain, accountNumber, (int)agent, currenciesList);

			return Ok(response);
		}

		[HttpGet("console/ping")]
		[AllowAnonymous]
		public IActionResult Ping()
		{
			return Ok("pong");
		}

	}
}

