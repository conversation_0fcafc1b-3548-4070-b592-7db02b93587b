﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Domains;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class Deposit : Objeto
    {
        internal Jar JarOrigin { get; private set; }
        internal int Id { get; private set; }
        internal string ConfirmedCurrency { get; private set; } 
        internal decimal ConfirmedAmount { get; private set; } 
        internal decimal Amount { get; private set; }
        internal string Address { get; private set; }
        internal string Kind => JarOrigin.Kind;
        internal string InvoiceId { get; private set; }
        internal int AuthorizationId { get; private set; }
        internal int ExternalReference { get; private set; }
        internal string ExternalAtAddress { get; private set; } = string.Empty;
        internal string Destination { get; private set; }
        internal string PaymentLink { get; private set; } = string.Empty;
        internal decimal Rate { get; private set; }
        internal DateTime CreatedAt { get; private set; }
        internal DateTime ConfirmedDate { get; private set; }
        internal DateTime CanceledDated { get; private set; }
        internal bool IsPending => ConfirmedDate == DateTime.MinValue && CanceledDated == DateTime.MinValue;
        internal bool IsConfirmed => ConfirmedDate != DateTime.MinValue && CanceledDated == DateTime.MinValue;
        internal bool IsCanceled => CanceledDated != DateTime.MinValue;
        internal int StoreId { get; private set; }
        internal Domain Domain { get; private set; }

        internal Deposit(int id, string address, DateTime createdAt, Jar jarOrigin, string invoiceId, int authorizationId, string externalAtAddress, int externalReference, string destination, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int storeId, Domain domain)
        {
            if (id <= 0) throw new ArgumentException("Id must be greater than zero.", nameof(id));
            if (string.IsNullOrWhiteSpace(address)) throw new ArgumentException("Address cannot be null or empty.", nameof(address));
            if (createdAt == DateTime.MinValue) throw new ArgumentException("CreatedAt date cannot be MinValue.", nameof(createdAt));
            if (jarOrigin == null) throw new ArgumentNullException(nameof(jarOrigin), "Jar origin cannot be null.");
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentException("Invoice id cannot be null or empty.", nameof(invoiceId));
            if (authorizationId <= 0) throw new ArgumentException("Authorization id must be greater than zero.", nameof(authorizationId));
            if (externalReference <= 0) throw new ArgumentException("External reference must be greater than zero.", nameof(externalReference));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentException("Destination link cannot be null or empty.", nameof(destination));
            if (exchangeAmount <= 0) throw new ArgumentException("Exchange amount must be greater than zero.", nameof(exchangeAmount));
            if (exchangeRate <= 0) throw new ArgumentException("Rate cannot be negative.", nameof(exchangeRate));
            if (string.IsNullOrWhiteSpace(confirmedCurrency)) throw new ArgumentException("Confirmed currency cannot be null or empty.", nameof(confirmedCurrency));
            if (confirmedAmount <= 0) throw new ArgumentException("Confirmed amount must be greater than zero.", nameof(confirmedAmount));
            if (storeId <= 0) throw new ArgumentException("Store id must be greater than zero.", nameof(storeId));
            if (domain == null) throw new ArgumentNullException(nameof(domain), "Domain cannot be null.");

            Id = id;
            Address = address;
            CreatedAt = createdAt;
            JarOrigin = jarOrigin;//ESTO ES DE REVISAR, NO DEBE
            InvoiceId = invoiceId;
            AuthorizationId = authorizationId;
            ExternalReference = externalReference;
            ExternalAtAddress = externalAtAddress;
            Destination = destination;
            Rate = exchangeRate;
            ConfirmedCurrency = confirmedCurrency;
            ConfirmedAmount = confirmedAmount;
            Amount = exchangeAmount;
            StoreId = storeId;//PASARLO AL ConfirmationsInboundTask
            Domain = domain;//PASARLO AL ConfirmationsInboundTask
        }

        internal void Confirm(DateTime confirmedDate)
        {
            if (confirmedDate == DateTime.MinValue) throw new ArgumentException("Confirmed date cannot be MinValue.", nameof(confirmedDate));
            if (IsConfirmed) throw new InvalidOperationException("Deposit is already confirmed.");
            ConfirmedDate = confirmedDate;
        }

        internal void Cancel(DateTime canceledDate)
        {
            if (canceledDate == DateTime.MinValue) throw new ArgumentNullException(nameof(canceledDate));
            if (IsCanceled) throw new GameEngineException("Deposit is already canceled.");
            CanceledDated = canceledDate;
        }
    }
}
