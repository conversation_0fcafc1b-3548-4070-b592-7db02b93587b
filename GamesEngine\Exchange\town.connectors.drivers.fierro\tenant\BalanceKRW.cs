﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using BalancesResponse = GamesEngine.Finance.BalancesResponse;

namespace town.connectors.drivers.fiero
{

    #if DEBUG

    internal class BalanceKRW : BalanceInsider
    {
        public BalanceKRW() : base("KRW") { }
    }
    #endif

}
