﻿using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class PendingMatches: Objeto
    {
        readonly List<PendingMatch> matches = new List<PendingMatch>();

        internal IEnumerable<PendingMatch> GetAll => matches;

        internal bool Exists(Game game, Domain domain)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = matches.Exists(match => match.Game == game && match.Domain == domain);
            return result;
        }

        internal PendingMatch Find(Game game, Domain domain)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            var result = matches.Find(match => match.Game == game && match.Domain == domain);
            if (result == null) throw new GameEngineException($"No match with {nameof(game)} '{game.Number}' and {nameof(domain)} '{domain.Url}'");
            return result;
        }

        internal void Add(PendingMatch match)
        {
            if (match == null) throw new ArgumentNullException(nameof(match));
            if (Exists(match.Game, match.Domain)) throw new GameEngineException("Match was already added");

            matches.Add(match);
        }

        internal void Add(IEnumerable<PendingMatch> pendingMatches)
        {
            foreach (var match in pendingMatches)
            {
                Add(match);
            }
        }

        internal void UpdateTotals(Wager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);

            TotalWagers ++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin() : 0;
            TotalPrize += toWin;
            if (wager.IsWinner() || wager.IsLoser()) TotalProfit += wager.Risk - toWin;
        }

        HashSet<string> accounts;

        internal IEnumerable<string> Accounts
        {
            get
            {
                return accounts;
            }
        }

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
