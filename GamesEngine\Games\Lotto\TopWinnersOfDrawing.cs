﻿using GamesEngine.Business;
using GamesEngine.Finance;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal class TopWinnersOfDrawing : Objeto
    {
        internal string State { get; }
        internal string GameType { get; }
        internal string DomainUrl { get; }
        internal DateTime DrawDate { get; }

        internal TopWinnersOfDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
        {
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (state.Length != 2) throw new GameEngineException($"{nameof(state)} {state} is not valid");
            if (gameType.Length != 2) throw new GameEngineException($"{nameof(gameType)} {gameType} is not valid");

            DrawDate = drawDate;
            State = state;
            GameType = gameType;
            DomainUrl = domainUrl;
        }

        List<RankedWinner> rankedWinners = new List<RankedWinner>();
        internal void RankWinners(IEnumerable<WinnerInfo> sortedWinners)
        {
            if (!sortedWinners.Any()) throw new GameEngineException($"{nameof(sortedWinners)} cannot be empty");

            int position = 0;
            decimal lastPrize = decimal.MaxValue;
            var sortedWinnerWagers = sortedWinners.SelectMany(winner => winner.WinnerWagers()).OrderByDescending(winner => winner.Prize);
            foreach (var winnerWager in sortedWinnerWagers)
            {
                var winner = winnerWager.WinnerInfo;
                if (winner.DomainUrl != DomainUrl) throw new GameEngineException($"{nameof(winner)} does not belong to domain {DomainUrl}");
                if (winner.StateAbb != State) throw new GameEngineException($"{nameof(winner)} does not belong to {nameof(State)} {State}");
                if (winner.DrawDate() != DrawDate) throw new GameEngineException($"{nameof(winner)} does not belong to {nameof(DrawDate)} {DrawDate}");
                if (!winner.IdOfLottery.EqualsTo(GameType)) throw new GameEngineException($"{nameof(winner)} does not belong to {nameof(GameType)} {GameType}");
                if (winnerWager.Prize > lastPrize) throw new GameEngineException($"{nameof(sortedWinners)} is not sorted");

                if (winnerWager.Prize < lastPrize) position++;
                rankedWinners.Add(new RankedWinner(winnerWager, position));
                lastPrize = winnerWager.Prize;
            }
        }

        internal IEnumerable<RankedWinner> Top2WithPlayerTicket(List<int> ticketNumbers)
        {
            if (rankedWinners.Count == 0) return rankedWinners;

            int ticketNumbersFound = 0;
            var result = new List<RankedWinner>();
            foreach (var winner in rankedWinners)
            {
                if (ticketNumbers.Contains(winner.TicketNumber))
                {
                    ticketNumbersFound++;
                    var placeToInsert = 2;
                    if (winner.Position <= 2)
                    {
                        var currentWinner = result.ElementAtOrDefault(winner.Position - 1);
                        if (currentWinner == null || currentWinner.Position >= winner.Position) placeToInsert = winner.Position - 1;
                    }
                    result.Insert(placeToInsert, winner);
                }
                else if (result.Count < 3 && ticketNumbersFound == ticketNumbers.Count || result.Count < 2 && ticketNumbersFound != ticketNumbers.Count) result.Add(winner);
                if (result.Count == 3 && ticketNumbersFound == ticketNumbers.Count) break;
            }

            return result;
        }

        internal class RankedWinner : Objeto
        {
            internal int Position { get; }
            internal decimal Prize => winner.Prize;
            internal string PrizeFormatted { get; }
            internal string WinnerNumber => winner.Wager.SubticketAsString;
            internal int TicketNumber => winner.WinnerInfo.TicketNumber;
            internal string AccountNumber => winner.WinnerInfo.AccountNumber;
            internal string GameType => winner.WinnerInfo.GameType();

            WagerWinnerInfo winner;
            public RankedWinner(WagerWinnerInfo winner, int position)
            {
                this.winner = winner;
                PrizeFormatted = Currency.Factory(winner.WinnerInfo.Company.Lotto900().StandardCurrency, winner.Prize).ToDisplayFormat();
                Position = position;
            }
        }
    }
}
