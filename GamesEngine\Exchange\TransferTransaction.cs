﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Exchange
{
	internal class TransferTransaction : Transaction
	{
		internal const int TEMPLATE_ID = 2;

		private int authorizationId;
		private CustomerAccount toAccount;
		internal TransferTransaction(TransactionDefinition transactionDefinition, bool itsThePresent, IConversionSpread conversionSpread, CustomerAccount toAccount, int authorizationId, string employeeName, Currency amount, DateTime now)
	: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Transfer)
		{
			this.authorizationId = authorizationId;
			this.toAccount = toAccount;
			CustomerAccount fromAccount = TransactionDefinition.Account;

			transactionDefinition.Batch.AddTransactionDenifition(this);

			string description = $"Transfer {amount.CurrencyCode} {amount.Value} from {fromAccount.Identificator} to {toAccount.Identificator}";
			SendFragmentsCreationMessage(itsThePresent, description, amount.Value, fromAccount.CustomerAccountNumber, amount.Coin, authorizationId, amount.Value, Agents.INSIDER, now, Domain.Url);
        }


		//protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted, int journalEntryNumber)
		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted)
		{
			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				if (!itsThePresent) return;

				string RealAccount = toAccount.CustomerAccountNumber;
				string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(gross.Coin);

				CustomerAccount fromAccount = TransactionDefinition.Account;
				var isTheSameCurrencyCode = gross.CurrencyCode == toAccount.CurrencyCode;
				string concept = isTheSameCurrencyCode ?
					$"Transfer {amountToCustomer.CurrencyCode} {amountToCustomer.Value} from {fromAccount.Identificator} to {toAccount.Identificator}" :
					$"Transfer {gross.CurrencyCode} {gross.Value} from {fromAccount.Identificator} converted to {amountToCustomer.CurrencyCode} {amountToCustomer.Value} in {toAccount.Identificator}";

				SendFragmentPaymentMessage(
					itsThePresent, 
					date, 
					gross.CurrencyCode.ToString(), 
					fromAccount.CustomerAccountNumber, 
					authorizationId, 
					employeeName, 
					WagerStatus.L, 
					concept,
					net,
					RealAccount,
					thirdPartyOutAccount,
					Type,
					Status);
				const int NoProcessorAccountId = 0;
				//SendDepositMessage(
				//	itsThePresent,
				//	toAccount.CurrencyCode.ToString(),
				//	toAccount.CustomerAccountNumber,
				//	amountToCustomer.Value,
				//	concept,
				//	this.Id,
				//	employeeName,
				//	toAccount.Identificator,
				//	NoProcessorAccountId,
				//	PaymentChannels.Agents.INSIDER
				//	);



				Integration.Kafka.Send(
                    itsThePresent,
                    $"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new ApprovedTransactionMessage(TransactionType.Transfer, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, authorizationId, NoProcessorAccountId)
                );

				SendProfitNotification(date, itsThePresent, fromAccount, profit);
			}
        }

		private void SendProfitNotification(DateTime date, bool itIsThePresent, CustomerAccount account, Currency profit)
        {
			if (profit.Value > 0)
            {
				var player = TransactionDefinition.FindOwner().Player;
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
					new CustomerSaleMessage(account.CustomerIdentifier, 
					account.CustomerAccountNumber, 
					player.Company.Sales.CurrentStore.Id, 
					date, 
					profit,
					(int)player.Agent));
			}
		}

		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
            TransactionDefinition.Deny(this);

            if (!itsThePresent) return;

			Currency net = Currency.Factory(TransactionDefinition.Account.CurrencyCode, 0);
			string RealAccount = "";
			string thirdPartyOutAccount = TransactionDefinition.OutAccountFor(TransactionDefinition.Account.Coin);

			CustomerAccount fromAccount = TransactionDefinition.Account;
           SendFragmentPaymentMessage(
				itsThePresent, 
				date, 
				fromAccount.CurrencyCode.ToString(), 
				fromAccount.CustomerAccountNumber, 
				authorizationId, employeeName,
				WagerStatus.X, 
				reason,
				net,
				RealAccount,
				thirdPartyOutAccount,
				Type,
				Status);

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
            {
                Integration.Kafka.Send(
                    itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
                    new DeniedTransactionMessage(TransactionType.Transfer, Id, date, reason, employeeName)
                );
            }
        }

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode && Amount.CurrencyCode == toAccount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

	}

	

}
