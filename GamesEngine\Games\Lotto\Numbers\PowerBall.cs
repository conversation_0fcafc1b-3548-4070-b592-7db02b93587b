﻿using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal struct PowerBall : IPick
    {
        private readonly WhiteBall whiteBall1;
        private readonly WhiteBall whiteBall2;
        private readonly WhiteBall whiteBall3;
        private readonly WhiteBall whiteBall4;
        private readonly WhiteBall whiteBall5;
        private readonly RedBall redBall;

        private PowerBall(WhiteBall pair1, WhiteBall pair2, <PERSON>B<PERSON> pair3, <PERSON>Ball pair4, WhiteBall pair5, RedBall powerBall)
        {
            if (
                pair1 == pair2 || pair1 == pair3 || pair1 == pair4 || pair1 == pair5 ||
                pair2 == pair3 || pair2 == pair4 || pair2 == pair5 ||
                pair3 == pair4 || pair3 == pair5 ||
                pair4 == pair5
            ) throw new GameEngineException($"Every Powerball pair of number must be unique. {pair1},{pair2},{pair3},{pair4},{pair5} has duplicated numbers");
            whiteBall1 = pair1;
            whiteBall2 = pair2;
            whiteBall3 = pair3;
            whiteBall4 = pair4;
            whiteBall5 = pair5;
            redBall = powerBall;
        }

        internal PowerBall(int pair1, int pair2, int pair3, int pair4, int pair5, int powerBall) :
             this(new WhiteBall(pair1), new WhiteBall(pair2), new WhiteBall(pair3), new WhiteBall(pair4), new WhiteBall(pair5), new RedBall(powerBall))
        {
        }

        internal PowerBall(string pair1, string pair2, string pair3, string pair4, string pair5, string powerBall) :
            this(new WhiteBall(pair1), new WhiteBall(pair2), new WhiteBall(pair3), new WhiteBall(pair4), new WhiteBall(pair5), new RedBall(powerBall))
        {
        }

        internal PowerBall(string sequenceOfNumbers)
        {
            if (string.IsNullOrWhiteSpace(sequenceOfNumbers)) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 12) throw new GameEngineException($"Number '{sequenceOfNumbers}' is not a valid {nameof(PowerBall)} Lottery number");
            string pair1 = "" + sequenceOfNumbers[0] + sequenceOfNumbers[1];
            string pair2 = "" + sequenceOfNumbers[2] + sequenceOfNumbers[3];
            string pair3 = "" + sequenceOfNumbers[4] + sequenceOfNumbers[5];
            string pair4 = "" + sequenceOfNumbers[6] + sequenceOfNumbers[7];
            string pair5 = "" + sequenceOfNumbers[8] + sequenceOfNumbers[9];
            string red = "" + sequenceOfNumbers[10] + sequenceOfNumbers[11];
            if (
                pair1 == pair2 || pair1 == pair3 || pair1 == pair4 || pair1 == pair5 ||
                pair2 == pair3 || pair2 == pair4 || pair2 == pair5 ||
                pair3 == pair4 || pair3 == pair5 ||
                pair4 == pair5
            ) throw new GameEngineException($"Every Powerball pair of number must be unique. {pair1},{pair2},{pair3},{pair4},{pair5} has duplicated numbers");

            whiteBall1 = new WhiteBall(pair1);
            whiteBall2 = new WhiteBall(pair2);
            whiteBall3 = new WhiteBall(pair3);
            whiteBall4 = new WhiteBall(pair4);
            whiteBall5 = new WhiteBall(pair5);
            redBall = new RedBall(red);
        }

        Pattern IPick.this[int i] => throw new GameEngineException("PowerBall does not have single digits as there are in Picks lotteries.");

        IEnumerable<IPick> IPick.Permute() => throw new GameEngineException("PowerBall does not have permutations.");

        internal WhiteBall this[int i]
        {
            get
            {
                switch(i)
                {
                    case 1:
                        return whiteBall1;
                    case 2:
                        return whiteBall2;
                    case 3:
                        return whiteBall3;
                    case 4:
                        return whiteBall4;
                    case 5:
                        return whiteBall5;
                    default:
                        throw new GameEngineException($"Powerball has only 5 pairs of numbers. Pair {i} does not exist.");
                }
            }
        }

        internal RedBall RedBall
        {
            get
            {
                return redBall;
            }
        }

        private bool allIndexesAreDifferent(int[] indexes)
        {
            var result =
                indexes[0] == indexes[1] || indexes[0] == indexes[2] || indexes[0] == indexes[3] || indexes[0] == indexes[4] ||
                indexes[1] == indexes[2] || indexes[1] == indexes[3] || indexes[1] == indexes[4] ||
                indexes[2] == indexes[3] || indexes[2] == indexes[4] ||
                indexes[3] == indexes[4];
            return ! result;
        }

        internal int WhiteMatches(PowerBall other)
        {
            int result = 0;
            if (other.whiteBall1 == whiteBall1 || other.whiteBall2 == whiteBall1 || other.whiteBall3 == whiteBall1 || other.whiteBall4 == whiteBall1 || other.whiteBall5 == whiteBall1)
            {
                result++;
            }
            if (other.whiteBall1 == whiteBall2 || other.whiteBall2 == whiteBall2 || other.whiteBall3 == whiteBall2 || other.whiteBall4 == whiteBall2 || other.whiteBall5 == whiteBall2)
            {
                result++;
            }
            if (other.whiteBall1 == whiteBall3 || other.whiteBall2 == whiteBall3 || other.whiteBall3 == whiteBall3 || other.whiteBall4 == whiteBall3 || other.whiteBall5 == whiteBall3)
            {
                result++;
            }
            if (other.whiteBall1 == whiteBall4 || other.whiteBall2 == whiteBall4 || other.whiteBall3 == whiteBall4 || other.whiteBall4 == whiteBall4 || other.whiteBall5 == whiteBall4)
            {
                result++;
            }
            if (other.whiteBall1 == whiteBall5 || other.whiteBall2 == whiteBall5 || other.whiteBall3 == whiteBall5 || other.whiteBall4 == whiteBall5 || other.whiteBall5 == whiteBall5)
            {
                result++;
            }
            return result;
        }

        internal bool IsMarked(WhiteBall pair1, WhiteBall pair2, WhiteBall pair3, WhiteBall pair4, WhiteBall pair5)
        {
            bool result = whiteBall1 == pair1 && whiteBall2 == pair2 && whiteBall3 == pair3 && whiteBall4 == pair4 && whiteBall5 == pair5;
            return result;
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            result.Add(this);
            return result;
        }

        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            result.Add(new SubTicket<IPick>(this));
            return result;
        }

        int IPick.Length => Length;
        public int Length
        {
            get
            {
                return 6;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return 1;
            }
        }

        string IPick.AsString() => AsString();
        internal string AsString()
        {
            return $"[{whiteBall1},{whiteBall2},{whiteBall3},{whiteBall4},{whiteBall5},{redBall}]";
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            return $"{whiteBall1.AsStringForAccounting()}-{whiteBall2.AsStringForAccounting()}-{whiteBall3.AsStringForAccounting()}-{whiteBall4.AsStringForAccounting()}-{whiteBall5.AsStringForAccounting()},{redBall.AsStringForAccounting()}";
        }

        internal static PowerBall GenerateOneRandom()
        {
            var whiteBalls = new List<WhiteBall>();
            var whiteBall1 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
            whiteBalls.Add(whiteBall1);
            var whiteBall2 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
            whiteBalls.Add(whiteBall2);
            var whiteBall3 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
            whiteBalls.Add(whiteBall3);
            var whiteBall4 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
            whiteBalls.Add(whiteBall4);
            var whiteBall5 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
            whiteBalls.Add(whiteBall5);
            var redBall = RedBall.GenerateOneRandom();
            return new PowerBall(whiteBall1, whiteBall2, whiteBall3, whiteBall4, whiteBall5, redBall);
        }

        internal static PowerBall GenerateOneRandomWithSomeFixedNumbers(string pair1, string pair2, string pair3, string pair4, string pair5, string powerBall)
        {
            var whiteBalls = new List<WhiteBall>();
            WhiteBall whiteBall1 = new WhiteBall(), whiteBall2 = new WhiteBall(), whiteBall3 = new WhiteBall(), whiteBall4 = new WhiteBall(), whiteBall5 = new WhiteBall();
            if (pair1 != "*")
            {
                whiteBall1 = new WhiteBall(pair1);
                whiteBalls.Add(whiteBall1);
            }
            if (pair2 != "*")
            {
                whiteBall2 = new WhiteBall(pair2);
                whiteBalls.Add(whiteBall2);
            }
            if (pair3 != "*")
            {
                whiteBall3 = new WhiteBall(pair3);
                whiteBalls.Add(whiteBall3);
            }
            if (pair4 != "*")
            {
                whiteBall4 = new WhiteBall(pair4);
                whiteBalls.Add(whiteBall4);
            }
            if (pair5 != "*")
            {
                whiteBall5 = new WhiteBall(pair5);
                whiteBalls.Add(whiteBall5);
            }

            if (pair1 == "*")
            {
                whiteBall1 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
                whiteBalls.Add(whiteBall1);
            }
            if (pair2 == "*")
            {
                whiteBall2 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
                whiteBalls.Add(whiteBall2);
            }
            if (pair3 == "*")
            {
                whiteBall3 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
                whiteBalls.Add(whiteBall3);
            }
            if (pair4 == "*")
            {
                whiteBall4 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
                whiteBalls.Add(whiteBall4);
            }
            if (pair5 == "*")
            {
                whiteBall5 = WhiteBall.GenerateOneRandomDistinctOf(whiteBalls);
                whiteBalls.Add(whiteBall5);
            }

            var redBall = powerBall == "*" ? RedBall.GenerateOneRandom() : new RedBall(powerBall);
            return new PowerBall(whiteBall1, whiteBall2, whiteBall3, whiteBall4, whiteBall5, redBall);
        }

        public long ToInt64()
        {
            return this.whiteBall1.ToInt32() * 10000000000 + this.whiteBall2.ToInt32() * 100000000 + this.whiteBall3.ToInt32() * 1000000 + this.whiteBall4.ToInt32() * 10000 + this.whiteBall5.ToInt32() * 100 + this.redBall.ToInt32();
        }
    }
}
