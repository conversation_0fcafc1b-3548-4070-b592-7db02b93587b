﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public static class OlapTableDefinitions
    {
        public const string DepositTableName = "deposit";
        public const string JarTableName = "jar";
        public const string TankTableName = "tank";
        public const string TankerTableName = "tanker";
        public const string JarDetailTableName = "jardetail";
        public const string TankDetailTableName = "tankdetail";
        public const string TankerDetailTableName = "tankerdetail";
        public const string WithdrawalTableName = "withdrawal";
        public const string BottleTableName = "bottle";
        public const string DispenserTableName = "dispenser";
        public const string BottleDetailTableName = "bottledetail";
        public const string DispenserDetailTableName = "dispenserdetail";

        public const string DepositCurrentJarAssignmentTable = "deposit_current_jar_assignment";
        public const string DepositCurrentTankAssignmentTable = "deposit_current_tank_assignment";
        public const string DepositCurrentTankerAssignmentTable = "deposit_current_tanker_assignment";
        public const string WithdrawalCurrentDispenserAssignmentTable = "withdrawal_current_dispenser_assignment";
        public const string DailyTransactionSummaryTable = "daily_transaction_summary";

        public const string MvDepositCurrentJarAssignment = "mv_deposit_current_jar_assignment";
        public const string MvDepositCurrentTankAssignment = "mv_deposit_current_tank_assignment";
        public const string MvDepositCurrentTankerAssignment = "mv_deposit_current_tanker_assignment";
        public const string MvWithdrawalCurrentDispenserAssignment = "mv_withdrawal_current_dispenser_assignment";
        public const string MvDailyDepositToSummary = "mv_daily_deposit_to_summary";
        public const string MvDailyWithdrawalToSummary = "mv_daily_withdrawal_to_summary";

        public static string DynamicTableName(string baseName, string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or whitespace for dynamic table naming.", nameof(kind));

            var sb = new StringBuilder(baseName.Length + kind.Length + 3);
            sb.Append('`').Append(baseName).Append('_');
            foreach (char c in kind)
            {
                sb.Append(char.ToLowerInvariant(c));
            }
            sb.Append('`');
            return sb.ToString();
        }
    }
}
