﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedWagers:Objeto
    {
        List<CompletedWager> wagers = new List<CompletedWager>();

        internal IEnumerable<CompletedWager> Wagers 
        { 
            get
            {
                return wagers;
            }
        }

        internal virtual void Add(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));

            wagers.Add(wager);

            if (accounts == null) accounts = new HashSet<string>();
            accounts.Add(wager.AccountNumber);
            TotalTicketAmount += wager.Risk;
            if (wager.IsWinner()) TotalPrize += wager.ToWin;
        }

        HashSet<string> accounts;

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers => wagers.Count;

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit()
        {
            return TotalTicketAmount - TotalPrize;
        }
    }
}
