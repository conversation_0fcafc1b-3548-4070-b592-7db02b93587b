﻿using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    internal interface INumberComparer
    {
        bool IsMatchingInAnyOrderThis(IPick number);
        bool IsMatchingInTheSameOrderThis(IPick number);
    }

    internal abstract class DesiredWinnerNumber : INumberComparer
    {
        internal IPick Number { get;}

        internal DesiredWinnerNumber(IPick number)
        {
            if (number == null) throw new ArgumentNullException(nameof(number));
            if (number.Count != 1) throw new GameEngineException($"{nameof(number)} {number.AsString()} must be a single number");

            Number = number;
        }

        public abstract bool IsMatchingInAnyOrderThis(IPick number);
        public abstract bool IsMatchingInTheSameOrderThis(IPick number);
    }

    internal sealed class DesiredWinnerNumberPick2 : DesiredWinnerNumber
    {
        internal DesiredWinnerNumberPick2(Pick2 number) : base(number)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick2.AreTheseNumbersTheSameInAnyOrder((Pick2)Number, (Pick2)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick2.AreTheseNumbersTheSameInTheSameOrder((Pick2)Number, (Pick2)numberToCheck);
            return result;
        }
    }

    internal sealed class DesiredWinnerNumberPick3 : DesiredWinnerNumber
    {
        internal DesiredWinnerNumberPick3(Pick3 number) : base(number)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick3.AreTheseNumbersTheSameInAnyOrder((Pick3)Number, (Pick3)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick3.AreTheseNumbersTheSameInTheSameOrder((Pick3)Number, (Pick3)numberToCheck);
            return result;
        }
    }

    internal sealed class DesiredWinnerNumberPick4 : DesiredWinnerNumber
    {
        internal DesiredWinnerNumberPick4(Pick4 number) : base(number)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick4.AreTheseNumbersTheSameInAnyOrder((Pick4)Number, (Pick4)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick4.AreTheseNumbersTheSameInTheSameOrder((Pick4)Number, (Pick4)numberToCheck);
            return result;
        }
    }

    internal sealed class DesiredWinnerNumberPick5 : DesiredWinnerNumber
    {
        internal DesiredWinnerNumberPick5(Pick5 number) : base(number)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick5.AreTheseNumbersTheSameInAnyOrder((Pick5)Number, (Pick5)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = WinnerNumberPick5.AreTheseNumbersTheSameInTheSameOrder((Pick5)Number, (Pick5)numberToCheck);
            return result;
        }
    }

    internal abstract class WinnerNumber : Objeto, INumberComparer
    {
        internal IPick Number { get;}

        private readonly LotteryDraw draw;

        internal DateTime LastGradedDate => draw.LastGradedDate;

        internal DateTime DrawDate => draw.Date;

        internal string StateAbbWhereBelongs => draw.State.Abbreviation;

        internal string ScheduleDescriptionWhereBelongs => draw.FindSchedule().GetDescription();

        internal string SequenceOfNumbers => draw.SequenceOfNumbers;

        internal WinnerNumber(LotteryDraw draw)
        {
            if (draw == null) throw new ArgumentNullException(nameof(draw));

            this.draw = draw;
            Number = draw.WinnerNumber;
        }

        public abstract bool IsMatchingInAnyOrderThis(IPick number);
        public abstract bool IsMatchingInTheSameOrderThis(IPick number);
    }

    internal sealed class WinnerNumberPick2 : WinnerNumber
    {
        internal WinnerNumberPick2(LotteryDraw draw) : base(draw)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInAnyOrder((Pick2)Number, (Pick2)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInTheSameOrder((Pick2)Number, (Pick2)numberToCheck);
            return result;
        }

        public static bool AreTheseNumbersTheSameInAnyOrder(Pick2 number, Pick2 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var result = number.IsMarked(digit1, digit2) || number.IsMarked(digit2, digit1);
            return result;
        }

        public static bool AreTheseNumbersTheSameInTheSameOrder(Pick2 number, Pick2 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var result = number[1].SingleDigit == digit1 && number[2].SingleDigit == digit2;
            return result;
        }
    }

    internal sealed class WinnerNumberPick3 : WinnerNumber
    {
        internal WinnerNumberPick3(LotteryDraw draw) : base(draw)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInAnyOrder((Pick3)Number, (Pick3)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInTheSameOrder((Pick3)Number, (Pick3)numberToCheck);
            return result;
        }

        public static bool AreTheseNumbersTheSameInAnyOrder(Pick3 number, Pick3 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var result = number.IsMarked(digit1, digit2, digit3) ||
                number.IsMarked(digit1, digit3, digit2) ||
                number.IsMarked(digit3, digit1, digit2) ||
                number.IsMarked(digit3, digit2, digit1) ||
                number.IsMarked(digit2, digit3, digit1) ||
                number.IsMarked(digit2, digit1, digit3);
            return result;
        }

        public static bool AreTheseNumbersTheSameInTheSameOrder(Pick3 number, Pick3 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var result = number[1].SingleDigit == digit1 && number[2].SingleDigit == digit2 && number[3].SingleDigit == digit3;
            return result;
        }
    }

    internal sealed class WinnerNumberPick4 : WinnerNumber
    {
        internal WinnerNumberPick4(LotteryDraw draw) : base(draw)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInAnyOrder((Pick4)Number, (Pick4)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInTheSameOrder((Pick4)Number, (Pick4)numberToCheck);
            return result;
        }

        public static bool AreTheseNumbersTheSameInAnyOrder(Pick4 number, Pick4 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var digit4 = numberToCheck[4].SingleDigit;
            var result = number.IsBoxed(digit1, digit2, digit3, digit4);
            return result;
        }

        public static bool AreTheseNumbersTheSameInTheSameOrder(Pick4 number, Pick4 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var digit4 = numberToCheck[4].SingleDigit;
            var result = number[1].SingleDigit == digit1 && number[2].SingleDigit == digit2 && number[3].SingleDigit == digit3 && number[4].SingleDigit == digit4;
            return result;
        }
    }

    internal sealed class WinnerNumberPick5 : WinnerNumber
    {
        internal WinnerNumberPick5(LotteryDraw draw) : base(draw)
        {
        }

        public override bool IsMatchingInAnyOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInAnyOrder((Pick5)Number, (Pick5)numberToCheck);
            return result;
        }

        public override bool IsMatchingInTheSameOrderThis(IPick numberToCheck)
        {
            if (numberToCheck == null) throw new ArgumentNullException(nameof(numberToCheck));
            if (numberToCheck.Count != 1) throw new GameEngineException($"{nameof(numberToCheck)} {numberToCheck.AsString()} must be a single number");

            var result = AreTheseNumbersTheSameInTheSameOrder((Pick5)Number, (Pick5)numberToCheck);
            return result;
        }

        public static bool AreTheseNumbersTheSameInAnyOrder(Pick5 number, Pick5 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var digit4 = numberToCheck[4].SingleDigit;
            var digit5 = numberToCheck[5].SingleDigit;
            var result = number.IsBoxed(digit1, digit2, digit3, digit4, digit5);
            return result;
        }

        public static bool AreTheseNumbersTheSameInTheSameOrder(Pick5 number, Pick5 numberToCheck)
        {
            var digit1 = numberToCheck[1].SingleDigit;
            var digit2 = numberToCheck[2].SingleDigit;
            var digit3 = numberToCheck[3].SingleDigit;
            var digit4 = numberToCheck[4].SingleDigit;
            var digit5 = numberToCheck[5].SingleDigit;
            var result = number[1].SingleDigit == digit1 && number[2].SingleDigit == digit2 && number[3].SingleDigit == digit3 && number[4].SingleDigit == digit4 && number[5].SingleDigit == digit5;
            return result;
        }
    }

    internal abstract class LotteryDrawsMatchingDesiredNumber : Objeto
    {
        internal Dictionary<Schedule, WinnerNumber> LotteryDraws { get;} = new Dictionary<Schedule, WinnerNumber>();
        internal DesiredWinnerNumber DesiredNumber { get;}

        internal LotteryDrawsMatchingDesiredNumber(IPick number)
        {
            if (number == null) throw new ArgumentNullException(nameof(number));

            DesiredNumber = CreateDesiredWinnerNumber(number);
        }

        internal IEnumerable<WinnerNumber> ListWinnerNumbers()
        {
            return LotteryDraws.Values.ToList();
        }

        internal void AddIfItMatchesWinner(LotteryDraw draw)
        {
            if (draw == null) throw new ArgumentNullException(nameof(draw));

            if (DesiredNumber.IsMatchingInAnyOrderThis(draw.WinnerNumber))
            {
                WinnerNumber winnerNumber;
                var schedule = draw.FindSchedule();
                LotteryDraws.TryGetValue(schedule, out winnerNumber);
                if (winnerNumber == null)
                {
                    winnerNumber = CreateWinnerNumber(draw);
                    LotteryDraws.Add(schedule, winnerNumber);
                }
                else if (winnerNumber.LastGradedDate < draw.LastGradedDate)
                {
                    winnerNumber = CreateWinnerNumber(draw);
                    LotteryDraws[schedule] = winnerNumber;
                }
            }
        }

        protected abstract DesiredWinnerNumber CreateDesiredWinnerNumber(IPick number);

        protected abstract WinnerNumber CreateWinnerNumber(LotteryDraw draw);

        internal bool DesiredNumberMatchesInAnyOrderThis(WinnerNumber winnerNumber)
        {
            if (winnerNumber == null) throw new ArgumentNullException(nameof(winnerNumber));

            var result = DesiredNumber.IsMatchingInAnyOrderThis(winnerNumber.Number);
            return result;
        }

        internal bool DesiredNumberMatchesInTheSameOrderThis(WinnerNumber winnerNumber)
        {
            if (winnerNumber == null) throw new ArgumentNullException(nameof(winnerNumber));

            var result = DesiredNumber.IsMatchingInTheSameOrderThis(winnerNumber.Number);
            return result;
        }
    }

    internal sealed class LotteryDrawsMatchingDesiredNumberPick2 : LotteryDrawsMatchingDesiredNumber
    {
        internal LotteryDrawsMatchingDesiredNumberPick2(Pick2 number) : base(number)
        {
        }

        protected override DesiredWinnerNumber CreateDesiredWinnerNumber(IPick number)
        {
            return new DesiredWinnerNumberPick2((Pick2)number);
        }

        protected override WinnerNumber CreateWinnerNumber(LotteryDraw draw)
        {
            return new WinnerNumberPick2(draw);
        }
    }

    internal sealed class LotteryDrawsMatchingDesiredNumberPick3 : LotteryDrawsMatchingDesiredNumber
    {
        internal LotteryDrawsMatchingDesiredNumberPick3(Pick3 number) : base(number)
        {
        }

        protected override DesiredWinnerNumber CreateDesiredWinnerNumber(IPick number)
        {
            return new DesiredWinnerNumberPick3((Pick3)number);
        }

        protected override WinnerNumber CreateWinnerNumber(LotteryDraw draw)
        {
            return new WinnerNumberPick3(draw);
        }
    }

    internal sealed class LotteryDrawsMatchingDesiredNumberPick4 : LotteryDrawsMatchingDesiredNumber
    {
        internal LotteryDrawsMatchingDesiredNumberPick4(Pick4 number) : base(number)
        {
        }

        protected override DesiredWinnerNumber CreateDesiredWinnerNumber(IPick number)
        {
            return new DesiredWinnerNumberPick4((Pick4)number);
        }

        protected override WinnerNumber CreateWinnerNumber(LotteryDraw draw)
        {
            return new WinnerNumberPick4(draw);
        }
    }

    internal sealed class LotteryDrawsMatchingDesiredNumberPick5 : LotteryDrawsMatchingDesiredNumber
    {
        internal LotteryDrawsMatchingDesiredNumberPick5(Pick5 number) : base(number)
        {
        }

        protected override DesiredWinnerNumber CreateDesiredWinnerNumber(IPick number)
        {
            return new DesiredWinnerNumberPick5((Pick5)number);
        }

        protected override WinnerNumber CreateWinnerNumber(LotteryDraw draw)
        {
            return new WinnerNumberPick5(draw);
        }
    }
}
