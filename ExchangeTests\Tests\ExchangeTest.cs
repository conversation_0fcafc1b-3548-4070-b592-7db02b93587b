﻿using ExchangeAPI.Controllers;
using ExchangeAPI.ExchangeEntities;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ExchangeTests.Tests
{
    [TestClass]
    public class ExchangeTest
    {
        private string sqlStringConection = string.Empty;// "persistsecurityinfo=True;port=3306;Server=localhost;Database=exchangetests;user id=*********;password=*********;SslMode=preferred";
        
        [TestMethod]
        public void CurrentExchangeRatesTest()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection is required.");

            var dbExchangeForTesting = new MySQLDBExchangeCreator(sqlStringConection);

            using (var context = new ExchangeContext(dbExchangeForTesting.ContextOptions))
            {
#if DEBUG
                ExchangeAPI.Logic.ExchangeRates.ContextForTesting = context;
#endif
                var rates = ExchangeAPI.Logic.ExchangeRates.CurrentExchangeRates();

                Assert.AreEqual(2, rates.Count());
            }
        }

        [TestMethod]
        public void CurrentExchangeRateForCurrencies()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection is required.");

            var dbExchangeForTesting = new MySQLDBExchangeCreator(sqlStringConection);

            using (var context = new ExchangeContext(dbExchangeForTesting.ContextOptions))
            {
#if DEBUG
                ExchangeAPI.Logic.ExchangeRates.ContextForTesting = context;
#endif
                var rate = ExchangeAPI.Logic.ExchangeRates.CurrentExchangeRateFor(3, 2);

                Assert.AreEqual(7000m, rate.Purchaseprice);
                Assert.AreEqual(7500m, rate.Saleprice);
            }
        }

        [TestMethod]
        public void ConvertAmount()
        {
            if (string.IsNullOrEmpty(sqlStringConection)) Assert.Inconclusive("MySQL string connection is required.");

            var dbExchangeForTesting = new MySQLDBExchangeCreator(sqlStringConection);

            using (var context = new ExchangeContext(dbExchangeForTesting.ContextOptions))
            {
#if DEBUG
                ExchangeAPI.Logic.ExchangeRates.ContextForTesting = context;
#endif
                var amountConverted = ExchangeAPI.Logic.ExchangeRates.Convert(3, 2, 2m);

                Assert.AreEqual(14000m, amountConverted);
            }
        }
        
    }
}
