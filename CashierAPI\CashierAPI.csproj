﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\**" />
    <Content Remove="wwwroot\**" />
    <EmbeddedResource Remove="wwwroot\**" />
    <None Remove="wwwroot\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\ExchangeAPI\Controllers\CatalogConsumer.cs" Link="Controllers\CatalogConsumer.cs" />
	<Compile Include="..\MarchMadnessAPI\Controllers\AuthorizeController.cs" Link="Controllers\AuthorizeController.cs" />
	<Compile Include="..\MarchMadnessAPI\Controllers\DriverController.cs" Link="Controllers\DriverController.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.31.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.HttpOverrides" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.2" />
    <PackageReference Include="Microsoft.UnitTestFramework.Extensions" Version="2.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="17.13.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
  </ItemGroup>
  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\GamesEngineMocks\GamesEngineMocks.csproj" />
    <ProjectReference Include="..\GamesEngine\GamesEngine.csproj" />
  </ItemGroup>
</Project>