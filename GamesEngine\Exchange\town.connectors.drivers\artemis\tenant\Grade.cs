﻿using GamesEngine.Settings;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using static GamesEngine.Finance.PaymentChannels;

namespace Connectors.town.connectors.drivers.artemis
{
    public abstract class Grade : DGSTenantDriver
    {
        public static TokenDriver CashierToken { get; set; }
        private RestClient _postClient;
        public string CashierUrl { get; private set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private RestClient _postUpdateWagersClient;

        public Grade(string currencyCode) : base(Tenant_Actions.Grade, TransactionType.PayPrize, currencyCode)
        {
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
			if (_postUpdateWagersClient == null)
			{
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postUpdateWagersClient = new RestClient(ServicesUrl);
            }

            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        _ = Task.Run(async () =>
                        {
                            await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                        });
                    }

                    _postUpdateWagersClient = new RestClient(ServicesUrl);
                }
            }

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            if (_postClient == null)
            {
                _postClient = new RestClient(CashierUrl);
            }

            var wagersList = recordSet.Mappings["gradeFreeFormWagers"].AsObject;
            switch (wagersList)
            {
                case List<PayFragmentsWithAtAddressMessage> fragmentWithAtAddressList:
                    //Ir a cashier a hacer el cambio de status 
                    var result = PayFragments(now, recordSet);//Cashier

                    //UpdateWagers
                    var wagers = fragmentWithAtAddressList.Where(wager => wager.AgentId == (int)Agents.ARTEMIS).ToList();
                    if (wagers.Any())
                    {
                        UpdateWagers(now, wagers);//Fragmentos que pertenecen a artemis
                    }
                    return (T)Convert.ChangeType(result, typeof(T));
                case List<PayFragmentsMessage> fragmentList:
                    //UpdateWagers
                    var wagers2 = fragmentList.Where(wager => wager.AgentId == (int)Agents.ARTEMIS).ToList();
                    if (wagers2.Any())
                    {
                        UpdateWagers(now, wagers2);//Fragmentos que pertenecen a artemis
                    }
                    return (T)Convert.ChangeType(DEFAULT_RESPONSE, typeof(T));
                default:
                    throw new Exception($"Invalid type {wagersList.GetType()} for Wagers");
            }
		}

        private static GradeFreeFormWagersResponse DEFAULT_RESPONSE = new GradeFreeFormWagersResponse()
        {
            Error = new ErrorResponse()
            {
                Code = "0",
                Message = "No response is ok"
            }
        };

        public override void Prepare(DateTime now)
        {
            //CustomSettings.AddVariableParameter("wagers"); //VERIFICAR DE DONDE VIENEN LOS WAGERS

            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("processorId");
            CustomSettings.AddVariableParameter("processorKey");
            CustomSettings.AddVariableParameter("updateFragments");
            CustomSettings.AddVariableParameter("gradeFreeFormWagers");

            //CustomSettings.Prepare();

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;


            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public void UpdateWagers(DateTime now, List<PayFragmentsWithAtAddressMessage> wagers)
        {
            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (!wagers.Any()) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)} on {nameof(UpdateWagers)}");

            Debug.WriteLine($"Accounting service {nameof(UpdateWagersColection)} received {nameof(wagers)}:{wagers.Count()}");
            UpdateWagersColection(wagers);
        }

        public void UpdateWagers(DateTime now, List<PayFragmentsMessage> wagers)
        {
            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (!wagers.Any()) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)} on {nameof(UpdateWagers)}");

            Debug.WriteLine($"Accounting service {nameof(UpdateWagersColection)} received {nameof(wagers)}:{wagers.Count()}");
            UpdateWagersColection(wagers);
        }

        void UpdateWagersColection(List<PayFragmentsWithAtAddressMessage> wagers)
        {
            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (wagers.Count() == 0) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)}");
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            const string url = "/GradingBet";
            List<List<PayFragmentsWithAtAddressMessage>> listOfGradesChunked = SplitList(wagers, GradedWagersPerChunk);
            foreach (List<PayFragmentsWithAtAddressMessage> wagersChunck in listOfGradesChunked)
            {
                GradeFreeFormWagerCollectionChunked(url, wagersChunck);
            }
        }

        void UpdateWagersColection(List<PayFragmentsMessage> wagers)
        {
            if (wagers == null) throw new ArgumentNullException(nameof(wagers));
            if (wagers.Count() == 0) throw new Exception($"At least one wager is required to send request {nameof(UpdateWagersColection)}");
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            const string url = "/GradingBet";
            List<List<PayFragmentsMessage>> listOfGradesChunked = SplitList(wagers, GradedWagersPerChunk);
            foreach (List<PayFragmentsMessage> wagersChunck in listOfGradesChunked)
            {
                GradeFreeFormWagerCollectionChunked(url, wagersChunck);
            }
        }

        List<List<T>> SplitList<T>(List<T> items, int nSize = 30)
        {
            var list = new List<List<T>>();

            for (int i = 0; i < items.Count; i += nSize)
            {
                list.Add(items.GetRange(i, Math.Min(nSize, items.Count() - i)));
            }

            return list;
        }

        void GradeFreeFormWagerCollectionChunked(string url, List<PayFragmentsWithAtAddressMessage> wagers)
        {
            var gradeFreeFormWagers = new WagersUpdateBody();
            gradeFreeFormWagers.AddWagers(wagers);

            var jsonString = Commons.ToJson(gradeFreeFormWagers);
            string responseString = string.Empty;
            int retryNumber = 0;
            const int MAX_RETRIES_WAGER_COLLECTION = 5;
            string valuesWithHiddenFields = $"ticketNumbers:{string.Join(',', wagers.Select(wager => wager.TicketNumber))}";
            while (true)
            {
                try
                {
                    Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                    var request = new RestRequest(url, Method.Post);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}");
                        NotifyWarn(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");

                        if (responseString.Contains("Lotto wager not found.")) break;
                        retryNumber++;
                        Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                        if (retryNumber == MAX_RETRIES_WAGER_COLLECTION) break;
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(responseString))
                            Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nResponse: {responseString}");

                        break;
                    }
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                    if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
                    {
                        InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
                    }
                }
            }

        }


        void GradeFreeFormWagerCollectionChunked(string url, List<PayFragmentsMessage> wagers)
        {
            var gradeFreeFormWagers = new WagersUpdateBody();
            gradeFreeFormWagers.AddWagers(wagers);

            var jsonString = Commons.ToJson(gradeFreeFormWagers);
            string responseString = string.Empty;
            int retryNumber = 0;
            const int MAX_RETRIES_WAGER_COLLECTION = 5;
            string valuesWithHiddenFields = $"ticketNumbers:{string.Join(',', wagers.Select(wager => wager.TicketNumber))}";
            while (true)
            {
                try
                {
                    Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($@"url:{url} data:{valuesWithHiddenFields}");
                    var request = new RestRequest(url, Method.Post);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}");
                        NotifyWarn(nameof(GradeFreeFormWagerCollectionChunked), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");

                        if (responseString.Contains("Lotto wager not found.")) break;
                        retryNumber++;
                        Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                        if (retryNumber == MAX_RETRIES_WAGER_COLLECTION) break;
                    }
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(responseString))
                            Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Debug($"{nameof(GradeFreeFormWagerCollectionChunked)}\nUrl:{url}\nResponse: {responseString}");

                        break;
                    }
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesGradeUpdateFragment.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                    if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
                    {
                        InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
                    }
                }
            }

        }


        public GradeFreeFormWagersResponse PayFragments(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var who = recordSet.Mappings["who"].AsString;
            var concept = recordSet.Mappings["concept"].AsString;
            var storeId = recordSet.Mappings["storeId"].AsInt;
            var processorId =  recordSet.Mappings["processorId"].AsInt;
            var gradeFreeFormWagers = recordSet.Mappings["gradeFreeFormWagers"].As<List<PayFragmentsWithAtAddressMessage>>();
            var processorKey = recordSet.Mappings["processorKey"].AsString;

            var payBody = new PayBody()
            {
                StoreId = storeId,
                Concept = concept,
                Who = who,
                GradeFreeFormWagers = gradeFreeFormWagers,
                ProcessorKey = processorKey,
                ProcessorId = processorId,

            };

            var result = Pay(payBody);
            return result;
        }


        private GradeFreeFormWagersResponse Pay(PayBody frgamentBody)
        {
            GradeFreeFormWagersResponse payResponse;
            try
            {
                var url = $"api/pay";
                var jsonString = Commons.ToJson(frgamentBody);

                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                }
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
                string responseString = "";
                var resultFromCashier = _postClient.Execute(request);
                responseString = resultFromCashier.Content;
                string body = string.Empty;
                if (resultFromCashier.StatusCode == HttpStatusCode.OK)
                {
                    payResponse = JsonConvert.DeserializeObject<GradeFreeFormWagersResponse>(responseString);

                }
                else
                {
                    payResponse = new GradeFreeFormWagersResponse();
                    payResponse.Error = new ErrorResponse() { Code = resultFromCashier.StatusCode.ToString(), Message = responseString };
                    ErrorsSender.Send(body, $@"Pay fragment {responseString} fails.");
                }

            }
            catch (Exception e)
            {
                payResponse = new GradeFreeFormWagersResponse();
                payResponse.Error = new ErrorResponse() { Code = String.Empty, Message = e.ToString() };
                ErrorsSender.Send(e);

            }

            return payResponse;
        }
    }
}
