﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    public struct Pick3 : IPick
    {
        private readonly Pattern number1;
        private readonly Pattern number2;
        private readonly Pattern number3;

        private Pick3(<PERSON>tern number1, <PERSON>tern number2, <PERSON>tern number3)
        {
            this.number1 = number1;
            this.number2 = number2;
            this.number3 = number3;
            this.subticket = null;
        }

        public Pick3(string number1, string number2, string number3) :
            this(new <PERSON><PERSON>(number1), new <PERSON><PERSON>(number2), new <PERSON><PERSON>(number3))
        { }

        public Pick3(int number1, int number2, int number3) :
            this(new <PERSON><PERSON>(number1), new <PERSON>tern(number2), new <PERSON>tern(number3))
        { }

        public Pick3(string sequenceOfNumbers)
        {
            if (sequenceOfNumbers == null) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 3) throw new GameEngineException($"Number '{sequenceOfNumbers}' is not a valid {nameof(Pick3)} Lottery number");
            char digit1 = sequenceOfNumbers[0];
            char digit2 = sequenceOfNumbers[1];
            char digit3 = sequenceOfNumbers[2];
            number1 = new Pattern(digit1);
            number2 = new Pattern(digit2);
            number3 = new Pattern(digit3);
            subticket = null;
        }

        Pattern IPick.this[int i] => this[i];
        internal Pattern this[int i]
        {
            get
            {
                if (i == 1) return number1;
                if (i == 2) return number2;
                if (i == 3) return number3;
                throw new GameEngineException($"{nameof(Pick3)} does not have index {i}");
            }
        }

        IEnumerable<IPick> IPick.Permute() => Permute();
        internal IEnumerable<IPick> Permute()
        {
            if (Count == 1)
            {
                var result = new HashSet<IPick>();
                result.Add(new Pick3(number1.SingleDigit, number2.SingleDigit, number3.SingleDigit));
                result.Add(new Pick3(number1.SingleDigit, number3.SingleDigit, number2.SingleDigit));
                result.Add(new Pick3(number2.SingleDigit, number1.SingleDigit, number3.SingleDigit));
                result.Add(new Pick3(number2.SingleDigit, number3.SingleDigit, number1.SingleDigit));
                result.Add(new Pick3(number3.SingleDigit, number2.SingleDigit, number1.SingleDigit));
                result.Add(new Pick3(number3.SingleDigit, number1.SingleDigit, number2.SingleDigit));
                return result;
            }
            else
            {
                var result = new List<IPick>();
                foreach (var subticket in SubTickets())
                {
                    result.AddRange(subticket.Permute());
                }
                return result;
            }
            
        }

        internal bool IsBoxed(int digit1, int digit2, int digit3)
        {
            bool result =
                IsMarked(digit1, digit2, digit3) ||
                IsMarked(digit1, digit3, digit2) ||

                IsMarked(digit2, digit1, digit3) ||
                IsMarked(digit2, digit3, digit1) ||

                IsMarked(digit3, digit2, digit1) ||
                IsMarked(digit3, digit1, digit2);
            return false;
        }

        internal bool IsMarked(int digit1, int digit2, int digit3)
        {
            bool result = number1.IsMarked(digit1) && number2.IsMarked(digit2) && number3.IsMarked(digit3);
            return result;
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            if (Count == 1)
            {
                result.Add(this);
            }
			else
			{
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        foreach (int digit3 in number3.Digits())
                        {
                            result.Add(new Pick3(digit1, digit2, digit3));
                        }
                    }
                }
            }                
            return result;
        }

        private SubTicket<IPick> subticket;
        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            if (Count == 1)
            {
                if (subticket == null) subticket = new SubTicket<IPick>(this);
                result.Add(subticket);
            }
			else
			{
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        foreach (int digit3 in number3.Digits())
                        {
                            result.Add(new SubTicket<IPick>(digit1, digit2, digit3));
                        }
                    }
                }
            }   
            return result;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is Pick3)) return false;
            Pick3 other = (Pick3)obj;
            return number1.Equals(other.number1) && number2.Equals(other.number2) && number3.Equals(other.number3);
        }

        public override int GetHashCode()
        {
            return this.number1.SingleDigit * 100 + this.number2.SingleDigit * 10 + this.number3.SingleDigit;
        }

        int IPick.Length => Length;
        public int Length
        {
            get
            {
                return 3;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return number1.Count * number2.Count * number3.Count;
            }
        }
        
        string IPick.AsString() => AsString();
        internal string AsString()
        {
            return $"[{number1},{number2},{number3}]";
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            return $"{number1.AsStringForAccounting()}-{number2.AsStringForAccounting()}-{number3.AsStringForAccounting()}";
        }

        public long ToInt64()
        {
            if (number1.Count != 1 || number2.Count != 1 || number3.Count != 1) throw new GameEngineException($"Pick {nameof(Pick3)} cannot be converted to Int32");
            return this.number1.SingleDigit * 100 + this.number2.SingleDigit * 10 + this.number3.SingleDigit;
        }
    }
}
