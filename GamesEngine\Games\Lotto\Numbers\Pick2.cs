﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lotto
{
    public struct Pick2 : IPick
    {
        private bool isEndingNumber;
        private readonly Pattern number1;
        private readonly Pattern number2;

        public static string SPLIT_BEGINNING = "-";

        private Pick2(Pattern number1, Pattern number2)
        {
            this.number1 = number1;
            this.number2 = number2;
            this.subticket = null;
            this.isEndingNumber = true;
        }

        public Pick2(string number1, string number2) :
            this(new <PERSON><PERSON>(number1), new <PERSON>tern(number2))
        { }

        public Pick2(int number1, int number2) :
            this(new <PERSON><PERSON>(number1), new Pattern(number2))
        { }

        public Pick2(string sequenceOfNumbers) :
            this(sequenceOfNumbers, true)
        { }

        public Pick2(string sequenceOfNumbers, bool isEndingNumber)
        {
            if (sequenceOfNumbers == null) throw new ArgumentNullException(nameof(sequenceOfNumbers));
            if (sequenceOfNumbers.Length != 2) throw new GameEngineException($"Number '{sequenceOfNumbers}' is not a valid {nameof(Pick2)} Lottery number");
            char digit1 = sequenceOfNumbers[0];
            char digit2 = sequenceOfNumbers[1];
            number1 = new Pattern(digit1);
            number2 = new Pattern(digit2);
            subticket = null;
            this.isEndingNumber = isEndingNumber;
        }

        Pattern IPick.this[int i] => this[i];
        internal Pattern this[int i]
        {
            get
            {
                if (i == 1) return number1;
                if (i == 2) return number2;
                throw new GameEngineException($"{nameof(Pick2)} does not have index {i}");
            }
        }

        IEnumerable<IPick> IPick.Permute() => Permute();
        internal IEnumerable<IPick> Permute()
        {
            if (Count == 1) 
            {
                var result = new HashSet<IPick>();
                result.Add(new Pick2(number1.SingleDigit, number2.SingleDigit));
                result.Add(new Pick2(number2.SingleDigit, number1.SingleDigit));
                return result;
            }
            else
            {
                var result = new List<IPick>();
                foreach (var subticket in SubTickets())
                {
                    result.AddRange(subticket.Permute());
                }
                return result;
            }
        }

        internal bool IsBoxed(int digit1, int digit2)
        {
            bool result =
                IsMarked(digit1, digit2) ||
                IsMarked(digit2, digit1);
            return false;
        }

        internal bool IsMarked(int digit1, int digit2)
        {
            bool result = number1.IsMarked(digit1) && number2.IsMarked(digit2);
            return result;
        }

        IEnumerable<IPick> IPick.Split() => Split();
        public IEnumerable<IPick> Split()
        {
            List<IPick> result = new List<IPick>();
            if(Count == 1)
			{
                result.Add(this);
			}
			else
			{
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        result.Add(new Pick2(digit1, digit2));
                    }
                }
            }            
            return result;
        }

        private SubTicket<IPick> subticket;
        IEnumerable<SubTicket<IPick>> IPick.SubTickets() => SubTickets();
        public IEnumerable<SubTicket<IPick>> SubTickets()
        {
            var result = new List<SubTicket<IPick>>();
            if(Count == 1)
			{
                if (subticket == null) subticket = new SubTicket<IPick>(this);
                result.Add(subticket);
			}
			else
			{
                foreach (int digit1 in number1.Digits())
                {
                    foreach (int digit2 in number2.Digits())
                    {
                        result.Add(new SubTicket<IPick>(digit1, digit2));
                    }
                }
            }
            return result;
        }

        public override bool Equals(object obj)
        {
            if (!(obj is Pick2)) return false;
            Pick2 other = (Pick2)obj;
            return number1.Equals(other.number1) && number2.Equals(other.number2) && isEndingNumber.Equals(other.isEndingNumber);
        }

		public override int GetHashCode()
		{
			return this.number1.SingleDigit * 10 + this.number2.SingleDigit;
        }

		int IPick.Length => Length;
        public int Length
        {
            get
            {
                return 2;
            }
        }

        int IPick.Count => Count;
        public int Count
        {
            get
            {
                return number1.Count * number2.Count;
            }
        }
        
        string IPick.AsString() => AsString();
        internal string AsString()
        {
            return $"[{number1},{number2}]";
        }

        string IPick.AsStringForAccounting() => AsStringForAccounting();
        internal string AsStringForAccounting()
        {
            return $"{number1.AsStringForAccounting()}-{number2.AsStringForAccounting()}";
        }

        public long ToInt64()
        {
            if (number1.Count != 1 || number2.Count != 1) throw new GameEngineException($"Pick {nameof(Pick2)} cannot be converted to Int32");
            return this.number1.SingleDigit * 10 + this.number2.SingleDigit;
        }
    }
}
