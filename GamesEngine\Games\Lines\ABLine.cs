﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.Logs;
using System;

namespace GamesEngine.Games.Lines
{
	internal abstract class ABLine : Line
	{
		private readonly int teamAReward;
		private readonly int teamBReward;
		private readonly double teamACoefficientReward;
		private readonly double teamBCoefficientReward;
		protected ABAnswer TeamAAnswer { get; }
		protected ABAnswer TeamBAnswer { get; }
		private readonly ScorePair realAnswer;
		protected GameFavorite Favorite { get; }

		internal ABLine(int lineId, Question question, Tier tier, Game game, GameFavorite favorite, Shelve shelve, int index, int teamAReward, int teamBReward, string who, DateTime now) : base(lineId, question, tier, game, shelve, index, who, now)
		{
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException($"Line can not be placed because game {game.ToString()} already started");
			ValidateReward(teamAReward, teamBReward);
			if (favorite == null) throw new ArgumentNullException(nameof(favorite));

			this.teamAReward = teamAReward;
			this.teamBReward = teamBReward;
			this.teamACoefficientReward = CalculateRewardCoefficient(game.TeamA);
			this.teamBCoefficientReward = CalculateRewardCoefficient(game.TeamB);
			this.TeamAAnswer = new ABAnswer(game, game.TeamA);
			this.TeamBAnswer = new ABAnswer(game, game.TeamB);
			this.Favorite = favorite;
			this.realAnswer = new ScorePair(game);

			LogRewards();
		}

		protected ABLine(ABLine previousVersion, int teamAReward, int teamBReward, string who, DateTime now) : base(previousVersion, who, now)
		{
			if (game.IsInPlay() && this.Tier != Tier.TIER_TWO) throw new GameEngineException($"Line can not be placed because game {game.ToString()} already started");
			ValidateReward(teamAReward, teamBReward);

			this.teamAReward = teamAReward;
			this.teamBReward = teamBReward;
			this.teamACoefficientReward = CalculateRewardCoefficient(game.TeamA);
			this.teamBCoefficientReward = CalculateRewardCoefficient(game.TeamB);
			this.TeamAAnswer = previousVersion.TeamAAnswer;
			this.TeamBAnswer = previousVersion.TeamBAnswer;
			this.Favorite = previousVersion.Favorite;
			this.realAnswer = previousVersion.realAnswer;

			LogRewards(previousVersion.TeamAReward, previousVersion.TeamBReward);
		}

		protected override double CoefficientReward(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			ABAnswer abAnswer = (ABAnswer)answer;
			if (abAnswer.ChosenTeam == game.TeamA)
			{
				return this.teamACoefficientReward;
			}
			else if (abAnswer.ChosenTeam == game.TeamB)
			{
				return this.teamBCoefficientReward;
			}

			throw new GameEngineException($"Team {abAnswer.ChosenTeam.Name} is unknown");
		}

		internal override string RewardAsString(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			ABAnswer abAnswer = (ABAnswer)answer;
			int result;
			if (abAnswer.ChosenTeam == game.TeamA)
			{
				result = this.teamAReward;
			}
			else if (abAnswer.ChosenTeam == game.TeamB)
			{
				result = this.teamBReward;
			}
			else
			{
				throw new GameEngineException($"Team {abAnswer.ChosenTeam.Name} is unknown");
			}

			return (result > 0 ? "+" : "-") + result;
		}

		private double CalculateRewardCoefficient(Team team)
		{
			double result = 0;
			if (team == game.TeamA)
			{
				if (this.teamAReward < 0)
				{
					result = 100 / (double)Math.Abs(this.teamAReward);
				}
				else
				{
					result = Math.Abs(this.teamAReward) / 100.0;
				}
			}
			else if (team == game.TeamB)
			{
				if (this.teamBReward < 0)
				{
					result = 100 / (double)Math.Abs(this.teamBReward);
				}
				else
				{
					result = Math.Abs(this.teamBReward) / 100.0;
				}
			}
			else
			{
				throw new GameEngineException($"Team {team.Name} is unknown");
			}

			if (result == 0) throw new GameEngineException($"Prize reward for a {nameof(Line)} can not be zero.");

			return result;
		}

		internal int ScoreTeamA()
		{
			var original = (ABLine)this.OriginalVersion;
			if (original.realAnswer == null) throw new GameEngineException("Real answer has not been set yet");
			if (!original.realAnswer.IsUsingPoints()) throw new GameEngineException("Score must be set in points");

			int result = original.realAnswer.ScoreTeamA.Points;
			return result;
		}

		internal int ScoreTeamB()
		{
			var original = (ABLine)this.OriginalVersion;
			if (original.realAnswer == null) throw new GameEngineException("Real answer has not been set yet");
			if (!original.realAnswer.IsUsingPoints()) throw new GameEngineException("Score must be set in points");

			int result = original.realAnswer.ScoreTeamB.Points;
			return result;
		}

		internal void SetRealAnswer(Team teamA, int scoreA, Team teamB, int scoreB)
		{
			if (teamA == null) new ArgumentNullException(nameof(teamA));
			if (teamB == null) new ArgumentNullException(nameof(teamB));
			if (this.TeamAAnswer.ChosenTeam != teamA || this.TeamBAnswer.ChosenTeam != teamB) throw new GameEngineException("The team is not valid for this line.");
			if (scoreA == scoreB && this.Tier == Tier.TIER_ONE && !Game.Sport.AllowsTieResults()) throw new GameEngineException($"Sport {Game.Sport.Name} does not allow tie results");
			if (scoreA < 0) throw new GameEngineException($"{teamA.Name}'s score can not be negative");
			if (scoreB < 0) throw new GameEngineException($"{teamB.Name}'s score can not be negative");
			if (!this.IsLastVersion()) throw new GameEngineException("Answer should be set only for the original line");
			if (!game.ScoreIncludes(teamA, scoreA, teamB, scoreB)) throw new GameEngineException($"Game score has never been set as {scoreA} / {scoreB}");
			
			this.realAnswer.ScoreTeamA = Score.WithPoints(scoreA);
			this.realAnswer.ScoreTeamB = Score.WithPoints(scoreB);

			if (scoreA == scoreB && this is MoneyLine)
			{
				this.ChangeToNoAction();
			}
			else if (this is SpreadLine line)
			{
				var realSpread = Math.Abs(scoreA - scoreB);
				if (line.Spread == realSpread) this.ChangeToNoAction();
			}
		}

		internal WagerAnswer GetTeamAnswer(Team choosenTeam)
		{
			if (choosenTeam == this.TeamAAnswer.ChosenTeam) return this.TeamAAnswer;
			if (choosenTeam == this.TeamBAnswer.ChosenTeam) return this.TeamBAnswer;

			throw new GameEngineException($"Choosen team {choosenTeam.Name} is not a participant of the game {this.game.ToString()}");
		}

		private void LogRewards()
		{
			Log.AppendAtTheEndOfTheLastEntry($"Rewards: Team A : {teamAReward}, Team B : {teamBReward}");
		}

		private void LogRewards(int previousTeamAReward, int previousTeamBReward)
		{
			var log = OriginalVersion.Log;
			log.AppendAtTheEndOfTheLastEntry($"Previous rewards: Team A : {previousTeamAReward}, Team B : {previousTeamBReward}. New rewards: Team A : {teamAReward}, Team B : {teamBReward}");
		}

		internal int TeamAReward
		{
			get
			{
				return this.teamAReward;
			}
		}

		internal int TeamBReward
		{
			get
			{
				return this.teamBReward;
			}
		}

		protected ScorePair RealAnswer
		{
			get
			{
				var original = (ABLine)this.OriginalVersion;
				if (original.realAnswer == null) throw new GameEngineException("Real answer has not been set yet");

				return original.realAnswer;
			}
		}

		protected Team TeamA
		{
			get
			{
				return this.TeamAAnswer.ChosenTeam;
			}
		}

		protected Team TeamB
		{
			get
			{
				return this.TeamBAnswer.ChosenTeam;
			}
		}
	}
}
