﻿using Connectors.town.connectors.driver.transactions;
using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;

namespace Connectors.town.connectors.drivers.hades
{
	public class Refund : ASIProcessorDriver
	{
		private const float VERSION = 1.0F;
		private RestClient _postTransactionClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;

        public Refund()
			: base(TransactionType.Refund, VERSION)
		{
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
			if (_postTransactionClient == null)
			{
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;

				_postTransactionClient = new RestClient(companyBaseUrlServices);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				string companyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_postTransactionClient = new RestClient(companyBaseUrlServices);
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			CustomSetting CustomerId = recordSet.Mappings["customerId"];
			CustomSetting Amount = recordSet.Mappings["amount"];
			CustomSetting DescriptionField = recordSet.Mappings["description"];
			CustomSetting AdditionalInfo = recordSet.Mappings["additionalInfo"];

			var authorizationNumber = RefundAmount(CustomerId.AsString, Amount.AsDecimal, DescriptionField.AsString, now, AdditionalInfo.AsString);
			var result = new RefundTransaction(authorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(result, typeof(T));

		}

		private int RefundAmount(string customerId, decimal amount, string descriptionField, DateTime now, string additionalInfo)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (amount <= 0) throw new Exception($"Amount {amount} is not valid to send request {nameof(PostTransaction)}");
			if (String.IsNullOrWhiteSpace(descriptionField)) throw new ArgumentNullException(nameof(descriptionField));
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));

			Debug.WriteLine($"Accounting service {nameof(RefundAmount)} received {nameof(customerId)}:{customerId} {nameof(amount)}:{amount} {nameof(descriptionField)}:{descriptionField}");
			var code = PostTransaction(customerId, amount, descriptionField, "C", "C", now, additionalInfo);
			return code;
		}

        private int PostTransaction(string customerId, decimal amount, string descriptionField, string tranCode, string tranType, DateTime now, string additionalInfo)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (String.IsNullOrWhiteSpace(descriptionField)) throw new ArgumentNullException(nameof(descriptionField));
			if (String.IsNullOrWhiteSpace(tranCode)) throw new ArgumentNullException(nameof(tranCode));
			if (String.IsNullOrWhiteSpace(tranType)) throw new ArgumentNullException(nameof(tranType));
			if (now == default(DateTime)) throw new ArgumentNullException(nameof(now));

			var strTotal = amount.ToString();
			var values = new PostTransactionBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				CustomerId = customerId,
				Amount = strTotal,
				TranCode = tranCode,
				TranType = tranType,
				Description = descriptionField,
				BettingAdjustmentFlagYN = "",
				DailyFigureDate_YYYYMMDD = now.ToString("yyyyMMdd")
			};

			const string url = "/v1/5dimesAPI/PostTransaction";
			var jsonString = Commons.ToJson(values);

			string responseString = "";
			int retryNumber = 0;

			string valuesWithHiddenFields = ASIJsonUtils.HideSensitiveData(values, Loggers.GetIntance().AccountingServicesASIPostTransaction);

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIPostTransaction.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					var request = new RestRequest(url, Method.Post);
					request.AddHeader("Content-Type", "application/json");
					request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
					var response = _postTransactionClient.Execute(request);
					responseString = response.Content;

					Loggers.GetIntance().AccountingServicesASIPostTransaction.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIPostTransaction.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
					InternalOnError(nameof(PostTransaction), retryNumber, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", $"Additional Info: {additionalInfo}", extraErrorMessage);

					Thread.Sleep(5000);
					if (retryNumber == MAX_RETRIES)
					{
						return FAKE_DOCUMENT_NUMBER;
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(PostTransaction), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nAdditional Info: {additionalInfo}", $"Response can not be empty");
				return FAKE_DOCUMENT_NUMBER;
			}
			else
			{
				var errorResponse = Commons.FromJson<JsonErrorResponse>(responseString);
				if (errorResponse != null)
				{
					NotifyWarn(nameof(PostTransaction), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nAdditional Info: {additionalInfo}", $"Response threw an error");
					return FAKE_DOCUMENT_NUMBER;
				}
				else
				{
					var documentNumber = Convert.ToInt32(responseString);
					if (documentNumber <= 0)
					{
						NotifyWarn(nameof(PostTransaction), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}\nAdditional Info: {additionalInfo}", $"{nameof(documentNumber)} {documentNumber} is not valid");
						return FAKE_DOCUMENT_NUMBER;
					}
					return documentNumber;
				}
			}
		}

		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
        {
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("amount");
			CustomSettings.AddVariableParameter("description");
			CustomSettings.AddVariableParameter("additionalInfo");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
			SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
			ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
		}
    }
}
