﻿using GamesEngine;
using GamesEngine.RealTime.RestAPI;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Linq;

namespace GamesEngineTests.Unit_Tests.Business
{

    [TestClass]
    public class ServiceMonitorTest
    {
        [TestMethod]
        public void ServicesTest()
        {
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");

            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");

            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);
        }


        [TestMethod]
        public void AddNewConsumer()
        {
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");

            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);
            subscriberId = proxyEndpoint.NextSubscriberId();
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            Assert.IsTrue(proxyEndpoint.Subscribers.Count() == 2);
        }


        [TestMethod]
        public void ShowConsumersFromSepecificPublicService()
        {
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");
            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            subscriberId = proxyEndpoint.NextSubscriberId();
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            Assert.IsTrue(proxyEndpoint.Subscribers.Count() == 2);

            Assert.IsTrue(proxyEndpoint.Subscribers.Any(c => c.Organization == "TestEntindadBancaria"));
            Assert.IsTrue(proxyEndpoint.Subscribers.Any(c => c.Organization == "BGPAN"));

            Assert.IsTrue(monitor.Find("Deposits") != null);
        }


        [TestMethod]
        public void UpdateSecretFromConsumer()
        {
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");
            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            subscriberId = proxyEndpoint.NextSubscriberId();
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            var newSecret = proxyEndpoint.GenerateSecret();
            EndpointSubscriber currentEntity = proxyEndpoint.Subscribers.FirstOrDefault((x)=>x.Id == x.Id);
            currentEntity.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", newSecret);

            Assert.IsTrue(currentEntity.Secret != entidad.Secret, "The secret should be updated correctly.");
        }

        [TestMethod]
        public void DisabledEndpointConsumer()
        {
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");
            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            subscriberId = proxyEndpoint.NextSubscriberId();
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            var currentSubscriber = proxyEndpoint.FindSubscriber(entidad.Id);
            currentSubscriber.Disable(itIsThePresent, DateTime.Now,"Juan", "Cause he wants");
            Assert.IsTrue(!currentSubscriber.Enabled);

        }

        [TestMethod]
        public void EnabledEndpointConsumer()
        {
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");
            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "TestEntindadBancaria", secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            subscriberId = proxyEndpoint.NextSubscriberId();
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            var currentSubscriber = proxyEndpoint.FindSubscriber(entidad.Id);

            Assert.ThrowsException<GameEngineException>(() => {
                currentSubscriber.Enable(itIsThePresent, DateTime.Now, "Juan", "Cause he wants");
            });
        }


        [TestMethod]
        public void RemoveConsumer()
        {
            bool itIsThePresent = false;
            DateTime now = DateTime.Now;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/retiros", "localhost");
            var subscriberId = proxyEndpoint.NextSubscriberId();
            //Esto es para Upgrades, es una variable que existe en el diario.
            string organizacion = "TestEntindadBancaria";
            string secret = proxyEndpoint.GenerateSecret();
            var entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, organizacion, secret, "description", "admin");
            //Eval('consumerDeposit.Secret=''+consumerDeposit.GenerateSecret()+'';'); => consumerDeposit.Secret='asdasd123';
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);
            subscriberId = proxyEndpoint.NextSubscriberId();

            organizacion = "BGPAN";
            secret = proxyEndpoint.GenerateSecret();
            entidad = proxyEndpoint.Add(itIsThePresent, subscriberId, now, "BGPAN", secret, "Access deposit", "admin");
            entidad.UpdateSecret(itIsThePresent, DateTime.Now, "Juan", secret);

            var currentSubscriber = proxyEndpoint.FindSubscriber(entidad.Id);
            proxyEndpoint.RemoveEndpointSubscriber(itIsThePresent, currentSubscriber.Id);

            Assert.IsTrue(proxyEndpoint.Subscribers.Count() == 1);
        }


        [TestMethod]
        public void ShowProxyEndpoints()
        {
            bool itIsThePresent = false;
            var monitor = GamesEngine.RealTime.RestAPI.EndpointServiceMonitor.Instance();

            var proxyEndpoint = monitor.Add(itIsThePresent, "Deposits", "/town/2/liquidity/v1/deposits", "localhost");

            foreach (var endpoint in monitor.ProxyAccessEndpoints)
            {
                
                foreach (var subscriber in endpoint.Subscribers)
                {
                    Console.WriteLine($"  Subscriber: {subscriber.Organization}, Secret: {subscriber.Secret}");
                }
            }
        }

    }
}
