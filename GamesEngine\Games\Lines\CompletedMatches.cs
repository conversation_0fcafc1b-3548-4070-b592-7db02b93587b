﻿using GamesEngine.Domains;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedMatches:Objeto
    {
        private Dictionary<int, CompletedScoreGame> matches = new Dictionary<int, CompletedScoreGame>();

        internal IEnumerable<CompletedScoreGame> GetAll => matches.Values.ToList();

        internal bool Contains(CompletedScoreGame game)
        {
            return matches.ContainsKey(game.GameId);
        }

        internal void Add(CompletedScoreGame game)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (matches.ContainsKey(game.GameId)) throw new GameEngineException($"Match was already added with {nameof(game.GameId)} '{game.GameId}'");

            matches.Add(game.GameId, game);
        }

        internal void UpdateTotals(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);

            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
        }

        HashSet<string> accounts;

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }

    }
}
