﻿using GamesEngine.Business;
using System;
using System.Collections.Generic;
using static GamesEngine.Business.WholePaymentProcessor;

namespace GamesEngine.Finance
{
    internal class CascadePaymentProcessor
    {
        private IEnumerator<PaymentProcessor> listPaymentProcessor;

        public CascadePaymentProcessor(IEnumerable<PaymentProcessor> listPaymentProcessor)
        {
            this.listPaymentProcessor = listPaymentProcessor.GetEnumerator();
            SortProcessors();
        }

        public PaymentProcessor Next()
        {
            if (listPaymentProcessor.MoveNext())
            {
                return listPaymentProcessor.Current;
            }
            else
            {
                return null;
            }
        }

        internal void SortProcessors()
        {
            // Agregar un mensaje para el debugger que indique que se está ordenando la lista de procesadores esta pendiente de programar
            System.Diagnostics.Debugger.Break();
        }
    }
}