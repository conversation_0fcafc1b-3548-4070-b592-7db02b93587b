﻿using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class CompletedScoreGame: Objeto
    {
        internal int TournamentId { get; }
        internal int GameId { get; }
        internal int DomainId { get; }
        internal string TeamAName { get; }
        internal string TeamBName { get; }

        string teamAShortName;
        internal string TeamAShortName
        {
            get 
            { 
                return teamAShortName; 
            }
            set 
            {
                if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException($"{nameof(teamAShortName)} cannot be assigned as null");
                teamAShortName = value; 
            }
        }

        string teamBShortName;
        internal string TeamBShortName
        {
            get
            {
                return teamBShortName;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException($"{nameof(teamBShortName)} cannot be assigned as null");
                teamBShortName = value;
            }
        }

        internal DateTime StartDate { get; }
        internal DateTime EndDate { get; }
        internal int ScoreTeamA { get; }
        internal int ScoreTeamB { get; }

        string gradedBy;
        internal string GradedBy
        {
            get
            {
                return gradedBy;
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException($"{nameof(gradedBy)} cannot be assigned as null");
                gradedBy = value;
            }
        }

        DateTime gradedDate;
        internal DateTime GradedDate
        {
            get
            {
                return gradedDate;
            }
            set
            {
                if (value == default(DateTime)) throw new GameEngineException($"{nameof(gradedDate)} cannot be assigned as null");
                gradedDate = value;
            }
        }

        internal string LeagueName { get; }
        internal string SportName { get; }
        Dictionary<int, CompletedLine> lines;
        CompletedMatches matches;

        public CompletedScoreGame(CompletedMatches matches, int tournamentId, int gameId, string teamAName, string teamBName, int scoreTeamA, int scoreTeamB, string leagueName, string sportName, DateTime startDate)
        {
            if (matches == null) throw new ArgumentNullException(nameof(matches));
            if (tournamentId <= 0) throw new GameEngineException($"{nameof(tournamentId)} must be greater than 0");
            if (gameId <= 0) throw new GameEngineException($"{nameof(gameId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(teamAName)) throw new ArgumentNullException(nameof(teamAName));
            if (string.IsNullOrWhiteSpace(teamBName)) throw new ArgumentNullException(nameof(teamBName));
            if (scoreTeamA < 0) throw new GameEngineException($"{nameof(scoreTeamA)} must be greater or equal than 0");
            if (scoreTeamB < 0) throw new GameEngineException($"{nameof(scoreTeamB)} must be greater or equal than 0");
            if (string.IsNullOrWhiteSpace(leagueName)) throw new ArgumentNullException(nameof(leagueName));
            if (string.IsNullOrWhiteSpace(sportName)) throw new ArgumentNullException(nameof(sportName));
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            this.matches = matches;
            TournamentId = tournamentId;
            GameId = gameId;
            TeamAName = teamAName;
            TeamBName = teamBName;
            ScoreTeamA = scoreTeamA;
            ScoreTeamB = scoreTeamB;
            LeagueName = leagueName;
            SportName = sportName;
            StartDate = startDate;
        }

        internal bool Contains(CompletedLine line)
        {
            if (lines == null) return false;
            return lines.ContainsKey(line.LineId);
        }

        internal void Add(CompletedLine line)
        {
            if (line == null) throw new ArgumentNullException(nameof(line));
            if (lines == null) lines = new Dictionary<int, CompletedLine>();
            if (lines.ContainsKey(line.LineId)) throw new GameEngineException($"Line was already added with {nameof(line.LineId)} '{line.LineId}'");

            lines.Add(line.LineId, line);
        }

        internal void UpdateTotals(CompletedWager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);
            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin : 0;
            TotalPrize += toWin;
            TotalProfit += wager.Risk - toWin;
            matches.UpdateTotals(wager);
        }

        HashSet<string> accounts;

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }

    }
}
