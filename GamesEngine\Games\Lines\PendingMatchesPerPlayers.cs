﻿using GamesEngine.Domains;
using GamesEngine.Gameboards.Lines;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Games.Lines
{
    [Puppet]
    class PendingMatchesPerPlayers : Objeto
    {
        readonly List<PendingMatchPerPlayer> matchPerPlayers = new List<PendingMatchPerPlayer>();

        internal IEnumerable<PendingMatchPerPlayer> GetAll => matchPerPlayers;

        internal bool Exists(Game game, Domain domain, string accountNumber)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = matchPerPlayers.Exists(match => match.Game == game && match.AccountNumber == accountNumber && match.Domain == domain);
            return result;
        }

        internal PendingMatchPerPlayer Find(Game game, Domain domain, string accountNumber)
        {
            if (game == null) throw new ArgumentNullException(nameof(game));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));

            var result = matchPerPlayers.Find(match => match.Game == game && match.AccountNumber == accountNumber && match.Domain == domain);
            if (result == null) throw new GameEngineException($"No match with {nameof(accountNumber)} '{accountNumber}' and {nameof(domain)} '{domain.Url}'");
            return result;
        }

        internal void Add(PendingMatchPerPlayer match)
        {
            if (match == null) throw new ArgumentNullException(nameof(match));
            if (Exists(match.Game, match.Domain, match.AccountNumber)) throw new GameEngineException($"Match was already added for this {nameof(match.AccountNumber)} '{match.AccountNumber}'");

            matchPerPlayers.Add(match);
        }

        internal void Add(IEnumerable<PendingMatchPerPlayer> pendingMatches)
        {
            if (!pendingMatches.Any()) throw new GameEngineException($"{nameof(pendingMatches)} cannot be empty");

            foreach (var match in pendingMatches)
            {
                Add(match);
            }
        }

        internal void UpdateTotals(Wager wager)
        {
            if (wager == null) throw new ArgumentNullException(nameof(wager));
            if (accounts == null) accounts = new HashSet<string>();

            accounts.Add(wager.AccountNumber);
            TotalWagers++;
            TotalTicketAmount += wager.Risk;
            var toWin = wager.IsWinner() ? wager.ToWin() : 0;
            TotalPrize += toWin;
            if (wager.IsWinner() || wager.IsLoser()) TotalProfit += wager.Risk - toWin;
        }

        HashSet<string> accounts = new HashSet<string>();

        internal IEnumerable<string> Accounts
        {
            get
            {
                return accounts;
            }
        }

        internal int TotalAccounts => accounts == null ? 0 : accounts.Count;

        internal int TotalWagers { get; private set; }

        internal decimal TotalTicketAmount { get; private set; }

        internal decimal TotalPrize { get; private set; }

        internal decimal TotalProfit { get; private set; }
    }
}
