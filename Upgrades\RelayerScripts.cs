﻿using GamesEngine.Custodian;
using GamesEngine.Finance;
using GamesEngine.Settings;
using GamesEngine;
using Microsoft.Extensions.Configuration;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Upgrades
{
    internal class RelayerScripts
    {
        private static RelayerActor actor;

        private static void InitSettings()
        {
            var builder = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .AddJsonFile("customization/appsettings.accounting.json", optional: true)
            .AddJsonFile("customization/appsettings.security.json", optional: true)
            .AddJsonFile("customization/appsettings.kafka.json", optional: true)
            .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
            .AddJsonFile("customization/appsettings.apm.json", optional: true)
            .AddJsonFile("secrets/appsettings.secrets.json", optional: true);

            var config = builder.Build();
            var sectionDairy = config.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            actor = new RelayerActor();
            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                Integration.DbDairy.MySQL = mySQL;
                Console.WriteLine("Starting to recover state from dairy");
                actor.EventSourcingStorage(DatabaseType.MySQL, mySQL, "", "PRODUCTION_DOES_NOT_NEED_IT");
                Console.WriteLine("State has been already recovered");
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                Integration.DbDairy.SQLServer = sqlServer;
                Console.WriteLine("Starting to recover state from dairy");
                actor.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, "", "PRODUCTION_DOES_NOT_NEED_IT");
                Console.WriteLine("State has been already recovered");
            }
        }

        public static string CurrentVersion()
        {
            if (actor == null) InitSettings();
            try
            {
                var result = actor.PerformQry($@"
                    {{
                        currVersion = GameEngineVersion();
                        print currVersion.Version version;
                    }}
                ", IpAddress.DEFAULT, UserInLog.ANONYMOUS);
                result = result.Split("\"")[3];
                return result;
            }
            catch
            {
                return "NEW";
            }
        }

        public static void FreshInstallation()
        {
            var transactionTypesAsText = RiskAssignmentModel.TransactionTypesAsText();
            if (actor == null) InitSettings();
            var x = actor.PerformCmd($@"
                gameEngineVersion('1.0');

                company = Company();
                companyIDf2401f84xa94ax4db3xaec4x83f5361730ae = company;

            ", IpAddress.DEFAULT, UserInLog.ANONYMOUS);
        }

        public static void Upgrade_From_Ver_1_0_To_Ver_2_0()
        {
            if (actor == null) InitSettings();
            var x = actor.PerformCmd($@"
                gameEngineVersion('2.0');
                company.System.ServiceMonitor.Add(itIsThePresent, 'Deposits', '/v1/Depositos', 'localhost');
                company.System.ServiceMonitor.Add(itIsThePresent, 'Withdrawal', '/v1/Withdrawal', 'localhost');

            ", IpAddress.DEFAULT, UserInLog.ANONYMOUS);
        }
    }
}
