﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static town.connectors.CustomSettings;

namespace Connectors.town.connectors.drivers.hades
{
	public class Fragment_an_Authorization : ASITenantDriver, IFragmentDriver
	{
		private RestClient _postFreeFormWagerCollectionClient;
		private string SystemId;
        private string SystemPassword;
        private string ClerkId;
        public string CompanyBaseUrlServices { get; private set; }

		public int _amountOfWagerPerChunk = 500;
		public int AmountOfWagersPerChunk
		{
			get
			{
				return _amountOfWagerPerChunk;
			}
			set
			{
				if (value < 1)
				{
					throw new Exception($@"_amountOfWagerPerChunk must be greater than 1. ");
				}
				_amountOfWagerPerChunk = value;
			}
		}

		public Fragment_an_Authorization() : base(Tenant_Actions.Fragment, TransactionType.RetrieveInfo, "USD")
        {
		}
		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("customerId");
			CustomSettings.AddVariableParameter("ticketNumber");
			CustomSettings.AddVariableParameter("wagers");

			//CustomSettings.Prepare();

			CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			if (_postFreeFormWagerCollectionClient == null)
			{
				_postFreeFormWagerCollectionClient = new RestClient(CompanyBaseUrlServices);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				CompanyBaseUrlServices = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				if (changeApplied)
				{
					_postFreeFormWagerCollectionClient = new RestClient(CompanyBaseUrlServices);
				}

				SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
				SystemPassword = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
				ClerkId = CustomSettings.Get(now, "CompanyClerkId").AsString;
			}

			var CustomerId = recordSet.Mappings["customerId"];
			var TicketNumber = recordSet.Mappings["ticketNumber"];
			var Wagers = recordSet.Mappings["wagers"];

			string customerId = CustomerId.AsString;
			int ticketNumber = TicketNumber.AsInt;
			PostFreeFormWager[] wagers = Wagers.As<PostFreeFormWager[]>();

			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(CustomerId));
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Length == 0) throw new Exception($"At least one wager is required to send request {nameof(PostFreeFormWagerCollection)} TicketNumber: {ticketNumber} CustomerId: {customerId} on RegisterWagers");

			Debug.WriteLine($"Accounting service {nameof(PostFreeFormWagerCollection)} received {nameof(customerId)}:{customerId} {nameof(ticketNumber)}:{ticketNumber} {nameof(wagers)}:{wagers}");

			var result = PostFreeFormWagerCollection(now, customerId, ticketNumber, wagers);
			return (T)Convert.ChangeType(result, typeof(T));

		}
		public override Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {
            throw new NotImplementedException();
        }
		public PostFreeFormWagerCollectionSuccessResponse RegisterWagersWithoutPartitions(DateTime now, string customerId, int ticketNumber, PostFreeFormWager[] wagers)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Length == 0) throw new Exception($"At least one wager is required to send request {nameof(PostFreeFormWagerCollection)} TicketNumber: {ticketNumber} CustomerId: {customerId} on RegisterWagers");

			Debug.WriteLine($"Accounting service {nameof(PostFreeFormWagerCollection)} received {nameof(customerId)}:{customerId} {nameof(ticketNumber)}:{ticketNumber} {nameof(wagers)}:{wagers}");
			var result = PostFreeFormWagerChunckCollection(now, customerId, ticketNumber, wagers, 1);
			return result;
		}
		private PostFreeFormWagerCollectionSuccessResponse PostFreeFormWagerCollection(DateTime now, string customerId, int ticketNumber, PostFreeFormWager[] wagers)
		{
			int initialCounter = 0;
			int chunkSize = AmountOfWagersPerChunk;
			PostFreeFormWager[][] arrayOfChunks = wagers.GroupBy(s => initialCounter++ / chunkSize).Select(g => g.ToArray()).ToArray();


			List<PostFreeFormWager> bufferOfWagers = new List<PostFreeFormWager>();

			for (int i = 0; i < arrayOfChunks.Length; i++)
			{
				PostFreeFormWager[] chunck = arrayOfChunks[i];
				bool itsTheFirstIteration = i == 0;
				int initialWagerNumberIfSomethingFails = (chunkSize * i) + 1;

				PostFreeFormWagerCollectionSuccessResponse response = PostFreeFormWagerChunckCollection(now, customerId, ticketNumber, chunck, initialWagerNumberIfSomethingFails);

				bufferOfWagers.AddRange(response.Wagers);

			}

			return new PostFreeFormWagerCollectionSuccessResponse()
			{
				Wagers = bufferOfWagers.ToArray()
			};
		}
		private PostFreeFormWagerCollectionSuccessResponse PostFreeFormWagerChunckCollection(DateTime now, string customerId, int ticketNumber, PostFreeFormWager[] wagers, int initialWagerId)
		{
			if (String.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));
			if (wagers == null) throw new ArgumentNullException(nameof(wagers));
			if (wagers.Length == 0) throw new Exception($"At least one wager is required to send request {nameof(PostFreeFormWagerChunckCollection)} TicketNumber: {ticketNumber} CustomerId: {customerId}");

			bool qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub = NeedsUniqueIdentifierForPaymentHub != PRODUCTION_DOES_NOT_NEED_IT;
			if (qaAndStageNeedsToGenerateAnUniqueReferenceBecauseTheyUseSamePaymentHub)
			{
				foreach (var wager in wagers)
				{
					wager.ReferenceNumber = UniqueIdentifierPrefixForLottoReferenceId + wager.ReferenceNumber;
				}
			}

			var values = new PostFreeFormWagerCollectionBody()
			{
				SystemId = SystemId,
				SystemPassword = SystemPassword,
				ClerkId = ClerkId,
				CustomerId = customerId,
				TicketNumber = ticketNumber.ToString(),
				Wagers = new PostFreeFormWagerCollectionWagers() { Wagers = wagers.ToArray() }
			};
			const string url = "/v1/5dimesAPI/PostFreeFormWagerCollection";
			var jsonString = Commons.ToJson(values);

			string valuesWithHiddenFields = ASIJsonUtils.HideSensitiveData(values, Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection);

			if (ticketNumber <= 0)
			{
				NotifyError(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}", $"nRequest cannot be thrown for {nameof(ticketNumber)} {ticketNumber}");
				return EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, initialWagerId);
			}


			string responseString = "";
			int retryNumber = 0;
			const int MAX_RETRIES = 3;

			while (true)
			{
				try
				{
					Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection.Debug($@"url:{url} data:{valuesWithHiddenFields}");

					bool wasADeadlock = false;
					do
					{
						var request = new RestRequest(url, Method.Post);
						request.AddHeader("Content-Type", "application/json");
						request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
						var response = _postFreeFormWagerCollectionClient.Execute(request);
						responseString = response.Content;
						if (responseString != null && responseString.Contains("error") && responseString.Contains("was deadlocked on lock"))
						{
							wasADeadlock = true;
							NotifyWarn(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw a deadlock, retry in progress");
							Thread.Sleep(100);
						}
						else
						{
							wasADeadlock = false;
						}
					}
					while (wasADeadlock);


					Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection.Debug($@"response:{responseString}");

					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesASIPostFreeFormWagerCollection.Error($@"url:{url} data:{valuesWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;
					var extraErrorMessage = string.Empty;
					if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server";

					Thread.Sleep(1000);

					if (retryNumber == MAX_RETRIES)
					{
						InternalOnError(nameof(PostFreeFormWagerChunckCollection), retryNumber, e, $"Url:{url}", $"Request: {valuesWithHiddenFields}", $"Response: {responseString}", extraErrorMessage);

						return EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, initialWagerId);
					}
				}
			}

			if (String.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response can not be empty");
				return EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, initialWagerId);
			}
			else
			{
				var objectResponse = Commons.FromJson<PostFreeFormWagerCollectionResponse>(responseString);
				if (objectResponse.Error != null)
				{
					NotifyWarn(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Response threw an error");
					return EntitiesConverter.CreateFakePostFreeFormWagerCollectionResponse(wagers, initialWagerId);
				}
				if (objectResponse.Wagers == null)
				{
					NotifyWarn(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"{nameof(objectResponse.Wagers)} are null");
				}
				else if (objectResponse.Wagers.Length != wagers.Length)
				{
					NotifyWarn(nameof(PostFreeFormWagerChunckCollection), $"Url:{url}\nRequest: {valuesWithHiddenFields}\nResponse: {responseString}", $"Wagers count is not the same. Body has {wagers.Length} wagers and response has {objectResponse.Wagers.Length} wagers");
				}

				return new PostFreeFormWagerCollectionSuccessResponse()
				{
					Wagers = objectResponse.Wagers
				};

			}
		}

	}
}
