﻿using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using town.connectors;
using town.connectors.drivers;

namespace Connectors.town.connectors.drivers.consignment
{
    public class Name: ConsignmentTenantDriver
    {
        public Name() : base(Tenant_Actions.Others, TransactionType.RetrieveInfo, "USD")
        {

        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
                SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
                Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            }

            var result = await GetNameAsync(recordSet);
            return (T)Convert.ChangeType(result, typeof(T));
        }

        async Task<string> GetNameAsync(CustomSettings.RecordSet recordSet)
        {
            ResponseNameEnvelope responseXml = null;
            string responseString = string.Empty;
            string xmlWithHiddenFields = string.Empty;
            var url = $"{ServicesUrl}?op=getName";
            try
            {
                string xmlString = CreateSoapEnvelope(recordSet, out xmlWithHiddenFields);
                var webRequest = new HttpRequestMessage(HttpMethod.Post, url)
                {
                    Content = new StringContent(xmlString, Encoding.UTF8, "text/xml"),
                };

                Loggers.GetIntance().AccountingConsignmentGetName.Debug($@"url:{url} data:{xmlWithHiddenFields}");
                using (HttpResponseMessage response = await httpClient.SendAsync(webRequest, HttpCompletionOption.ResponseHeadersRead))
                {
                    responseString = await response.Content.ReadAsStringAsync();
                }
                Loggers.GetIntance().AccountingConsignmentGetName.Debug($@"response:{responseString}");

                XmlSerializer xmls = new XmlSerializer(typeof(ResponseNameEnvelope));
                responseXml = (ResponseNameEnvelope)xmls.Deserialize(new StringReader(responseString));
            }
            catch (Exception e)
            {
                Loggers.GetIntance().AccountingConsignmentGetName.Error($@"url:{url} data:{xmlWithHiddenFields} type:{e.GetType()} error:{e.Message}", e);

                var extraErrorMessage = string.Empty;
                InternalOnError(nameof(GetNameAsync), 1, e, $"Url:{url}", $"Request: {xmlWithHiddenFields}", $"Response: {responseString}");
            }

            if (responseXml == null || string.IsNullOrWhiteSpace(responseXml.Body.getNameResponse.getNameResult))
            {
                NotifyWarn(nameof(GetNameAsync), $"Url:{url}\nResponse: {responseString}", $"Response cannot be empty");
            }
            else
            {
                return responseXml.Body.getNameResponse.getNameResult;
            }

            return string.Empty;
        }

        string CreateSoapEnvelope(CustomSettings.RecordSet recordSet, out string xmlWithHiddenFields)
        {
            var amount = recordSet.Mappings["amount"].AsDecimal;
            var customerId = recordSet.Mappings["merchantId"].AsString;
            var sendersName = recordSet.Mappings["sendersName"].AsString;
            var reference = recordSet.Mappings["reference"].AsString;
            var provider = recordSet.Mappings["provider"].AsInt;

            var xmlBuilder = new StringBuilder();
            xmlBuilder.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilder.AppendLine("<soap:Body>");
            xmlBuilder.AppendLine("<getName xmlns=\"http://tempuri.org/\">");
            xmlBuilder.Append("<username>").Append(SystemId).AppendLine("</username>");
            xmlBuilder.Append("<password>").Append(Password).AppendLine("</password>");
            xmlBuilder.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilder.Append("<merchantId>").Append(customerId).AppendLine("</merchantId>");
            xmlBuilder.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilder.Append("<reference>").Append(reference).AppendLine("</reference>");
            xmlBuilder.Append("<provider>").Append(provider).AppendLine("</provider>");
            xmlBuilder.AppendLine("</getName>");
            xmlBuilder.AppendLine("</soap:Body>");
            xmlBuilder.AppendLine("</soap:Envelope>");

            var xmlBuilderWithHiddenFields = new StringBuilder();
            xmlBuilderWithHiddenFields.AppendLine("<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">");
            xmlBuilderWithHiddenFields.AppendLine("<soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("<getName xmlns=\"http://tempuri.org/\">");
            xmlBuilderWithHiddenFields.AppendLine("<username>XXXX</username>");
            xmlBuilderWithHiddenFields.AppendLine("<password>XXXX</password>");
            xmlBuilderWithHiddenFields.Append("<amount>").Append(amount).AppendLine("</amount>");
            xmlBuilderWithHiddenFields.Append("<merchantId>").Append(customerId).AppendLine("</merchantId>");
            xmlBuilderWithHiddenFields.Append("<sendersName>").Append(sendersName).AppendLine("</sendersName>");
            xmlBuilderWithHiddenFields.Append("<reference>").Append(reference).AppendLine("</reference>");
            xmlBuilderWithHiddenFields.Append("<provider>").Append(provider).AppendLine("</provider>");
            xmlBuilderWithHiddenFields.AppendLine("</getName>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Body>");
            xmlBuilderWithHiddenFields.AppendLine("</soap:Envelope>");

            xmlWithHiddenFields = xmlBuilderWithHiddenFields.ToString();
            return xmlBuilder.ToString();
        }

        [XmlRoot("Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class ResponseNameEnvelope
        {
            public ResponseNameBody Body { get; set; }
        }

        public class ResponseNameBody
        {
            [XmlElement(ElementName = "getNameResponse", Namespace = "http://tempuri.org/")]
            public GetNameResponse getNameResponse { get; set; }
        }

        public class GetNameResponse
        {
            public string getNameResult { get; set; }
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("merchantId");
            CustomSettings.AddVariableParameter("sendersName");
            CustomSettings.AddVariableParameter("reference");
            CustomSettings.AddVariableParameter("provider");

            SystemId = CustomSettings.Get(now, "CompanySystemId").AsString;
            Password = CustomSettings.Get(now, "CompanySystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
        }
    }
}
