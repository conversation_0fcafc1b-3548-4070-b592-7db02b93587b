﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using static town.connectors.drivers.Result;

namespace town.connectors.commons
{
	[DataContract(Name = "balanceResponse")]
	public abstract class BalanceResponseBase
	{
		[DataMember(Name = "locked")]
		public decimal Locked { get; set; }
		[DataMember(Name = "balance")]
		public decimal Balance { get; set; }
		[DataMember(Name = "changePercentage")]
		public decimal ChangePercentage { get; set; }
		public abstract string CurrencyCode { get; }

	}

	[DataContract(Name = "transactionDataModel")]
	public abstract class TransactionDataModel
	{
		private string atAddress = "";
		[DataMember(Name = "atAddress")]
		public string AtAddress
		{
			get
			{
				return atAddress;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
				atAddress = value;
			}
		}

		private decimal total;
		[DataMember(Name = "total")]
		public decimal Total
		{
			get
			{
				return total;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				total = value;
			}
		}

		private int authorizationNumber;
		[DataMember(Name = "authorizationNumber")]
		public int AuthorizationNumber
		{
			get
			{
				return authorizationNumber;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				authorizationNumber = value;
			}
		}

		private string currency;
		[DataMember(Name = "currency")]
		public string CurrencyCode
		{
			get
			{
				return currency;
			}
			set
			{
				currency = value;
			}
		}

		private DateTime useless;
		[DataMember(Name = "useless")]
		public DateTime Useless
		{
			get
			{
				return useless;
			}
			set
			{
				useless = value;
			}
		}

	}

	[DataContract(Name = "lockBalanceData")]
	public class LockBalanceData : TransactionDataModel
	{
		private int storeId;
		[DataMember(Name = "store")]
		public int StoreId
		{
			get
			{
				return storeId;
			}
			set
			{
				storeId = value;
			}
		}

		private decimal purchaseTotal;
		[DataMember(Name = "purchaseTotal")]
		public decimal PurchaseTotal
		{
			get
			{
				return purchaseTotal;
			}
			set
			{
				if (value <= 0) throw new Exception($"{nameof(value)} {value} is not greater than 0");
				purchaseTotal = value;
			}
		}

		private string concept;
		[DataMember(Name = "concept")]
		public string Concept
		{
			get
			{
				return concept;
			}
			set
			{
				concept = value;
			}
		}
		private string reference;
		[DataMember(Name = "reference")]
		public string Reference
		{
			get
			{
				return reference;
			}
			set
			{
				reference = value;
			}
		}

		[DataMember(Name = "accountNumber")]
		public string AccountNumber { get; set; }
		[DataMember(Name = "processorId")]
		public int ProcessorId { get; set; }
		[DataMember(Name = "who")]
		public string Who { get; set; }
		[DataMember(Name = "fragmentInformation")]
		public FragmentInformation FragmentInformation { get; set; }

	}
	[DataContract(Name = "FragmentInformation")]
	public class FragmentInformation
	{
		[DataMember(Name = "itsConfigured")]
		public bool ItsConfigured { get; set; }
		[DataMember(Name = "towin")]
		public decimal Towin { get; set; }
	}

	[DataContract(Name = "lockBalanceResponse")]
	public sealed class LockBalanceResponse
	{
		private int transactionNumber;

		[DataMember(Name = "authorization")]
		public int AuthorizationNumber
		{
			get { return transactionNumber; }
			set { transactionNumber = value; }
		}
	}

	[DataContract(Name = "message")]
	public abstract class MessageToAccountingServices
	{
		[DataMember(Name = "systemPassword")]
		public string SystemPassword { get; set; }
	}

	[DataContract(Name = "customerBalance")]
	public class CustomerBalance : MessageToAccountingServices
	{
		[DataMember(Name = "systemId")]
		public string SystemId { get; set; }
		[DataMember(Name = "customerId")]
		public string CustomerId { get; set; }
	}

	[DataContract(Name = "customerCasino")]
	public class CustomerCasino : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "customerId")]
		public string CustomerId { get; set; }
	}

	[DataContract(Name = "lottoCustomer")]
	public class LottoCustomer : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "password")]
		public new string SystemPassword { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
	}

	[DataContract(Name = "removeTransaction")]
	public class RemoveTransactionBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "documentNumber")]
		public string DocumentNumber { get; set; }
	}

	[DataContract(Name = "postTransaction")]
	public class PostTransactionBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "amount")]
		public string Amount { get; set; }
		[DataMember(Name = "tranCode")]
		public string TranCode { get; set; }
		[DataMember(Name = "tranType")]
		public string TranType { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "bettingAdjustmentFlagYN")]
		public string BettingAdjustmentFlagYN { get; set; }
		[DataMember(Name = "dailyFigureDate_YYYYMMDD")]
		public string DailyFigureDate_YYYYMMDD { get; set; }
	}

	[DataContract(Name = "postFreeFormTicketBody")]
	public class PostFreeFormTicketBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "totalRisk")]
		public string TotalRisk { get; set; }
	}

	[DataContract(Name = "PostTransactionWRef")]
	public abstract class PostTransactionWRefMessage : MessageToAccountingServices
	{
		protected enum TRANSATION_CODE
		{
			C = 0,  // Acreditar
			D = 1 //Regrade- Noaction debitar
		};

		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "amount")]
		public string Amount { get; set; }
		[DataMember(Name = "tranCode")]
		public string TranCode { get; protected set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "referenceID")]
		public string ReferenceID { get; set; }
	}

	[DataContract(Name = "DebitMessage")]
	public class DebitMessage : PostTransactionWRefMessage
	{


		public DebitMessage()
		{
			TranCode = TRANSATION_CODE.D.ToString();
		}
	}

	[DataContract(Name = "CreditMessage")]
	public class CreditMessage : PostTransactionWRefMessage
	{
		public CreditMessage()
		{
			TranCode = TRANSATION_CODE.C.ToString();
		}
	}

	[DataContract(Name = "getTicketWagersBody")]
	public class GetTicketWagersBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "ticketNumber")]
		public string TicketNumber { get; set; }
	}

	[DataContract(Name = "postFreeFormWagerCollectionBody")]
	public class PostFreeFormWagerCollectionBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "ticketNumber")]
		public string TicketNumber { get; set; }
		[DataMember(Name = "wagers")]
		public PostFreeFormWagerCollectionWagers Wagers { get; set; }
	}

	[DataContract(Name = "postFreeFormWagerCollectionWagers")]
	public class PostFreeFormWagerCollectionWagers
	{
		[DataMember(Name = "wagers")]
		public PostFreeFormWager[] Wagers { get; set; }
	}

	[DataContract(Name = "postFreeFormTicketAndWagersBody")]
	public class PostFreeFormTicketAndWagersBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "totalRisk")]
		public string TotalRisk { get; set; }
		[DataMember(Name = "wagers")]
		public PostFreeFormWager[] Wagers { get; set; }
	}
	public class GradeFreeFormWager : PostFreeFormWager
	{
	}
	[DataContract(Name = "postFreeFormWager")]
	public class PostFreeFormWager
	{
		[DataMember(Name = "risk")]
		public string Risk { get; set; }
		[DataMember(Name = "toWin")]
		public string ToWin { get; set; }
		[DataMember(Name = "betDescription")]
		public string BetDescription { get; set; }
		[DataMember(Name = "wagerNumber")]
		public string WagerNumber { get; set; }
		[DataMember(Name = "referenceNumber")]
		public string ReferenceNumber { get; set; }
		[DataMember(Name = "status")]
		public string Status { get; set; }
		[DataMember(Name = "ticketNumber")]
		public string TicketNumber { get; set; }
	}

	[DataContract(Name = "postFreeFormTicketAndWagersResponse")]
	public class PostFreeFormTicketAndWagersResponse
	{
		[DataMember(Name = "ticketNumber")]
		public int TicketNumber { get; set; }
		[DataMember(Name = "wagers")]
		public PostFreeFormWager[] Wagers { get; set; }
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }
	}

	[DataContract(Name = "postFreeFormWagerCollectionResponse")]
	public class PostFreeFormWagerCollectionSuccessResponse
	{
		[DataMember(Name = "wagers")]
		public PostFreeFormWager[] Wagers { get; set; }
		[DataMember(Name = "error")]
		public string Error { get; set; }
	}

	[DataContract(Name = "FragmentAuthorizationBody")]
	public class FragmentAuthorizationBody
	{
		[DataMember(Name = "atAddress")]
		public string AtAddress { get; set; }
		[DataMember(Name = "authorizationNumber")]
		public int AuthorizationNumber { get; set; }
		[DataMember(Name = "wagers")]
		public Fragment[] Fragments { get; set; }
	}


	[DataContract(Name = "DepositThenLockBody")]
	public class DepositThenLockBody
	{
		[DataMember(Name = "atAddress")]
		public string AtAddress { get; set; }
		[DataMember(Name = "authorizationNumber")]
		public int AuthorizationNumber { get; set; }
		[DataMember(Name = "fragments")]
		public Fragment[] Fragments { get; set; }
	}

	[DataContract(Name = "fragment")]
	public class Fragment
	{
		[DataMember(Name = "risk")]
		public string Risk { get; set; }
		[DataMember(Name = "toWin")]
		public string ToWin { get; set; }
		[DataMember(Name = "betDescription")]
		public string BetDescription { get; set; }
		[DataMember(Name = "Number")]
		public string Number { get; set; }
		[DataMember(Name = "referenceNumber")]
		public string ReferenceNumber { get; set; }
		[DataMember(Name = "ticketNumber")]
		public string TicketNumber { get; set; } = "0";
	}

	public class EntitiesConverter
    {
        public static PostFreeFormWager FragmentToWager(Fragment input)
        {
            return new PostFreeFormWager
            {
                BetDescription = input.BetDescription,
                WagerNumber = input.Number,
                ReferenceNumber = input.ReferenceNumber,
                Risk = input.Risk,
                ToWin = input.ToWin,
                TicketNumber = input.TicketNumber,
            };
        }

        public static PostFreeFormWagerCollectionSuccessResponse CreateFakePostFreeFormWagerCollectionResponse(PostFreeFormWager[] wagers, int initialWagerId)
        {
            PostFreeFormWager[] cloned = new PostFreeFormWager[wagers.Length];
            wagers.CopyTo(cloned, 0);
            var fakeResponse = new PostFreeFormWagerCollectionSuccessResponse()
            {
                Wagers = cloned
            };
            foreach (PostFreeFormWager wager in fakeResponse.Wagers)
            {
                wager.WagerNumber = initialWagerId.ToString();
                initialWagerId++;
            }

            return fakeResponse;
        }
    }

    [DataContract(Name = "casinoInfoResponse")]
	public class CasinoInfoResponse
	{
		[DataMember(Name = "AgentId")]
		public string AgentId { get; set; }
		[DataMember(Name = "AvailableBalance")]
		public decimal AvailableBalance { get; set; }
		[DataMember(Name = "CasinoActive")]
		public string CasinoActive { get; set; }
		[DataMember(Name = "CustomerId")]
		public string CustomerId { get; set; }
		[DataMember(Name = "Email")]
		public string Email { get; set; }
		[DataMember(Name = "FirstName")]
		public string FirstName { get; set; }
		[DataMember(Name = "InetTarget")]
		public string InetTarget { get; set; }
		[DataMember(Name = "LastName")]
		public string LastName { get; set; }
	}


	[DataContract(Name = "postFreeFormWagersBody")]
	public class PostFreeFormWagersBody
	{
		[DataMember(Name = "postFreeFormWagers")]
		public PostFreeFormWager[] PostFreeFormWagers { get; set; }
	}

	[DataContract(Name = "payFragmentsMessage")]
	public class PayFragmentsMessage
	{
		[DataMember(Name = "ticketNumber")]
		public string TicketNumber { get; set; }
		[DataMember(Name = "wagerNumber")]
		public int WagerNumber { get; set; }
		[DataMember(Name = "outcome")]
		public string Outcome { get; set; }
		[DataMember(Name = "dailyFigureDate_YYYYMMDD")]
		public string DailyFigureDate_YYYYMMDD { get; set; }
		[DataMember(Name = "adjustedWinAmount")]
		public string AdjustedWinAmount { get; set; }
		[DataMember(Name = "adjustedLossAmount")]
		public string AdjustedLossAmount { get; set; }
		[DataMember(Name = "isValidTicketNumber")]
		public bool IsValidTicketNumber { get; set; }
		public int AgentId { get; set; }
		public string DomainUrl { get; set; }
	}

    [DataContract(Name = "payFragmentsWithAtAddressMessage")]
    public class PayFragmentsWithAtAddressMessage: PayFragmentsMessage
    {
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }

		public static PayFragmentsWithAtAddressMessage ConverterWithAtAddress(string atAddress, PayFragmentsMessage payFragmentsMessage)
		{
			return new PayFragmentsWithAtAddressMessage()
			{
				TicketNumber = payFragmentsMessage.TicketNumber,
				WagerNumber = payFragmentsMessage.WagerNumber,
				Outcome = payFragmentsMessage.Outcome,
				DailyFigureDate_YYYYMMDD = payFragmentsMessage.DailyFigureDate_YYYYMMDD,
				AdjustedWinAmount = payFragmentsMessage.AdjustedWinAmount,
				AdjustedLossAmount = payFragmentsMessage.AdjustedLossAmount,
				IsValidTicketNumber = payFragmentsMessage.IsValidTicketNumber,
				AgentId = payFragmentsMessage.AgentId,
				DomainUrl = payFragmentsMessage.DomainUrl,
				AtAddress = atAddress
			};
        }
    }

    [DataContract(Name = "PayBody")]
    public class PayBody
    {
        [DataMember(Name = "storeId")]
        public int StoreId { get; set; }

        [DataMember(Name = "concept")]
        public string Concept { get; set; }

        [DataMember(Name = "who")]
        public string Who { get; set; }

        [DataMember(Name = "gradeFreeFormWagers")]
        public List<PayFragmentsWithAtAddressMessage> GradeFreeFormWagers { get; set; }

        [DataMember(Name = "processorKey")]
        public string ProcessorKey { get; set; }

        [DataMember(Name = "processorId")]
        public int ProcessorId { get; set; }
    }

    [DataContract(Name = "gradeFreeFormWagers")]
	public class GradeFreeFormWagers : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "wagers")]
		public PayFragmentsMessage[] Wagers { get; set; }
	}

	[DataContract(Name = "gradeFreeFormWagerCollectionBody")]
	public class GradeFreeFormWagerCollectionBody
	{
		[DataMember(Name = "collection")]
		public GradeFreeFormWagers Collection { get; set; }
	}

	[DataContract(Name = "gradeFreeFormResponse")]
	public class Entities : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "wagers")]
		public PayFragmentsMessage[] Wagers { get; set; }
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }
		public PayFragmentsMessage[] FailedWagers { get; set; }

		public void Merge(Entities response)
		{
			if (response.Error != null)
			{
				if (this.FailedWagers == null)
				{
					this.FailedWagers = response.FailedWagers;
				}
				else
				{
					PayFragmentsMessage[] combined = this.FailedWagers.Concat(response.FailedWagers).ToArray();
					this.FailedWagers = combined;
				}
			}
			else
			{
				if (this.Wagers == null && response.Wagers != null)
				{
					this.Wagers = response.Wagers;
				}
				else if (response.Wagers != null)
				{
					PayFragmentsMessage[] combined = this.Wagers.Concat(response.Wagers).ToArray();
					this.Wagers = combined;
				}

				if (this.Error == null)
				{
					this.Error = response.Error;
				}
			}
		}
	}

	[DataContract(Name = "error")]
	public class ErrorResponse
	{
		[DataMember(Name = "code")]
		public string Code { get; set; }
		[DataMember(Name = "message")]
		public string Message { get; set; }
	}

	[DataContract(Name = "jsonErrorResponse")]
	public class JsonErrorResponse
	{
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }
	}

	[DataContract(Name = "ValidateCustomerResponse")]
	public class ValidateCustomerResponse
	{
		[DataMember(Name = "Agent")]
		public string Agent { get; set; }
		[DataMember(Name = "AvailableBalance")]
		public decimal AvailableBalance { get; set; }
		[DataMember(Name = "CreditAcctFlag")]
		public string CreditAcctFlag { get; set; }
		[DataMember(Name = "CreditLimit")]
		public decimal CreditLimit { get; set; }
		[DataMember(Name = "Currency")]
		public string Currency { get; set; }
		[DataMember(Name = "CurrencyRate")]
		public decimal CurrencyRate { get; set; }
		[DataMember(Name = "CurrentBalance")]
		public decimal CurrentBalance { get; set; }
		[DataMember(Name = "Partner")]
		public string Partner { get; set; }
		[DataMember(Name = "LoginStatus")]
		public decimal LoginStatus { get; set; }
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }


	}
	
	[DataContract(Name = "postValidateCutomer")]
	public sealed class PostValidateCustomerBody : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "customerID")]
		public string CustomerId { get; set; }
		[DataMember(Name = "token")]
		public string Token { get; set; }

	}
	[DataContract(Name = "postFreeFormWagerCollectionResponse")]
	public class PostFreeFormWagerCollectionResponse
	{
		[DataMember(Name = "wagers")]
		public PostFreeFormWager[] Wagers { get; set; }
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }
	}
	[DataContract(Name = "gradeFreeFormResponse")]
	public class GradeFreeFormWagersResponse : MessageToAccountingServices
	{
		[DataMember(Name = "systemID")]
		public string SystemId { get; set; }
		[DataMember(Name = "clerkID")]
		public string ClerkId { get; set; }
		[DataMember(Name = "wagers")]
		public PayFragmentsMessage[] Wagers { get; set; }
		[DataMember(Name = "error")]
		public ErrorResponse Error { get; set; }
		public PayFragmentsMessage[] FailedWagers { get; set; }

		public void Merge(GradeFreeFormWagersResponse response)
		{
			if (response.Error != null)
			{
				if (this.FailedWagers == null)
				{
					this.FailedWagers = response.FailedWagers;
				}
				else
				{
					PayFragmentsMessage[] combined = this.FailedWagers.Concat(response.FailedWagers).ToArray();
					this.FailedWagers = combined;
				}
			}
			else
			{
				if (this.Wagers == null && response.Wagers != null)
				{
					this.Wagers = response.Wagers;
				}
				else if (response.Wagers != null)
				{
					PayFragmentsMessage[] combined = this.Wagers.Concat(response.Wagers).ToArray();
					this.Wagers = combined;
				}

				if (this.Error == null)
				{
					this.Error = response.Error;
				}
			}
		}
	}

	[DataContract(Name = "refundFreeFormResponse")]
    public class RefundFreeFormWagersResponse : MessageToAccountingServices
    {
        [DataMember(Name = "systemID")]
        public string SystemId { get; set; }
        [DataMember(Name = "clerkID")]
        public string ClerkId { get; set; }
        [DataMember(Name = "wagers")]
        public PayFragmentsMessage[] Wagers { get; set; }
        [DataMember(Name = "error")]
        public ErrorResponse Error { get; set; }
        public PayFragmentsMessage[] FailedWagers { get; set; }

        public void Merge(RefundFreeFormWagersResponse response)
        {
            if (response.Error != null)
            {
                if (this.FailedWagers == null)
                {
                    this.FailedWagers = response.FailedWagers;
                }
                else
                {
                    PayFragmentsMessage[] combined = this.FailedWagers.Concat(response.FailedWagers).ToArray();
                    this.FailedWagers = combined;
                }
            }
            else
            {
                if (this.Wagers == null && response.Wagers != null)
                {
                    this.Wagers = response.Wagers;
                }
                else if (response.Wagers != null)
                {
                    PayFragmentsMessage[] combined = this.Wagers.Concat(response.Wagers).ToArray();
                    this.Wagers = combined;
                }

                if (this.Error == null)
                {
                    this.Error = response.Error;
                }
            }
        }
    }

    public interface IDepositResponse
    {
		public int AuthorizationId { get; }
		public TransactionStatus Status { get; }
	}

	public struct DespositResponse : IDepositResponse
	{
		public int AuthorizationId { get; }
		public TransactionStatus Status { get; }
		public string ProcessorKeyUsed { get; private set; }

		public DespositResponse(int authorizationId, TransactionStatus status, string processorKeyUsed)
		{
			AuthorizationId = authorizationId;
			Status = status;
			ProcessorKeyUsed = processorKeyUsed;
		}

	}

	[DataContract(Name = "FieldsResponse")]
	public class FieldsResponse
	{
		[DataMember(Name = "DOB")]
		public string Birthdate { get; set; }
	}

	[DataContract(Name = "DepositBody")]
	public class DepositBody
	{
		[DataMember(Name = "merchId")]
		public string MerchantId { get; set; }
		[DataMember(Name = "sendersName")]
		public string SendersName { get; set; }
		[DataMember(Name = "country")]
		public string Country { get; set; }
		[DataMember(Name = "state")]
		public string State { get; set; }
		[DataMember(Name = "city")]
		public string City { get; set; }
		[DataMember(Name = "controlNum")]
		public string ControlNum { get; set; }
		[DataMember(Name = "amount")]
		public decimal Amount { get; set; }
		[DataMember(Name = "providerId")]
		public int ProviderId { get; set; }
	}

	[DataContract(Name = "PayoutBody")]
	public class PayoutBody
	{
		[DataMember(Name = "merchId")]
		public string MerchantId { get; set; }
		[DataMember(Name = "receiversName")]
		public string ReceiversName { get; set; }
		[DataMember(Name = "country")]
		public string Country { get; set; }
		[DataMember(Name = "state")]
		public string State { get; set; }
		[DataMember(Name = "city")]
		public string City { get; set; }
		[DataMember(Name = "amount")]
		public decimal Amount { get; set; }
		[DataMember(Name = "sendersAddress")]
		public string SendersAddress { get; set; }
	}
}
