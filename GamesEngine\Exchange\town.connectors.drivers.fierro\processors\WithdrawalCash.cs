﻿using Connectors.town.connectors.driver.transactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    internal abstract class WithdrawalCash : Withdrawal
	{
        public WithdrawalCash(string currencyIsoCode) : base(currencyIsoCode, PaymentMethod.Cash)
        {
        }

        public override string Description => "Fiero driver";
		public override string Fabricator => "Ncubo";

		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);

		public override void Prepare(DateTime now)
		{
			base.Prepare(now);
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }
	}
}
