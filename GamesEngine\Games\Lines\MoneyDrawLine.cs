﻿using GamesEngine.Gameboards.Lines;
using GamesEngine.Games.Tournaments;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using GamesEngine.Time;
using System;
using System.Collections.Generic;

namespace GamesEngine.Games.Lines
{
	internal class MoneyDrawLine : ABLine
	{
		internal const string TEXT = "Money Draw Line";
		private readonly int tieReward;
		private readonly double tieCoefficientReward;
		protected DrawAnswer TieAnswer { get; }

		internal MoneyDrawLine(int lineId, Question question, Tier tier, Game game, Shelve shelve, int index, int teamAReward, int tieReward, int teamBReward, string who, DateTime now) : base(lineId, question, tier, game, game.Favorite, shelve, index, teamAReward, teamBReward, who, now)
		{
			bool tieResultsAreAllowed = (game.Sport == Sport.SOCCER && Sport.SOCCER.AllowsTieResults()) 
				|| (game.Sport == Sport.HOCKEY && Sport.HOCKEY.AllowsTieResults());
			if (!tieResultsAreAllowed) throw new GameEngineException($"Sport {game.Sport.Name} does not allows draw results");
			if (game.IsInPlay() && tier != Tier.TIER_TWO) throw new GameEngineException($"Game {game.ToString()} already started, it does not allow changes");
			if (game.IsGameOver()) throw new GameEngineException($"Game {game.ToString()} already started, it does not allow changes");
			if (string.IsNullOrWhiteSpace(question.Text)) throw new ArgumentNullException(nameof(question.Text));

			ValidateReward(tieReward);
			this.tieReward = tieReward;
			this.TieAnswer = new DrawAnswer(base.Game, base.Game.TeamA, base.Game.TeamB);
			this.tieCoefficientReward = CalculateDrawCoefficient();
			base.Text = ReplacePlaceholders(question.Text, game);
		}

		private MoneyDrawLine(MoneyDrawLine previousVersion, int teamAReward, int tieReward, int teamBReward, string who, DateTime now) : base(previousVersion, teamAReward, teamBReward, who, now)
		{
			ValidateReward(tieReward);
			this.tieReward = tieReward;
			this.TieAnswer = previousVersion.TieAnswer;
			this.tieCoefficientReward = CalculateDrawCoefficient();
			if (!base.IsDraft && !base.IsCanceled)
			{
				var changedLineEvent = this.GetChangedLineEvent(now);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		protected override double CoefficientReward(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			if (answer is DrawAnswer)
			{
				return this.tieCoefficientReward;
			}
			return base.CoefficientReward(answer);
		}

		internal override string RewardAsString(WagerAnswer answer)
		{
			if (answer == null) throw new ArgumentNullException(nameof(answer));

			if (answer is DrawAnswer)
			{
				int result = this.tieReward;
				return (result > 0 ? "+" : "-") + result;
			}
			return base.RewardAsString(answer);
		}

		private double CalculateDrawCoefficient()
		{
			double result = 0;
			if (this.tieReward < 0)
			{
				result = 100 / (double)Math.Abs(this.tieReward);
			}
			else
			{
				result = Math.Abs(this.tieReward) / 100.0;
			}

			if (result == 0) throw new GameEngineException($"Prize reward for a {nameof(MoneyDrawLine)} can not be zero.");

			return result;
		}

		internal override decimal Grade(Wager wager)
		{
			if (!game.IsGameOver() && !Showcase.IsThereAnyPendingLine()) throw new GameEngineException($"This game {this.game.ToString()} can not be graded because it is not ended yet.");
			if (this != wager.Line) throw new GameEngineException($"Wager {wager.AuthorizationId} does not correspond to Line {this.LineId}");

			bool won = false;
			switch(wager.ChosenAnswer)
			{
				case ABAnswer abAnswer:
					if (abAnswer.ChosenTeam == base.TeamA)
					{
						won = base.RealAnswer.TeamAisWinner();
					}
					else if (abAnswer.ChosenTeam == base.TeamB)
					{
						bool teamBIsWinner = !base.RealAnswer.TeamAisWinner();
						won = teamBIsWinner;
					}
					break;
				case DrawAnswer drawAnswer:
					won = base.RealAnswer.IsDraw();
					break;
				default:
					throw new GameEngineException($"Wager {wager.AuthorizationId} does not correspond to Money Draw Line {this.LineId}");
			}

			decimal prize = 0;
			if (won)
			{
				prize = wager.ToWin();
				wager.GradeAsWinner();
			}
			else
			{
				wager.GradeAsLoser();
			}
			wager.Showcase.RiskAssestment.UpdateGrade(wager);

			return prize;
		}

		internal MoneyDrawLine NewVersion(int teamAReward, int tieReward, int teamBReward, string who, DateTime now)
		{
			MoneyDrawLine result = new MoneyDrawLine(this, teamAReward, tieReward, teamBReward, who, now);
			return result;
		}

		internal WagerAnswer GetTieAnswer(Team choosenTeam1, Team choosenTeam2)
		{
			if (choosenTeam1 == null) throw new ArgumentNullException(nameof(choosenTeam1));
			if (choosenTeam2 == null) throw new ArgumentNullException(nameof(choosenTeam2));

			return new DrawAnswer(base.Game, choosenTeam1, choosenTeam2);
		}

		internal override string LineTypeAsString => LineType.MONEYDRAW_LINE.ToString();

		internal override void Serialize(KafkaMessage message)
		{
			if (!game.IsGameOver() && !Showcase.IsThereAnyPendingLine()) throw new GameEngineException("Line should be serialized only to send to BI and the end of grades");
			if (!IsOriginalVersion()) PreviousVersion.Serialize(message);

			message.AddProperty((char)LineType.MONEYDRAW_LINE);
			message.AddProperty(base.LineId);
			message.AddProperty(base.Version);
			message.AddProperty(base.TeamAReward);
			message.AddProperty(this.tieReward);
			message.AddProperty(base.TeamBReward);
			if (RealAnswer.IsDraw())
            {
				message.AddProperty('D');
			}
			else if (RealAnswer.TeamAisWinner())
            {
				message.AddProperty('A');
			}
			else
			{
				message.AddProperty('B');
			}
			message.AddProperty((int)base.Grading);
			message.AddProperty((int)base.BasedOn.Id);
		}

		protected override LineEvent GetChangedLineEvent(DateTime timestamp)
		{
			var result = new ChangedMoneyDrawLineEvent(timestamp, this);
			return result;
		}

		protected override void ForcePublishedLineEvent(bool itIsThePresent, DateTime timestamp)
		{
			if (!base.IsPublished) throw new GameEngineException($"Line must be on publish visibility to send an event.");

			if (itIsThePresent)
			{
				var changedLineEvent = new PublishedMoneyDrawLineEvent(timestamp, this);
				PlatformMonitor.GetInstance().WhenNewEvent(changedLineEvent);
			}
		}

		internal int TieReward
		{
			get
			{
				return this.tieReward;
			}
		}
	}
}
