﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Sentinels;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Transactions.Deposit;

namespace GamesEngine.Business.Liquidity
{
    internal class Liquid : Objeto
    {
        private readonly List<Source> sources = new();
        private readonly Dictionary<int, Dispenser> _dispensers = new Dictionary<int, Dispenser>();
        private readonly Dictionary<int, Bottle> _bottles = new Dictionary<int, Bottle>();
        
        internal Liquid(string kind, LiquidFlow parentFlow, IngressSentinel ingressSentinel)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (parentFlow == null) throw new ArgumentNullException(nameof(parentFlow));
            if (ingressSentinel == null) throw new ArgumentNullException(nameof(ingressSentinel));

            Kind = kind;
            sources.Add(new Source(this));

            ParentFlow = parentFlow;
            IngressSentinel = ingressSentinel;
        }

        internal IngressSentinel IngressSentinel { get; private set; }

        internal string Kind { get; private set; }

        internal LiquidFlow ParentFlow { get; private set; }

        internal Source Source => sources.FirstOrDefault();
        internal IEnumerable<Source> Sources => sources;

        internal IEnumerable<Dispenser> Dispensers => _dispensers.Values;

        internal IEnumerable<Bottle> Bottles => _bottles.Values;

        internal decimal Amount => sources.Sum(s => s.Amount);

        internal decimal AmountTo(DateTime moment)
        {
            throw new NotImplementedException();
        }

        internal bool ExistXpub(string xpub)
        {
            if (string.IsNullOrWhiteSpace(xpub)) throw new GameEngineException(nameof(xpub));

            Source source = sources.FirstOrDefault(s => s.Kind == this.Kind);
            if (source == null) throw new ArgumentNullException(nameof(source));

            return source.ExsitXpub(xpub);
        }

        internal Source AddSource(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            bool existInOtherLiquid = ParentFlow.ExistXpub(xpub.Value);
            if (existInOtherLiquid) throw new GameEngineException($"The xpub: {xpub.Value} is already associated with an existing source in another liquid.");

            //Rubicon: Todo Verificar que ya no existe en todo el liquid system
            var source = sources.FirstOrDefault(s => s.Kind == this.Kind);
            if (source == null) throw new ArgumentNullException(nameof(source));

            source.AddXpub(xpub);
            return source;
        }

        internal void RemoveSource(Xpub xpub)
        {
            throw new NotImplementedException();
            //var source = _sources.FirstOrDefault(s => s.Xpub.Value == xpub.Value);
            //if (source != null) _sources.Remove(source);
        }

        //internal Deposit GenerateDeposit(bool itIsThePresent, DateTime now,int depositId)
        //{
        //    var source = sources.FirstOrDefault(s => s.Kind == this.Kind);
        //    if (source == null) throw new ArgumentNullException(nameof(source));
        //    if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
        //    if(depositId <= 0) throw new ArgumentException("Deposit ID must be greater than zero.", nameof(depositId));

        //    Deposit deposit = source.RequestDeposit(itIsThePresent, now,depositId);
        //    return deposit;
        //}

        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdAt, int id, string name, string address, decimal amount, DateTime startDate)
        {
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new GameEngineException("The Id is invalid.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The Name is null or empty.");
            if (string.IsNullOrWhiteSpace(address)) throw new GameEngineException("The Address is null or empty.");
            if (amount <= 0) throw new GameEngineException("The Amount is invalid.");
            if (startDate == DateTime.MinValue) throw new GameEngineException("The startDate is invalid.");

            if (amount > Amount) throw new GameEngineException("The amount is greater than the available amount.");

            var dispenser = new DispenserReady(id, name, this, this.Kind, address, amount, startDate);
            AddOrUpdateDispenser(dispenser);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    var createdDispenserMessage = new CreatedDispenserMessage(id, this.Kind, address, amount, createdAt);
                    buffer.Send(createdDispenserMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedDispenserEvent createdDispenserEvent = new CreatedDispenserEvent(createdAt, dispenser.Id, dispenser.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDispenserEvent);
            }
            return dispenser;
        }

        private int withdrawalConsecutive = 0;
        internal int NextWithdrawalId()
        {
            return withdrawalConsecutive + 1;
        }

        private int dispenserConsecutive = 0;
        internal int NextDispenserId()
        {
            return dispenserConsecutive + 1;
        }

        private int bottleConsecutive = 0;
        internal int NextBottleId()
        {
            return bottleConsecutive + 1;
        }

        internal void AddOrUpdateDispenser(Dispenser dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (_dispensers.TryGetValue(dispenser.Id, out Dispenser foundDispenser))
            {
                _dispensers[dispenser.Id] = dispenser;
            }
            else
            {
                _dispensers.Add(dispenser.Id, dispenser);
                dispenserConsecutive = dispenser.Id;
            }
        }

        internal Bottle FindBottle(int bottleId)
        {
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (_bottles.TryGetValue(bottleId, out Bottle foundBottle))
            {
                return foundBottle;
            }
            return null;
        }

        internal void AddOrUpdateBottle(Bottle bottle)
        {
            if (bottle == null) throw new ArgumentNullException(nameof(bottle));
            if (_bottles.TryGetValue(bottle.Id, out Bottle foundBottle))
            {
                _bottles[bottle.Id] = bottle;
            }
            else
            {
                _bottles.Add(bottle.Id, bottle);
                bottleConsecutive = bottle.Id;
            }
        }

        internal BottlePending CreateBottle(bool itIsThePresent, DateTime now, int bottleId, string name, DispenserReady dispenser)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or empty.", nameof(name));

            var bottle = new BottlePending(bottleId, name, dispenser, now);
            AddOrUpdateBottle(bottle);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedBottleMessage createdDispenserMessage = new CreatedBottleMessage(bottleId, name, Kind, now);
                    buffer.Send(createdDispenserMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedBottleEvent createdBottleEvent = new CreatedBottleEvent(now, bottle.Id, bottle.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdBottleEvent);
            }

            return bottle;
        }
    }

}
