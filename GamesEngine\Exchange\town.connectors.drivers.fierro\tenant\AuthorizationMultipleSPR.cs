﻿using GamesEngine.Marketing.Campaigns;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.fiero;

namespace GamesEngine.Exchange.town.connectors.drivers.fierro.tenant
{
    public class AuthorizationMultipleSPR : AuthorizationMultiple
    {
        public AuthorizationMultipleSPR() : base(Campaign.SPR_CURRENCY_CODE)
        {
        }
    }
}
