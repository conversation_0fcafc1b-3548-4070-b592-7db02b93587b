# Documentation: Puppeteer Actor Integration in an ASP.NET Core API

This document explains the architecture and use case for integrating the Puppeteer Actor model with a C# ASP.NET Core web API. It details how services are implemented as stateful Actors, how they are instantiated, and how API controllers interact with them using the Puppeteer DSL.

The `Wishmaker` service, which manages player favorites, will be used as the primary example.

## 1. Architectural Overview

The core of this architecture is the separation of the web layer (API Controllers) from the business logic and state management layer (Actors). Instead of a traditional N-tier architecture with stateless services, this system uses a single, stateful **Actor** for each logical service domain.

The interaction flow is as follows:

```
+----------------+      (HTTP Request)      +-----------------+      (DSL Script)      +---------------------+      (C# Method Call)      +----------------+
|                |  ----------------------> |                 |  ------------------->  |                     |  ----------------------> |                |
|  Web Client    |                          |  APIController  |                        |  RestAPIActorAsync  |                          |  [Puppet]      |
|                |  <---------------------- |                 |  <-------------------  |  (Wrapper for...)   |  <---------------------- |  Objects       |
+----------------+      (JSON Response)     +-----------------+      (IActionResult)   |  +---------------+  |                          |  (e.g., Company,|
                                                                                       |  | WishmakerActor|  |                          |   Player)      |
                                                                                       |  +---------------+  |                          |                |
                                                                                       +---------------------+                          +----------------+
```

-   **API Controller**: A thin layer that handles HTTP requests, authentication, and basic input validation. It does **not** contain business logic.
-   **RestAPIActorAsync**: A generic wrapper that bridges the web controller and the actual Actor. It handles error translation, context extraction (IP, User), and simplifies the calling pattern.
-   **WishmakerActor**: The concrete, stateful service instance. It inherits from `Puppeteer.EventSourcing.Actor` and is responsible for managing all state related to the "Favorites" feature.
-   **[Puppet] Objects**: Plain C# objects (`Company`, `Player`, `FavoritesCatalog`, etc.) that hold the actual data and implement the business logic methods. The Actor holds references to these objects in its internal state.

---

## 2. Actor Instantiation and Lifecycle

In this model, Actors are designed as **long-lived, stateful singletons**. There is only one `WishmakerActor` instance for the entire application lifecycle. This is crucial for maintaining a single, consistent source of truth for the service's state.

The instantiation process is simple and typically done in a static helper class.

**File: `Wishmaker.cs`**
This file is the "factory" or service locator for our actor.

```csharp
namespace WishmakerAPI
{
    internal static class WishmakerAPI
    {
        // 1. A private, static instance of the concrete Actor is created.
        // This happens only once when the application starts.
        private static WishmakerActor actor = new WishmakerActor();

        // 2. The actor instance is wrapped in the RestAPIActorAsync adapter.
        // This static wrapper becomes the public entry point for the service.
        internal static RestAPIActorAsync Wishmaker = new RestAPIActorAsync(actor);
    }
}
```

The `WishmakerActor` itself is just an empty shell that inherits all the powerful concurrency and state management features from the base `Actor` class.

**File: `GameMaker.cs`**
```csharp
public class WishmakerActor : Actor
{
    // The name passed to the base constructor is used for logging
    // and potentially for the event store (Dairy) table name.
    public WishmakerActor() : base(nameof(WishmakerActor))
    {
        // Initialization logic, like event handlers, can be set up here.
    }
}
```

From this point on, all interactions with the `Wishmaker` service from the web layer will go through the static `WishmakerAPI.Wishmaker` instance.

---

## 3. Controller Interaction Patterns

The `APIController.cs` file demonstrates several ways to interact with the Actor. The key is that the controller **builds a DSL script** and passes it to the `RestAPIActorAsync` wrapper, which then orchestrates the execution on the `WishmakerActor`.

### 3.1. Performing Queries (`PerformQryAsync`)

Queries are used for read-only operations. They do not change the actor's state and are executed under a **read lock**, allowing multiple queries to run concurrently.

**Purpose:** To retrieve data from the actor's state.

**Example: `FavoritesListsAsync` Endpoint**

```csharp
// File: APIController.cs

[HttpGet("api/favorites")]
[Authorize(Roles = "player")]
public async Task<IActionResult> FavoritesListsAsync()
{
    string playerId = Validator.StringEscape(Security.PlayerId(User));
    
    // The controller calls the wrapper's PerformQryAsync method...
    var result = await WishmakerAPI.Wishmaker.PerformQryAsync(HttpContext, $@"
        // ...passing it a DSL script to execute.
        {{
            player = company.CustomerByPlayerId('{playerId}').Player;
            favoritesCatalog = player.FavoritesCatalog();
            for (favoritesLists : favoritesCatalog.GetAll)
            {{
                favoriteNumbersList = favoritesLists;
                print favoriteNumbersList.Name name;
                print favoriteNumbersList.IconPath iconPath;
                for (favoriteNumbers : favoriteNumbersList.GetAll)
                {{
                    print favoriteNumbers.Enabled enabled;
                    print favoriteNumbers.AsString() number;
                    print favoriteNumbers.TicketTypeAsString() ticketType;
                }}
            }}
        }}
    ");

    return result;
}
```

**How It Works:**

1.  The controller constructs a multi-line DSL script as a C# string.
2.  It calls `Wishmaker.PerformQryAsync`, passing the script.
3.  The `RestAPIActorAsync` wrapper asks the `WishmakerActor` to execute the query.
4.  The `WishmakerActor` acquires a **read lock**.
5.  It executes the script:
    *   It looks up the root `company` object.
    *   It calls the C# method `CustomerByPlayerId()` on the `Company` object.
    *   It navigates the object graph (`.Player`, `.FavoritesCatalog()`, `.GetAll`).
    *   The `for` loops iterate over the C# collections.
    *   The `Print` commands build a JSON response from the data.
6.  The actor releases the read lock.
7.  The `RestAPIActorAsync` wrapper receives the resulting JSON string and wraps it in an `OkObjectResult`.
8.  The controller returns this `IActionResult` to the client.

### 3.2. Performing Commands (`PerformCmdAsync`)

Commands are used for write operations that change the actor's state. They are executed under an exclusive **write lock**, ensuring atomicity.

**Purpose:** To modify the actor's state.

**Example:** A simplified `CreateFavoriteAsync` endpoint.

```csharp
// (Simplified example for clarity)
[HttpPost("api/favorite")]
[Authorize(Roles = "player")]
public async Task<IActionResult> CreateFavoriteAsync([FromBody] FavoriteBody body)
{
    // ... input validation ...
    var nameEscaped = Validator.StringEscape(body.Name);
    string playerId = Validator.StringEscape(Security.PlayerId(User));

    // A command script is built that calls state-changing methods.
    var result = await WishmakerAPI.Wishmaker.PerformCmdAsync(HttpContext, $@"
        {{
            player = company.CustomerByPlayerId('{playerId}').Player;
            favoritesCatalog = player.FavoritesCatalog();
            favoritesCatalog.CreateFavoriteNumbers('{nameEscaped}','{body.IconPath}');
        }}
    ");

    return result;
}
```

**How It Works:**

1.  The flow is similar to a query, but the `PerformCmdAsync` method is used.
2.  The `WishmakerActor` acquires a **write lock**, blocking all other reads and writes.
3.  It executes the script, which ultimately calls the C# method `CreateFavoriteNumbers(...)`. This method modifies the internal list of favorites within the `FavoritesCatalog` object.
4.  After successful execution, the actor serializes the state-changing parts of the script to its **event store (`Dairy`)**.
5.  The write lock is released.

### 3.3. The Check-Then-Act Pattern (`PerformChkThenCmdAsync`)

This is the most robust pattern for commands that require pre-validation. It prevents race conditions by ensuring that the state hasn't changed between validation and execution.

**Purpose:** To safely perform a command only if certain preconditions are met.

**Example: The full `CreateFavoriteAsync` Endpoint**

```csharp
// File: APIController.cs

[HttpPost("api/favorite")]
[Authorize(Roles = "player")]
public async Task<IActionResult> CreateFavoriteAsync([FromBody] FavoriteBody body)
{
    // ... input validation ...
    var nameEscaped = Validator.StringEscape(body.Name);
    string playerId = Validator.StringEscape(Security.PlayerId(User));
    
    // The controller provides two scripts: a validation script and a command script.
    var result = await WishmakerAPI.Wishmaker.PerformChkThenCmdAsync(HttpContext, 
        // 1. Validation Script (runs under a read lock)
        $@"
        {{
            player = company.CustomerByPlayerId('{playerId}').Player;
            favoritesCatalog = player.FavoritesCatalog();
            Check(!favoritesCatalog.ExistsFavoriteNumbers('{nameEscaped}')) Error 'Favorite {nameEscaped} already exists';
        }}", 
        // 2. Command Script (runs under a write lock if validation passes)
        $@"
        {{
            player = company.CustomerByPlayerId('{playerId}').Player;
            favoritesCatalog = player.FavoritesCatalog();
            favoritesCatalog.CreateFavoriteNumbers('{nameEscaped}','{body.IconPath}');
        }}
    ");

    return result;
}
```

**How It Works:**

1.  The `RestAPIActorAsync` wrapper receives the two scripts.
2.  **Step 1 (Check):** It calls `actor.PerformChk()` with the first script. The actor acquires a **read lock**. The `Check` command evaluates its condition. If the condition is false (the favorite *does* exist), an "EWI" (Error) is generated in the output.
3.  The wrapper inspects the output from `PerformChk`.
    *   **If an EWI error exists**, it immediately stops and returns an HTTP 428 (Precondition Failed) response containing the error message. The command script is never executed.
    *   **If no error exists**, it proceeds to Step 2.
4.  **Step 2 (Act):** It calls `actor.PerformCmdAsync()` with the second script. The actor acquires a **write lock** and executes the command as described in section 3.2.

This two-phase process guarantees that the check for the favorite's existence and its creation happen as an atomic-like operation from the user's perspective.

---

## 4. Summary of Benefits

This Actor-based architecture provides several key advantages:

1.  **Thread Safety by Design:** State is confined to an Actor, and all access is managed through its internal lock. Developers don't need to reason about manual locking in the business logic.
2.  **Clear Separation of Concerns:**
    *   **Controllers** handle HTTP concerns.
    *   **Actors** handle state and concurrency.
    *   **[Puppet] Objects** handle pure business logic.
3.  **Testability:** DSL scripts can be executed directly against an actor in unit tests, completely bypassing the web stack.
4.  **Durability:** The model is built for event sourcing. Every state-changing command can be persisted, allowing the actor's state to be perfectly rebuilt from its history.
5.  **Simplified Controller Logic:** The controller code becomes very clean, consisting mostly of input validation and delegating to the Actor wrapper. All complex business logic and error handling are managed elsewhere.