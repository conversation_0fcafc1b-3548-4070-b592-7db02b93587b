﻿using GamesEngine.Finance;
using GamesEngine.Settings;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using town.connectors.commons;

namespace town.connectors.drivers.fiero
{
    public abstract class Grade : FieroTenantDriver
	{
        public static TokenDriver CashierToken { get; set; }

        private RestClient _postClient;
        public string CashierUrl { get; private set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public Grade(string currencyCode) : base(Tenant_Actions.Grade, TransactionType.PayPrize, currencyCode) { }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet) 
        {

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            if (_postClient == null)
            {
                _postClient = new RestClient(CashierUrl);
            }

            var result = PayFragments(now, recordSet);
			return (T)Convert.ChangeType(result, typeof(T));
		}

        public override void Prepare(DateTime now)
        {
			CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("concept");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("processorId");
            CustomSettings.AddVariableParameter("processorKey");
			CustomSettings.AddVariableParameter("gradeFreeFormWagers");

          
            //CustomSettings.Prepare();
            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;

        }

		public GradeFreeFormWagersResponse PayFragments(DateTime now, CustomSettings.RecordSet recordSet)
        {
			var who = recordSet.Mappings["who"].AsString;
            var concept = recordSet.Mappings["concept"].AsString;
            var storeId = recordSet.Mappings["storeId"].AsInt;
            var processorId = recordSet.Mappings["processorId"].AsInt;
			var gradeFreeFormWagers = recordSet.Mappings["gradeFreeFormWagers"].As<List<PayFragmentsWithAtAddressMessage>>();
            var processorKey = recordSet.Mappings["processorKey"].AsString;

            var payBody = new PayBody()
            {
                StoreId = storeId,
                Concept = concept,
                Who = who,
                GradeFreeFormWagers = gradeFreeFormWagers,
                ProcessorKey = processorKey,
                ProcessorId = processorId,

            };

            var result = Pay(payBody);
			return result;
        }

		//const int AverageScriptLengthForOneCommand = 1000;
		//const int MAX_TAMANO_DE_UN_LEXEMA = 1024 * 64;
        //		public GradeFreeFormWagersResponse Pay1(RestAPISpawnerActor actor, string who, FragmentPaymentMessages payFragments, List<PayFragmentsMessage> gradeFreeFormWagers, string processorKey)
        //	{
        //         var result = actor.PerformQry(MovementStorage.ATADDRESS_GENERAL, $@"
        //	{{
        //                 processorAccount = guardian.Accounts().SearchByProcessor('{processorKey}');
        //                 print processorAccount.Id processorAccountId;
        //             }}");
        //         if (!(result is OkObjectResult)) throw new Exception("Processor account not found");

        //         OkObjectResult o = (OkObjectResult)result;
        //         string json = o.Value.ToString();
        //         var processorAccountInfo = JsonConvert.DeserializeObject<ProcessorAccountInfo>(json);

        //         var sortedWagers = gradeFreeFormWagers.OrderBy(x => payFragments.GetAtAddressForThisAuthorizationNumber(x.TicketNumber)).ThenBy(x => x.TicketNumber).ToArray();
        //int wagerIndex = 0;

        //         GradeFreeFormWagersResponse finalResponse = new GradeFreeFormWagersResponse();
        //var buffer = MovementsBuffers.GetOrCreateBuffer(true);
        //var storeId = payFragments.StoreId;
        //var processorId = payFragments.ProcessorId == 0 ? processorAccountInfo.processorAccountId : payFragments.ProcessorId;

        //StringBuilder addressScript = new StringBuilder();
        //StringBuilder changesPerAuthorization = new StringBuilder();
        //StringBuilder wagersWinners = new StringBuilder();
        //StringBuilder wagersLosers = new StringBuilder();
        //StringBuilder refunds = new StringBuilder();
        //StringBuilder wagersAdjustments = new StringBuilder();
        //StringBuilder initialFragmentStatus = new StringBuilder();
        //while (wagerIndex < sortedWagers.Length)
        //{
        //	string currAtAddress = payFragments.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].TicketNumber);
        //	if (buffer.RequiresToChangeTopic(currAtAddress))
        //	{
        //		if (!buffer.IsEmpty()) buffer.Flush();
        //		buffer.UpdateTopicName(currAtAddress);
        //	}

        //	while (wagerIndex < sortedWagers.Length && payFragments.GetAtAddressForThisAuthorizationNumber(sortedWagers[wagerIndex].TicketNumber) == currAtAddress)
        //	{
        //		string currTicketNumber = sortedWagers[wagerIndex].TicketNumber;

        //		wagersWinners.Append('{');
        //		wagersLosers.Append('{');
        //		refunds.Append('{');

        //		int emptyLengthWagers = wagersWinners.Length;

        //		while (wagerIndex < sortedWagers.Length && sortedWagers[wagerIndex].TicketNumber == currTicketNumber)
        //		{
        //			var wager = sortedWagers[wagerIndex];
        //			wagerIndex++;

        //			WagerStatus outcome;
        //			Enum.TryParse(wager.Outcome, out outcome);

        //			switch (outcome)
        //			{
        //				case WagerStatus.L:
        //					wagersLosers.Append(wager.WagerNumber);
        //					wagersLosers.Append(',');
        //					break;
        //				case WagerStatus.W:
        //					wagersWinners.Append(wager.WagerNumber);
        //					wagersWinners.Append(',');
        //					break;
        //				case WagerStatus.X:
        //					refunds.Append(wager.WagerNumber);
        //					refunds.Append(',');
        //					break;
        //				case WagerStatus.D:
        //					refunds.Append(wager.WagerNumber);
        //					refunds.Append(',');
        //					break;
        //				default:
        //					ErrorsSender.Send(
        //						$@"Wager with unknown Outcome:{wager.Outcome}",
        //						$@"TicketNumber:{wager.TicketNumber} \n
        //									WagerNumber:{wager.WagerNumber} \n
        //									DailyFigureDate_YYYYMMDD:{wager.DailyFigureDate_YYYYMMDD} \n
        //									IsValidTicketNumber:{wager.IsValidTicketNumber} \n
        //									AdjustedLossAmount:{wager.AdjustedLossAmount} \n
        //									AdjustedWinAmount:{wager.AdjustedWinAmount}");
        //					break;
        //			}

        //			decimal AdjustedWinAmount = (string.IsNullOrEmpty(wager.AdjustedWinAmount)) ? 0 : decimal.Parse(wager.AdjustedWinAmount);
        //			decimal AdjustedLossAmount = (string.IsNullOrEmpty(wager.AdjustedLossAmount)) ? 0 : decimal.Parse(wager.AdjustedLossAmount);
        //			bool hasAdjustments = AdjustedWinAmount != 0 || AdjustedLossAmount != 0;
        //			if (hasAdjustments)
        //			{
        //				wagersAdjustments.Append(".PrepareAdjustments(authorization, ").Append(wager.WagerNumber).Append(',').Append(AdjustedWinAmount).Append(',').Append(AdjustedLossAmount).Append(')');
        //			}
        //		}

        //		if (wagersWinners.Length > 1) wagersWinners.Length = wagersWinners.Length - 1;
        //		if (wagersLosers.Length > 1) wagersLosers.Length = wagersLosers.Length - 1;
        //		if (refunds.Length > 1) refunds.Length = refunds.Length - 1;

        //		wagersWinners.Append('}');
        //		wagersLosers.Append('}');
        //		refunds.Append('}');

        //		if (wagersLosers.Length > 2)
        //		{
        //			initialFragmentStatus.Append("Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersLosers).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersLosers).Append(", initialStatus);");
        //			changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersLosers).Append(", Now, store, '").Append(payFragments.Concept).Append("', ").Append(FragmentReason.Loser).Append(',').Append(processorId).Append(')');
        //		}
        //		if (wagersWinners.Length > 2)
        //		{
        //			initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(wagersWinners).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(wagersWinners).Append(", initialStatus);");
        //			changesPerAuthorization.Append($".PreparePayments(itIsThePresent, authorization, ").Append(wagersWinners).Append(",  Now, store, '").Append(payFragments.Concept).Append("', ").Append(FragmentReason.Winner).Append(',').Append(processorId).Append(')');
        //		}
        //		if (refunds.Length > 2)
        //		{
        //			initialFragmentStatus.Append($"Eval('initialStatus = ' + authorization.FragmentsStatus(").Append(refunds).Append(") + ';'); authorization.ChangeInitialFragmentsStatus(").Append(refunds).Append(", initialStatus);");
        //			changesPerAuthorization.Append($".PrepareRefunds(itIsThePresent, authorization,").Append(refunds).Append(", Now, store, '").Append(payFragments.Concept).Append("', ").Append(FragmentReason.NoAction).Append(',').Append(processorId).Append(')');
        //		}
        //		if (wagersAdjustments.Length > 1) changesPerAuthorization.Append(wagersAdjustments.ToString());

        //		addressScript.Append("authorization = atAddress.GetAuthorization(").Append(currTicketNumber).Append(");")
        //			.Append(initialFragmentStatus)
        //			.Append("{ store = company.Sales.StoreById(").Append(storeId).Append(");")
        //			.Append("ataddress").Append(changesPerAuthorization).Append(".ApplyChanges(").Append(buffer.Id).Append(", itIsThePresent, '").Append(who).Append("'); }");

        //		changesPerAuthorization.Clear();
        //		wagersWinners.Clear();
        //		wagersLosers.Clear();
        //		refunds.Clear();
        //		wagersAdjustments.Clear();
        //		initialFragmentStatus.Clear();

        //		if (addressScript.Length + AverageScriptLengthForOneCommand > MAX_TAMANO_DE_UN_LEXEMA)
        //		{
        //			actor.PerformCmd(currAtAddress, addressScript.ToString());
        //			addressScript.Clear();
        //		}
        //	}
        //	if (addressScript.Length > 0)
        //	{
        //		actor.PerformCmd(currAtAddress, addressScript.ToString());
        //		addressScript.Clear();
        //	}

        //TODO cris preguntarle a alvaro como hacer el print ya que se necesitan las authorizaciones.
        //		GradeFreeFormWagersResponse mock = new GradeFreeFormWagersResponse();
        //		finalResponse.Merge(mock);
        //	}

        //	MovementsBuffers.Flush(buffer);
        //	return finalResponse;
        //}


        
        private GradeFreeFormWagersResponse Pay(PayBody frgamentBody)
        {
            GradeFreeFormWagersResponse payResponse;
            try
            {
                var url = $"api/pay";
                var jsonString = Commons.ToJson(frgamentBody);

                var request = new RestRequest(url, Method.Post);
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                }
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", jsonString, ParameterType.RequestBody);
                string responseString = "";
                var resultFromCashier = _postClient.Execute(request);
                responseString = resultFromCashier.Content;
                string body = string.Empty;
                if (resultFromCashier.StatusCode == HttpStatusCode.OK)
                {
                    payResponse =  JsonConvert.DeserializeObject<GradeFreeFormWagersResponse>(responseString);
                   
                }
                else
                {
                    payResponse = new GradeFreeFormWagersResponse();
                    payResponse.Error = new ErrorResponse() { Code = resultFromCashier.StatusCode.ToString(), Message = responseString};
                    ErrorsSender.Send(body, $@"Pay fragment {responseString} fails.");
                }

            }
            catch (Exception e)
            {
                payResponse = new GradeFreeFormWagersResponse();
                payResponse.Error = new ErrorResponse() { Code = String.Empty, Message = e.ToString() };
                ErrorsSender.Send(e);
               
            }

            return payResponse;
        }


        public struct ProcessorAccountInfo
        {
            public int processorAccountId { get; set; }
        }
    }
}
