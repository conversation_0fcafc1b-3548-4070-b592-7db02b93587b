﻿using GamesEngine.Business;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.Preferences.Lotto;
using GamesEngine.Time;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static GamesEngine.Exchange.RiskProfilesLotteries;
using static GamesEngine.Games.Lotto.Disincentives;
using TicketType = GamesEngine.Gameboards.Lotto.TicketType;

namespace GamesEngine.Games.Lotto
{
    [Puppet]
    class ToWinAccumulator:Objeto
    {
		readonly ToWinPerLotteries toWins = new ToWinPerLotteries();

		internal ToWin GetOrCreateCurrentToWinFor(Lottery lottery, DateTime drawDate, long subticketNumber, int pickLength)
		{
			if (lottery == null) throw new ArgumentNullException(nameof(lottery));
			if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
			if (subticketNumber < 0) throw new GameEngineException($"Invalid pick number {subticketNumber}");
			if (pickLength < 2 && pickLength > 5) throw new GameEngineException("This is not valid pick");

			var toWinPerDrawDates = toWins.GetOrCreateToWin(lottery, drawDate, subticketNumber, pickLength);
			var toWinPerSubtickets = toWinPerDrawDates.GetOrCreateToWin(drawDate, subticketNumber, pickLength);
			var toWin = toWinPerSubtickets.GetOrCreateToWin(subticketNumber, pickLength);

			return toWin;
		}

		class ToWinPerLotteries
		{
			readonly Dictionary<Lottery, ToWinPerDrawDates> toWins = new Dictionary<Lottery, ToWinPerDrawDates>();

			public ToWinPerLotteries()
            {
			}

			internal ToWinPerDrawDates GetOrCreateToWin(Lottery lottery, DateTime drawDate, long subticketNumber, int pickLength)
			{
				var toWinPerDrawDate = toWins.GetValueOrDefault(lottery);
				if (toWinPerDrawDate == null)
				{
					toWinPerDrawDate = new ToWinPerDrawDates(lottery);
					toWins.Add(lottery, toWinPerDrawDate);
					toWinPerDrawDate.GetOrCreateToWin(drawDate, subticketNumber, pickLength);
				}
				return toWinPerDrawDate;
			}
		}

		class ToWinPerDrawDates
		{
			readonly Dictionary<DateTime, ToWinPerSubtickets> toWins = new Dictionary<DateTime, ToWinPerSubtickets>();
			Lottery Lottery { get; }

			public ToWinPerDrawDates(Lottery lottery)
            {
				Lottery = lottery;
			}
			internal ToWinPerSubtickets GetOrCreateToWin(DateTime drawDate, long subticketNumber, int pickLength)
			{
				var toWinPerSubticket = toWins.GetValueOrDefault(drawDate);
				if (toWinPerSubticket == null)
				{
					toWinPerSubticket = new ToWinPerSubtickets(Lottery, drawDate);
					toWins.Add(drawDate, toWinPerSubticket);
					toWinPerSubticket.GetOrCreateToWin(subticketNumber, pickLength);
				}
				return toWinPerSubticket;
			}
		}
	}

	class ToWinPerSubtickets
	{
		Dictionary<long, ToWin> toWins = new Dictionary<long, ToWin>();
		readonly Lottery lottery;
		readonly DateTime drawDate;
		private decimal accumulatedToWin;
		internal decimal AccumulatedToWin
		{
			get
			{
				return accumulatedToWin;
			}
		}

		internal IEnumerable<ToWin> ToWins
		{
			get
			{
				return toWins.Values.OrderByDescending(risk => risk.Amount).ToList();
			}
		}

		internal ToWinPerSubtickets(Lottery lottery, DateTime drawDate)
		{
			this.lottery = lottery;
			this.drawDate = drawDate;
		}

		internal ToWin GetOrCreateToWin(long subticketNumber, int pickLength)
		{
			var toWin = toWins.GetValueOrDefault(subticketNumber);
			if (toWin == null)
			{
				toWin = new ToWin(subticketNumber, 0, pickLength);
				toWins.Add(subticketNumber, toWin);
			}
			return toWin;
		}

		internal void Add(Ticket ticket, DateTime now, Action<DateTime, SubTicket<IPick>, decimal, DateTime> sendNotification)
		{
			var ticketType = ticket.IdOfType();
			IEnumerable<SubTicket<IPick>> subticketsToAccumulate = Enumerable.Empty<SubTicket<IPick>>();
			if (ticketType == TicketType.P2S || ticketType == TicketType.P3S || ticketType == TicketType.P4S || ticketType == TicketType.P5S || ticketType == TicketType.K10 || ticketType == TicketType.K12) 
			{
				foreach (var wager in ticket.Wagers)
				{
					var toWin = wager.ToWin;
					foreach (var subticket in wager.Subtickets)
						subticketsToAccumulate = subticketsToAccumulate.Append(subticket);
				}
			}
			else if (ticketType == TicketType.P2B || ticketType == TicketType.P3B || ticketType == TicketType.P4B || ticketType == TicketType.P5B)
			{
				foreach (var subticket in ticket.Permute())
				{
					subticketsToAccumulate = subticketsToAccumulate.Append(subticket);
				}
			}
			else throw new GameEngineException($"{nameof(ticketType)} {ticketType} is not valid to accumulate risk");

            foreach (var subticket in subticketsToAccumulate)
			{
				var prize = CalculatePrizeToWin(subticket, ticket.BetAmount(), ticketType, ticket.Prizes);
                if (ticket.BelongsToFireBallDraw) prize /= 2;

                var subticketNumber = subticket.ToInt64();
				var toWinPerSubticket = toWins.GetValueOrDefault(subticketNumber);
				if (toWinPerSubticket == null)
				{
                    toWinPerSubticket = new ToWin(subticketNumber, prize, subticket.Length);
                    toWins.Add(subticketNumber, toWinPerSubticket);
				}
				else
				{
					toWinPerSubticket.AddRisk(prize);
				}
                toWinPerSubticket.AddPurchase();
                accumulatedToWin += prize;
                sendNotification(now, subticket, toWinPerSubticket.Amount, ticket.DrawDate);
            }
		}

        internal decimal AccumulatedToWinForNumber(SubTicket<IPick> subticket)
        {
			var number = subticket.ToInt64(); 
            var toWin = toWins.GetValueOrDefault(number);
			var result = toWin != null ? toWin.Amount : 0m;
			return result;
		}

		internal bool IsSubticketExceedingToWin(Schedule schedule, PrizeCriteriaIds prizeCriteriaId, RuleType ruleType, SubTicket<IPick> subticket, Domain domain)
		{
			var lotteryGame = lottery.Company.LotteryGamesPool.GetLotteryGame(lottery);
            var riskProfile = (RiskProfileLotteries)lotteryGame.RiskProfiles.GetRiskProfile(domain);
			var risks = riskProfile.Risks;
			var maxToWin = risks.Risk.GetMaximumToWin(domain, subticket.Length);
			var number = subticket.ToInt64();
			var toWin = toWins.GetValueOrDefault(number);
			var accumulatedToWinForSubticket = toWin != null ? toWin.Amount : 0m;
            var disincentivatedRisk = risks.Disincentives.ApplyTo(schedule, prizeCriteriaId, ruleType, subticket.Length, maxToWin);
			return accumulatedToWinForSubticket >= disincentivatedRisk;
		}

		decimal CalculatePrizeToWin(SubTicket<IPick> subticket, decimal betAmount, TicketType ticketType, Prizes prizes)
        {
			var prizeCriteria = prizes.WayOfSubticket(ticketType, subticket);
			decimal prize = prizes.Prize(ticketType, prizeCriteria);
			prize *= betAmount;
			return prize;
		}

		internal IEnumerable<SubTicket<IPick>> SubticketsExceedingToWin(Schedule schedule, int pickNumber, DateTime drawDate, IEnumerable<SubTicket<IPick>> subticketsToCheck, decimal betAmount, TicketType ticketType, Domain domain, ToWinAccumulator toWinAccumulator, bool withFireball, bool applyToleranceFactor)
		{
			var subticketsExceedingToWin = new List<SubTicket<IPick>>();
			var riskProfile = (RiskProfileLotteries)lottery.PicksLotteryGame.RiskProfiles.GetRiskProfile(domain);
            var maxToWin = riskProfile.Risks.Risk.GetMaximumToWin(domain, pickNumber);
            var prizes = (PrizesPicks)riskProfile.Prizes;

            foreach (var subticket in subticketsToCheck)
			{
				var number = subticket.ToInt64();
				var toWin = toWins.GetValueOrDefault(number);
				var accumulatedToWinForSubticket = toWin != null ? toWin.Amount : 0m;
                
                var prizeForCurrentSubticket = CalculatePrizeToWin(subticket, betAmount, ticketType, prizes);
                if (lottery.RootLottery is LotteryTriz lotteryTriz) prizeForCurrentSubticket += CalculateAccumulatedToWinInSubticketsByEnding(subticket, drawDate, lotteryTriz, riskProfile);
                if (withFireball) prizeForCurrentSubticket /= 2;
                if (applyToleranceFactor) prizeForCurrentSubticket *= riskProfile.Risks.Risk.RiskToleranceFactor;

                var accumulatedToWinInPurchase = toWinAccumulator.GetOrCreateCurrentToWinFor(lottery, drawDate, number, subticket.Length);
                var prizeCriteriaId = Disincentives.WayOfSubticket(ticketType, subticket);
                var ruleType = RiskPerLottery.RuleTypeFromTicketType(ticketType);
                var disincentivatedRisk = riskProfile.Risks.Disincentives.ApplyTo(schedule, prizeCriteriaId, ruleType, subticket.Length, maxToWin);
				if ((accumulatedToWinForSubticket + prizeForCurrentSubticket + accumulatedToWinInPurchase.Amount) > disincentivatedRisk)
				{
					subticketsExceedingToWin.Add(subticket);
				}
				accumulatedToWinInPurchase.AddRisk(prizeForCurrentSubticket);
				accumulatedToWinInPurchase.AddPurchase();
			}
			return subticketsExceedingToWin;
		}

        private decimal CalculateAccumulatedToWinInSubticketsByEnding(SubTicket<IPick> subticket, DateTime drawDate, LotteryTriz lotteryTriz, RiskProfileLotteries riskProfile)
        {
            var subticketsByEnding = SubTicket<IPick>.GenerateSubticketsByEnding(subticket.Number);
            var accumulatedToWin = 0m;
            foreach (var subticketByEnding in subticketsByEnding)
            {
                var internalLottery = lotteryTriz.GetLotteryPick(subticketByEnding.Length);
                var riskPerLottery = riskProfile.Risks.Risk.GetRiskPerLottery(subticketByEnding.Length, internalLottery);
                accumulatedToWin += riskPerLottery.AccumulatedToWinForNumber(subticketByEnding, drawDate);
            }
            return accumulatedToWin;
        }

        internal void CloneFrom(ToWinPerSubtickets toWinPerSubtickets)
		{
            if (toWinPerSubtickets == null) throw new ArgumentNullException(nameof(toWinPerSubtickets));

            toWins.Clear();
            foreach (var toWinPair in toWinPerSubtickets.toWins)
			{
				var newToWin = new ToWin(toWinPair.Key, toWinPair.Value.Amount, toWinPair.Value.PickLength);
                toWins.Add(toWinPair.Key, newToWin);
            }
            accumulatedToWin = toWinPerSubtickets.accumulatedToWin;
        }
	}

	class ToWin : Objeto
	{
		internal string Number
		{
			get
			{
				return this.number.ToString().PadLeft(this.PickLength, '0');
			}
		}

		internal decimal Amount
		{
			get; private set;
		}

		internal int PurchasesCount { get; private set; }

		internal int PickLength { get; }

		private readonly long number;

		internal ToWin(long number, decimal amount, int pickLength)
		{
			if (pickLength < 2 && pickLength > 5) throw new GameEngineException("This is not valid pick");
			this.number = number;
			Amount = amount;
			PickLength = pickLength;
		}

		internal void AddRisk(decimal amount)
		{
			Amount += amount;
		}

		internal void AddPurchase()
		{
			PurchasesCount++;
		}
	}
}
