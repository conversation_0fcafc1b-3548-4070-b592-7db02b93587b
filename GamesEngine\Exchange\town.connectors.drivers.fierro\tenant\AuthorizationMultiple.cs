﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{   
    public abstract class AuthorizationMultiple : FieroTenantDriver, IDriverUserProperties
    {
		public const int FAKE_TICKET_NUMBER = -1;
		private const float VERSION = 1.0F;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public string CashierUrl { get; private set; }

        public override string Description => $"Fiero {nameof(AuthorizationMultiple)} driver {VERSION}";

		public AuthorizationMultiple(string currencyCode)
			: base(Tenant_Actions.AuthorizationInternal, TransactionType.Authorization, currencyCode)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = Authorization.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Authorization.CashierToken.access_token, now);

            bool needToChangeToken = (Authorization.CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) Authorization.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            var result = await AuthorzationAsync(now, recordSet);
            if (result == null) throw new Exception($"Result is null. Driver {nameof(AuthorizationMultiple)} failed to execute.");
            return (T)Convert.ChangeType(result, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
		{
            CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("purchaseTotal");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("reference");
            CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("fragmentInformation");
			CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("useless");

            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }
        private async Task<AuthorizationTransaction> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var StoreId = recordSet.Mappings["storeId"];
            var PurchaseTotalValue = recordSet.Mappings["purchaseTotal"];
            var Concept = recordSet.Mappings["description"];
            var ReferenceNumber = recordSet.Mappings["reference"];
            string accountNumber = recordSet.Mappings["accountNumber"].AsString;
            var FragmentInformation = recordSet.Mappings["fragmentInformation"].As<FragmentInformation>();
            var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            var Useless = recordSet.Mappings["useless"];

            string atAddress = recordSet.Mappings["accountNumber"].AsString;
            decimal purchaseTotal = PurchaseTotalValue.AsDecimal;

            var lockBalanceData = new ExternalMultiLockBalanceData();
            lockBalanceData.AtAddress = atAddress;
            lockBalanceData.AccountNumber = accountNumber;
            lockBalanceData.PurchaseTotal = purchaseTotal;
            lockBalanceData.CurrencyCode = currencyCode;
            lockBalanceData.StoreId = StoreId.AsInt;
            lockBalanceData.Concept = Concept.AsString;
            if (recordSet.ContainsKeyName("fragmentInformation"))
                lockBalanceData.FragmentInformation = FragmentInformation;
            if (recordSet.ContainsKeyName("referenceNumber"))
                lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            lockBalanceData.ToWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ToWinByDrawAndNumber>>();

            AuthorizationTransaction auth = null;
            try
            {
                var url = $"{CashierUrl}api/customers/{atAddress}/balance/externalMultiLock";

                HttpClient client = new HttpClient();

                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured()) client.DefaultRequestHeaders.Add("Authorization", $"Bearer {Authorization.CashierToken.access_token}");

                var jsonString = JsonConvert.SerializeObject(lockBalanceData);
                var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");
                HttpResponseMessage resultFromCashier = await client.PostAsync(url, httpContent);

                int authorizationId = FAKE_TICKET_NUMBER;
                string responseContent = await resultFromCashier.Content.ReadAsStringAsync();
                if (!resultFromCashier.IsSuccessStatusCode)
                {
                    switch (resultFromCashier.StatusCode)
                    {
                        case System.Net.HttpStatusCode.BadRequest:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.AuthorizationFail, url, responseContent);
                            break;
                        case System.Net.HttpStatusCode.UnprocessableContent:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.InsufficientFunds, url, responseContent);
                            break;
                        default:
                            auth = new AuthorizationTransaction(FAKE_TICKET_NUMBER, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.UnexpectedFormat, url, responseContent);
                            break;
                    }
                }
                else
                {
                    if (!int.TryParse(responseContent, out authorizationId)) throw new Exception($"Failed to parse authorizationId from response: {responseContent} for atAddress: {atAddress}");
                    auth = new AuthorizationTransaction(authorizationId, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes, AuthorizationResponseCode.OK, url, responseContent);
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }

            return auth;
        }

	}
}
