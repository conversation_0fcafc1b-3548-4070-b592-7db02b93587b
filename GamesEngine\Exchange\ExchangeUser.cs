﻿using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.MessageQueuing;
using GamesEngine.Domains;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace GamesEngine.Exchange
{
	[Puppet]
	internal abstract class ExchangeUser : Objeto
	{
		private readonly Marketplace marketplace;
		protected List<Agent> agents;
		
		protected const char USER_SEPARATOR = '/';


        internal ExchangeUser(Marketplace marketplace)
		{
			this.marketplace = marketplace;
			stores = new Stores(marketplace.Company);
		}

		internal ExchangeUser AddAgent(string name)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
			if (!Validator.AreAllLettersOrDigitsOrSpaces(name)) throw new GameEngineException($"Name '{name}' has invalid characters. Only letters, digits and spaces are allowed.");

			name = name.Trim();
			Agent agent = new Agent(this, name);
			if (agents == null) agents = new List<Agent>();
			if (agents.Contains(agent)) throw new GameEngineException($"Agent {agent.Name} already exist in agency {Name}");
			agents.Add(agent);

			return agent;
		}

		internal bool HasAgent(string name)
		{
			if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

			if (agents == null) agents = new List<Agent>();
			var exist = agents.Exists(x => x.Name == name);
			return exist;
		}

		internal List<Agent> SearchUsers()
		{
			List<Agent> result = new List<Agent>();
			result.AddRange(agents);
			return result;
		}

		internal ExchangeUser SearchAgent(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) throw new ArgumentNullException(nameof(fullName));
			if (agents == null) throw new GameEngineException($"Agent {fullName} is unknown");

			string agentName;
			int indexSeparator = fullName.IndexOf(USER_SEPARATOR);
			if (indexSeparator < 0)
			{
				agentName = fullName;
				foreach (Agent user in agents)
				{
					if (user.Name == agentName)
					{
						return user;
					}
				}

				throw new GameEngineException($"Agent {fullName} is unknown");
			}

			agentName = fullName.Substring(0, indexSeparator);
			string tail = fullName.Substring(indexSeparator + 1);

			foreach (Agent user in agents)
			{
				if (user.Name == agentName)
				{
					return user.SearchAgent(tail);
				}
			}

			throw new GameEngineException($"Agent {fullName} is unknown");
		}

		internal bool ExistsAgent(string fullName)
		{
			if (string.IsNullOrWhiteSpace(fullName)) return false;
			if (agents == null) return false;

			string agentName;
			int indexSeparator = fullName.IndexOf(USER_SEPARATOR);
			if (indexSeparator < 0)
			{
				agentName = fullName;
				foreach (Agent user in agents)
				{
					if (user.Name == agentName)
					{
						return true;
					}
				}

				return false;
			}

			agentName = fullName.Substring(0, indexSeparator);
			string tail = fullName.Substring(indexSeparator + 1);

			foreach (Agent user in agents)
			{
				if (user.Name == agentName)
				{
					return user.ExistsAgent(tail);
				}
			}

			return false;
		}

		private void AllAgents(List<ExchangeUser> currentAgents)
		{
			if (agents == null) agents = new List<Agent>();
			foreach (var node in agents)
			{
				currentAgents.Add(node);
			}
		}

		internal IEnumerable<ExchangeUser> AllAgents()
		{
			List<ExchangeUser> currentAgents = new List<ExchangeUser>();
			foreach (Agent node in agents)
			{
				currentAgents.Add(node);
				node.AllAgents(currentAgents);
			}

			return currentAgents;
		}

		internal bool IsInternalAgent
		{
			get
			{
				if (agents == null) agents = new List<Agent>();
				return agents.Count > 0;
			}
		}

		internal bool IsFinalAgent
		{
			get
			{
				if (agents == null) agents = new List<Agent>();
				return agents.Count == 0;
			}
		}

		internal abstract string FullName { get; }

		internal Marketplace Marketplace
		{
			get
			{
				return this.marketplace;
			}
		}

		internal string Name { set; get; }

		internal bool HasChildrenWithProfile => UserProfileCounts != null;
		
		internal void AssignProfile(User user, Profile profile)
		{
			if (user == null) throw new ArgumentNullException(nameof(user));
			if (profile == null) throw new ArgumentNullException(nameof(profile));

			if (UserProfileCounts == null) UserProfileCounts = new UserProfileCounter();

			user.Profile = profile;
			UserProfileCounts.Add(profile);
		}

		internal void ReassignProfile(User user, Profile profile)
		{
			if (user == null) throw new ArgumentNullException(nameof(user));
			if (profile == null) throw new ArgumentNullException(nameof(profile));
			if (!user.HasProfile) throw new GameEngineException($"{nameof(user)} has not profile. Use {nameof(AssignProfile)} first");
			if (user.Profile == profile) throw new GameEngineException($"{nameof(profile)} must be different than previous one");

			UserProfileCounts.Remove(user.Profile);
			user.Profile = profile;
			UserProfileCounts.Add(profile);
		}

		internal UserProfileCounter UserProfileCounts { get; private set; }
		internal class UserProfileCounter : Objeto
		{
			Dictionary<Profile, int> profileCounts = new Dictionary<Profile, int>();
			IEnumerable<Profile> Profiles
			{
				get
				{
					return profileCounts.Keys.ToList();
				}
			}

			internal int Count(Profile profile)
			{
				if (profile == null) throw new ArgumentNullException(nameof(profile));
				if (!profileCounts.ContainsKey(profile)) return 0;

				return profileCounts[profile];
			}

			internal void Add(Profile profile)
			{
				if (profile == null) throw new ArgumentNullException(nameof(profile));

				if (profileCounts.TryGetValue(profile, out int count))
				{
					profileCounts[profile] = count + 1;
				}
				else
				{
					profileCounts.Add(profile, 1);
				}
			}

			internal void Remove(Profile profile)
			{
				if (profile == null) throw new ArgumentNullException(nameof(profile));

				if (profileCounts.TryGetValue(profile, out int count))
				{
					profileCounts[profile] = count - 1;
					if (count == 1) profileCounts.Remove(profile);
				}
				else
				{
					throw new GameEngineException($"{nameof(profile)} {profile.Name} was not added previously.");
				}
			}
		}

		Stores stores;
		internal IEnumerable<Domain> VisiblesDomains => stores.VisiblesDomains.ToList();

		internal Store SearchStore(int storeId)
		{
			return (Store)stores.Find(storeId);
		}

		internal bool ContainsStore(int storeId)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");

			return stores.Exists(storeId);
		}

		internal bool ContainsDomain(int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!ContainsStore(storeId)) return false;

			var store = SearchStore(storeId);
			var result = store.ExistsDomain(domain.Url);
			return result;
		}

		internal void Assign(int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!ContainsStore(storeId)) throw new GameEngineException($"{nameof(storeId)} '{storeId}' does not exist");

			var store = SearchStore(storeId);
			store.Add(domain);
			store.DisableDomain(domain);
		}

        internal void Assign(int storeId, string storeName, Domain domain)
        {
            if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(storeName)) throw new ArgumentNullException(nameof(storeName));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (ContainsStore(storeId)) throw new GameEngineException($"{nameof(storeId)} '{storeId}' already exist");
            var store = stores.Add(storeId, storeName);
            store.Add(domain);
            store.DisableDomain(domain);
        }

        internal void AssignToCurrentStore(Domain domain)
		{
			var currentStore = marketplace.Company.Sales.CurrentStore;
			if (!ContainsStore(currentStore.Id))
			{
				stores.Add(currentStore.Id, currentStore.Name);
			}
			Assign(currentStore.Id, domain);
		}

		internal void EnableDomain(int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!ContainsStore(storeId)) throw new GameEngineException($"{nameof(storeId)} '{storeId}' does not exist");

			var store = SearchStore(storeId);
			store.EnableDomain(domain);
		}

		internal void EnableDomain(bool itIsThePresent, Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var currentStore = marketplace.Company.Sales.CurrentStore;
			EnableDomain(itIsThePresent, currentStore.Id, domain);
		}

		internal void EnableDomain(bool itIsThePresent, int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			EnableDomain(storeId, domain);
			if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
			{
				var catalogMsg = new AgentStoreOnOffMessage(Name, FullName, domain.Url, storeId, true);
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, catalogMsg);
			}
		}

		internal void DisableDomain(int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!ContainsStore(storeId)) throw new GameEngineException($"{nameof(storeId)} '{storeId}' does not exist");

			var store = SearchStore(storeId);
			store.DisableDomain(domain);
		}

		internal void DisableDomain(bool itIsThePresent, Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var currentStore = marketplace.Company.Sales.CurrentStore;
			DisableDomain(itIsThePresent, currentStore.Id, domain);
		}

		internal void DisableDomain(bool itIsThePresent, int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			DisableDomain(storeId, domain);
			if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
			{
				var catalogMsg = new AgentStoreOnOffMessage(Name, FullName, domain.Url, storeId, false);
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCatalog, catalogMsg);
			}
		}

		internal bool IsEnabledInCurrentStore(Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			var currentStore = marketplace.Company.Sales.CurrentStore;
			if (!ContainsStore(currentStore.Id)) return false;

			var store = SearchStore(currentStore.Id);
			var result = store.IsEnabledOn(domain);
			return result;
		}

		internal bool IsEnabledInStore(int storeId, Domain domain)
		{
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} must be greater than 0");
			if (domain == null) throw new ArgumentNullException(nameof(domain));

			if (!ContainsStore(storeId)) return false;

			var store = SearchStore(storeId);
			var result = store.IsEnabledOn(domain);
			return result;
		}
	}
}
